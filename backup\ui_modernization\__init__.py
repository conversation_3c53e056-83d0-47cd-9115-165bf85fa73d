# ui/__init__.py
"""
Pacchetto per i componenti dell'interfaccia utente.
"""

from ui.main_window import PhotoDropApp
from ui.left_panel import LeftPanel
from ui.views.panels.right_panel import RightPanel
from ui.styles import Styles

__all__ = ['PhotoDropApp', 'LeftPanel', 'RightPanel', 'Styles']

# services/__init__.py
"""
Pacchetto per i servizi dell'applicazione.
"""

from services.image_loader import ImageLoader
from core.image_cache import ImageCache, LRUCache
from models.file_renamer import FileRenamer

__all__ = ['ImageLoader', 'ImageCache', 'LRUCache', 'FileRenamer']

# utils/__init__.py
"""
Pacchetto per le funzioni di utilità.
"""

from utils.image_utils import (
    is_valid_image_file, create_thumbnail, load_image, get_image_files_from_folder
)
from utils.exif_utils import get_exif_orientation, apply_exif_orientation
from utils.constants import THUMBNAIL_SIZE, PREVIEW_PADDING, VALID_EXTENSIONS

__all__ = [
    'is_valid_image_file', 'create_thumbnail', 'load_image', 'get_image_files_from_folder',
    'get_exif_orientation', 'apply_exif_orientation',
    'THUMBNAIL_SIZE', 'PREVIEW_PADDING', 'VALID_EXTENSIONS'
]

# models/__init__.py
"""
Pacchetto per i modelli dati dell'applicazione.
"""

from models.image_item import ImageItem

__all__ = ['ImageItem']
