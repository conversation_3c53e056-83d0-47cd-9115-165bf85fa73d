#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
test_topbar.py
Script di test per la nuova topbar moderna
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
from PySide6.QtCore import Qt

from ui.components.topbar import ModernTopbar
from ui.styles import Styles

class TestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Test Topbar Moderna - PhotoDrop")
        self.setGeometry(100, 100, 1200, 800)
        
        # Widget centrale
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Layout
        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        
        # Topbar
        self.topbar = ModernTopbar(self)
        layout.addWidget(self.topbar)
        
        # Placeholder per contenuto
        placeholder = QWidget()
        placeholder.setStyleSheet("background-color: #1a1a1a; color: white;")
        layout.addWidget(placeholder, 1)
        
        # Applica stili
        self.setStyleSheet(Styles.get_combined_style())
        
        # Connetti segnali
        self.topbar.action_triggered.connect(self.on_action)
        self.topbar.navigation_changed.connect(self.on_navigation)
    
    def on_action(self, action):
        print(f"Azione: {action}")
    
    def on_navigation(self, section):
        print(f"Navigazione: {section}")

if __name__ == "__main__":
    app = QApplication(sys.argv)
    
    # Imposta tema scuro
    app.setStyle("Fusion")
    
    window = TestWindow()
    window.show()
    
    sys.exit(app.exec())
