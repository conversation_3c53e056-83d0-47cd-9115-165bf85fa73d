#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Dialogo per il ritaglio delle immagini.
ui/dialogs/crop_dialog.py
"""

import os
from typing import Optional, Tuple
from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QPushButton, 
    QLabel, QMessageBox, QProgressBar
)
from PySide6.QtCore import Qt, Signal, QTimer, QThread, QObject
from PySide6.QtGui import QPixmap

from ui.widgets.crop_widget import CropWidget
from ui.styles import Styles
from core.image_operations import crop_image_file
from utils.logging_config import get_logger

logger = get_logger("PhotoDrop.CropDialog")

class CropWorker(QObject):
    """Worker per eseguire il ritaglio in un thread separato."""
    
    # Segnali
    finished = Signal(bool, str)  # success, message
    progress = Signal(int)  # progress percentage
    
    def __init__(self, image_path: str, x: int, y: int, width: int, height: int):
        super().__init__()
        self.image_path = image_path
        self.x = x
        self.y = y
        self.width = width
        self.height = height
    
    def run(self):
        """Esegue il ritaglio dell'immagine."""
        try:
            logger.info(f"Inizio ritaglio: {os.path.basename(self.image_path)} ({self.x}, {self.y}, {self.width}, {self.height})")
            
            # Simula progresso
            self.progress.emit(25)
            
            # Esegui il ritaglio
            success = crop_image_file(self.image_path, self.x, self.y, self.width, self.height)
            
            self.progress.emit(75)
            
            if success:
                self.progress.emit(100)
                self.finished.emit(True, "Ritaglio completato con successo!")
                logger.info(f"Ritaglio completato: {os.path.basename(self.image_path)}")
            else:
                self.finished.emit(False, "Errore durante il ritaglio dell'immagine.")
                logger.error(f"Errore durante il ritaglio: {self.image_path}")
                
        except Exception as e:
            error_msg = f"Errore imprevisto durante il ritaglio: {str(e)}"
            self.finished.emit(False, error_msg)
            logger.error(f"Errore imprevisto: {e}", exc_info=True)


class CropDialog(QDialog):
    """
    Dialogo per il ritaglio delle immagini.
    Fornisce un'interfaccia completa per selezionare e applicare il ritaglio.
    """
    
    # Segnali
    image_cropped = Signal(str)  # Emesso quando l'immagine è stata ritagliata
    
    def __init__(self, image_path: str, pixmap: QPixmap, parent=None):
        super().__init__(parent)
        
        self.image_path = image_path
        self.original_pixmap = pixmap
        self.crop_worker = None
        self.crop_thread = None
        
        self._setup_ui()
        self._connect_signals()
        
        # Imposta l'immagine nel widget di ritaglio
        self.crop_widget.set_image(image_path, pixmap)
        
        logger.info(f"CropDialog inizializzato per: {os.path.basename(image_path)}")
    
    def _setup_ui(self):
        """Configura l'interfaccia utente del dialogo."""
        self.setWindowTitle("Ritaglio Immagine - PhotoDrop")
        self.setModal(True)
        self.resize(800, 600)
        
        # Layout principale
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # Widget di ritaglio
        self.crop_widget = CropWidget()
        layout.addWidget(self.crop_widget)
        
        # Barra di progresso (inizialmente nascosta)
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setStyleSheet(f"""
            QProgressBar {{
                border: 1px solid {Styles.BORDER_COLOR};
                border-radius: 4px;
                text-align: center;
                background-color: {Styles.DARK_3};
                color: {Styles.TEXT_MAIN};
            }}
            QProgressBar::chunk {{
                background-color: {Styles.ACCENT};
                border-radius: 3px;
            }}
        """)
        layout.addWidget(self.progress_bar)
        
        # Messaggio di stato
        self.status_label = QLabel()
        self.status_label.setVisible(False)
        self.status_label.setStyleSheet(f"""
            QLabel {{
                color: {Styles.TEXT_SECONDARY};
                font-size: 12px;
                padding: 5px;
            }}
        """)
        self.status_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(self.status_label)
        
        # Stile del dialogo
        self.setStyleSheet(f"""
            CropDialog {{
                background-color: {Styles.DARK_1};
                color: {Styles.TEXT_MAIN};
            }}
        """)
    
    def _connect_signals(self):
        """Connette i segnali agli slot."""
        self.crop_widget.crop_requested.connect(self._on_crop_requested)
        self.crop_widget.crop_cancelled.connect(self.reject)
    
    def _on_crop_requested(self, x: int, y: int, width: int, height: int):
        """
        Gestisce la richiesta di ritaglio.
        
        Args:
            x: Coordinata x del ritaglio
            y: Coordinata y del ritaglio
            width: Larghezza del ritaglio
            height: Altezza del ritaglio
        """
        # Conferma dell'utente
        reply = QMessageBox.question(
            self,
            "Conferma Ritaglio",
            f"Vuoi ritagliare l'immagine alla dimensione {width}×{height} px?\n\n"
            f"Questa operazione modificherà permanentemente il file originale.\n"
            f"Assicurati di aver fatto un backup se necessario.",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            self._start_crop_operation(x, y, width, height)
    
    def _start_crop_operation(self, x: int, y: int, width: int, height: int):
        """
        Avvia l'operazione di ritaglio in un thread separato.
        
        Args:
            x: Coordinata x del ritaglio
            y: Coordinata y del ritaglio
            width: Larghezza del ritaglio
            height: Altezza del ritaglio
        """
        # Disabilita i controlli
        self.crop_widget.setEnabled(False)
        
        # Mostra la barra di progresso
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        
        # Mostra il messaggio di stato
        self.status_label.setText("Ritaglio in corso...")
        self.status_label.setVisible(True)
        
        # Crea il worker e il thread
        self.crop_worker = CropWorker(self.image_path, x, y, width, height)
        self.crop_thread = QThread()
        
        # Sposta il worker nel thread
        self.crop_worker.moveToThread(self.crop_thread)
        
        # Connetti i segnali
        self.crop_thread.started.connect(self.crop_worker.run)
        self.crop_worker.progress.connect(self.progress_bar.setValue)
        self.crop_worker.finished.connect(self._on_crop_finished)
        self.crop_worker.finished.connect(self.crop_thread.quit)
        self.crop_worker.finished.connect(self.crop_worker.deleteLater)
        self.crop_thread.finished.connect(self.crop_thread.deleteLater)
        
        # Avvia il thread
        self.crop_thread.start()
        
        logger.info(f"Operazione di ritaglio avviata: ({x}, {y}, {width}, {height})")
    
    def _on_crop_finished(self, success: bool, message: str):
        """
        Gestisce il completamento dell'operazione di ritaglio.
        
        Args:
            success: True se l'operazione è riuscita
            message: Messaggio di risultato
        """
        # Nascondi la barra di progresso
        self.progress_bar.setVisible(False)
        
        # Aggiorna il messaggio di stato
        self.status_label.setText(message)
        
        if success:
            # Emetti il segnale di successo
            self.image_cropped.emit(self.image_path)
            
            # Mostra messaggio di successo
            QMessageBox.information(
                self,
                "Ritaglio Completato",
                "L'immagine è stata ritagliata con successo!"
            )
            
            # Chiudi il dialogo
            QTimer.singleShot(1000, self.accept)
            
        else:
            # Mostra messaggio di errore
            QMessageBox.critical(
                self,
                "Errore Ritaglio",
                f"Si è verificato un errore durante il ritaglio:\n\n{message}"
            )
            
            # Riabilita i controlli
            self.crop_widget.setEnabled(True)
            self.status_label.setVisible(False)
        
        # Pulisci i riferimenti
        self.crop_worker = None
        self.crop_thread = None
    
    def closeEvent(self, event):
        """Gestisce la chiusura del dialogo."""
        # Se c'è un'operazione in corso, chiedi conferma
        if self.crop_thread and self.crop_thread.isRunning():
            reply = QMessageBox.question(
                self,
                "Operazione in Corso",
                "Un'operazione di ritaglio è in corso. Vuoi davvero chiudere?",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )
            
            if reply == QMessageBox.StandardButton.No:
                event.ignore()
                return
            
            # Termina il thread se necessario
            if self.crop_thread:
                self.crop_thread.quit()
                self.crop_thread.wait(3000)  # Aspetta max 3 secondi
        
        event.accept()
        logger.info("CropDialog chiuso")
