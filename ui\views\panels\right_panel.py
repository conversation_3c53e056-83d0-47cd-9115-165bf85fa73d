#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Implementazione del pannello destro dell'applicazione con funzionalità di ritaglio.
Contiene l'anteprima dell'immagine e i controlli di zoom, rotazione e ritaglio.
"""

import os
from typing import Op<PERSON>, Tu<PERSON>, Dict, Any
from pathlib import Path

# Importa il modulo Image da PIL per la gestione delle immagini
from PIL import Image

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QComboBox,
    QSizePolicy, QScrollArea, QFrame, QApplication, QMessageBox, QToolBar
)
from PySide6.QtCore import Qt, Signal, Slot, QPoint, QTimer, QEvent, QRect, QSize
from PySide6.QtGui import (
    QPixmap, QWheelEvent, QMouseEvent, QCursor, QIcon, QTransform, Q<PERSON>mage,
    <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, QColor, QRegion
)

# Impo<PERSON>zioni locali
from models.image_model import ImageModel
from core.image_cache import ImageCache
from core.image_operations import rotate_image_file, get_image_orientation, apply_exif_orientation
from ui.styles import Styles
from utils.constants import PREVIEW_PADDING
from utils.exif_utils import get_exif_data, format_exif_data
from utils.logging_config import get_logger

logger = get_logger("PhotoDrop.RightPanel")

class RightPanel(QWidget):
    """
    Pannello destro che mostra l'anteprima dell'immagine con controlli di zoom, rotazione e ritaglio.
    """
    # Segnali
    fullscreen_toggled = Signal(bool)  # Emesso quando viene attivata/disattivata la modalità a schermo intero
    image_rotated = Signal(str, int)   # Emesso quando un'immagine viene ruotata (path, angolo)
    zoom_changed = Signal(float)       # Emesso quando cambia il livello di zoom (fattore)
    rotation_changed = Signal(int)     # Emesso quando cambia la rotazione (gradi)
    image_loaded = Signal(str)         # Emesso quando viene caricata una nuova immagine (path)
    image_updated = Signal(str)        # Emesso quando un'immagine viene aggiornata (path)
    error_occurred = Signal(str, str)  # Emesso quando si verifica un errore (titolo, messaggio)
    request_prev_image = Signal()      # Emesso quando si richiede di mostrare l'immagine precedente
    request_next_image = Signal()      # Emesso quando si richiede di mostrare l'immagine successiva
    
    def __init__(self, image_model=None, image_cache=None, parent=None):
        """
        Inizializza il pannello destro.
        
        Args:
            image_model: Modello per i dati dell'immagine
            image_cache: Cache per le immagini
            parent: Widget genitore (solitamente MainWindow)
        """
        super().__init__(parent)
        
        # Riferimento alla finestra principale
        self.main_window = parent
        
        # Modello per i dati dell'immagine
        self.image_model = image_model if image_model else ImageModel(image_cache) # Usa l'argomento image_cache
        
        # Cache per le immagini
        self.image_cache = image_cache

        # Lista delle immagini disponibili e indice corrente
        self.image_list = []
        self.current_image_index = -1

        # Flag per consentire/disabilitare la navigazione
        self.navigation_enabled = True

        # Stato interno
        self.current_image_path = None
        self.original_pixmap = None
        self.zoom_factor = 1.0
        self.rotation_angle = 0
        self.panning = False
        self.last_mouse_position = None
        self.pending_scroll_position = None
        self.right_sidebar = None  # Sarà impostato dal main_window
        self.image_just_loaded = False  # Flag per il segnale image_loaded
        self.is_fullscreen = False  # Stato della modalità a schermo intero
        
        # Timer per il panning fluido
        self.pan_timer = QTimer()
        self.pan_timer.setSingleShot(False)
        self.pan_timer.setInterval(10)  # 10 ms = 100 Hz
        self.pending_scroll_position = None
        
        # Inizializza l'interfaccia
        self._setup_ui()
        self._connect_signals()
        
        # Imposta lo stile
        self.setStyleSheet(Styles.RIGHT_PANEL_STYLE)

    def _setup_ui(self):
        """Configura l'interfaccia utente del pannello."""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        
        # Toolbar per i controlli
        toolbar = QToolBar()
        toolbar.setStyleSheet(Styles.ICON_BUTTON_STYLE)
        toolbar.setIconSize(QSize(24, 24))
        toolbar.setMovable(False)
        
        # Aggiungi pulsanti alla toolbar
        prev_button = QPushButton()
        prev_button.setIcon(QIcon("assets/icons/prev_duotone.svg"))
        prev_button.setToolTip("Immagine precedente")
        prev_button.setStyleSheet(Styles.ICON_BUTTON_STYLE)
        prev_button.clicked.connect(self._show_prev_image)
        toolbar.addWidget(prev_button)
        
        next_button = QPushButton()
        next_button.setIcon(QIcon("assets/icons/next_duotone.svg"))
        next_button.setToolTip("Immagine successiva")
        next_button.setStyleSheet(Styles.ICON_BUTTON_STYLE)
        next_button.clicked.connect(self._show_next_image)
        toolbar.addWidget(next_button)
        
        toolbar.addSeparator()
        
        zoom_in_button = QPushButton()
        zoom_in_button.setIcon(QIcon("assets/icons/zoom_in_duotone.svg"))
        zoom_in_button.setToolTip("Zoom in")
        zoom_in_button.setStyleSheet(Styles.ICON_BUTTON_STYLE)
        zoom_in_button.clicked.connect(self._zoom_in_preview)
        toolbar.addWidget(zoom_in_button)
        
        zoom_out_button = QPushButton()
        zoom_out_button.setIcon(QIcon("assets/icons/zoom_out_duotone.svg"))
        zoom_out_button.setToolTip("Zoom out")
        zoom_out_button.setStyleSheet(Styles.ICON_BUTTON_STYLE)
        zoom_out_button.clicked.connect(self._zoom_out_preview)
        toolbar.addWidget(zoom_out_button)
        
        zoom_reset_button = QPushButton()
        zoom_reset_button.setIcon(QIcon("assets/icons/zoom_reset_duotone.svg"))
        zoom_reset_button.setToolTip("Reset zoom")
        zoom_reset_button.setStyleSheet(Styles.ICON_BUTTON_STYLE)
        zoom_reset_button.clicked.connect(self._reset_preview_zoom)
        toolbar.addWidget(zoom_reset_button)
        
        toolbar.addSeparator()
        
        rotate_left_button = QPushButton()
        rotate_left_button.setIcon(QIcon("assets/icons/rotate_left_duotone.svg"))
        rotate_left_button.setToolTip("Ruota a sinistra")
        rotate_left_button.setStyleSheet(Styles.ICON_BUTTON_STYLE)
        rotate_left_button.clicked.connect(self.rotate_image_90_ccw)
        toolbar.addWidget(rotate_left_button)
        
        rotate_right_button = QPushButton()
        rotate_right_button.setIcon(QIcon("assets/icons/rotate_right_duotone.svg"))
        rotate_right_button.setToolTip("Ruota a destra")
        rotate_right_button.setStyleSheet(Styles.ICON_BUTTON_STYLE)
        rotate_right_button.clicked.connect(self.rotate_image_90_cw)
        toolbar.addWidget(rotate_right_button)
        
        toolbar.addSeparator()
        
        # Aggiungi il pulsante per il ritaglio
        crop_button = QPushButton()
        crop_button.setIcon(QIcon("assets/icons/crop_duotone.svg")) # Placeholder
        crop_button.setToolTip("Ritaglia immagine")
        crop_button.setStyleSheet(Styles.ICON_BUTTON_STYLE)
        crop_button.clicked.connect(self._open_crop_dialog)
        toolbar.addWidget(crop_button)
        
        toolbar.addSeparator()
        
        # fullscreen_button = QPushButton()
        # fullscreen_button.setIcon(QIcon("assets/icons/fullscreen_duotone.svg"))
        # fullscreen_button.setToolTip("Schermo intero (F11)")
        # fullscreen_button.setStyleSheet(Styles.ICON_BUTTON_STYLE)
        # fullscreen_button.clicked.connect(lambda: self.fullscreen_toggled.emit(not self.is_fullscreen))
        # toolbar.addWidget(fullscreen_button)
        
        main_layout.addWidget(toolbar)
        
        # Etichetta per lo zoom
        self.zoom_label = QLabel("Zoom: 100%")
        self.zoom_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.zoom_label.setStyleSheet("color: white; background-color: rgba(0, 0, 0, 0.5); padding: 2px 5px; border-radius: 3px;")
        main_layout.addWidget(self.zoom_label)

        # Area di scorrimento per l'anteprima
        self.scroll_area = QScrollArea()
        self.scroll_area.setWidgetResizable(True)
        self.scroll_area.setFrameShape(QFrame.Shape.NoFrame)
        self.scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        self.scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        self.scroll_area.setAlignment(Qt.AlignmentFlag.AlignCenter)
        main_layout.addWidget(self.scroll_area)
        
        # Contenitore per l'anteprima
        preview_container = QWidget()
        preview_container.setStyleSheet("background-color: #1a1a1a;")
        preview_layout = QVBoxLayout(preview_container)
        preview_layout.setContentsMargins(PREVIEW_PADDING, PREVIEW_PADDING, PREVIEW_PADDING, PREVIEW_PADDING)
        preview_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # Etichetta per l'anteprima
        self.preview_label = QLabel("Seleziona un'immagine per visualizzarla")
        self.preview_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.preview_label.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        self.preview_label.setStyleSheet("color: white; font-size: 16px;")
        preview_layout.addWidget(self.preview_label)
        
        self.scroll_area.setWidget(preview_container)
        
        # Etichetta per lo stato dell'anteprima
        self.status_label = QLabel()
        self.status_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.status_label.setStyleSheet("color: white; background-color: rgba(0, 0, 0, 0.5); padding: 2px 5px; border-radius: 3px;")
        main_layout.addWidget(self.status_label)

    def _open_crop_dialog(self):
        """Apre il dialogo di ritaglio per l'immagine corrente."""
        if not self.current_image_path or not self.original_pixmap:
            QMessageBox.information(
                self,
                "Ritaglio Immagine",
                "Seleziona prima un'immagine da ritagliare."
            )
            return
        
        try:
            # Importa il dialogo di ritaglio
            from ui.dialogs.crop_dialog import CropDialog
            
            # Crea e mostra il dialogo
            dialog = CropDialog(self.current_image_path, self.original_pixmap, self)
            dialog.image_cropped.connect(self._on_image_cropped)
            dialog.exec()
            
        except Exception as e:
            logger.error(f"Errore nell'apertura del dialogo di ritaglio: {e}")
            QMessageBox.critical(
                self,
                "Errore Ritaglio",
                f"Impossibile aprire il dialogo di ritaglio:\n{str(e)}"
            )

    def _on_image_cropped(self, image_path: str):
        """
        Gestisce il completamento del ritaglio.
        
        Args:
            image_path: Percorso dell'immagine ritagliata
        """
        try:
            # Aggiorna l'anteprima con l'immagine ritagliata
            self.show_image(image_path)
            
            # Emetti il segnale che l'immagine è stata aggiornata (per il thumbnail, ecc.)
            self.image_updated.emit(image_path)
            
            # Potremmo ancora voler emettere image_loaded se altri componenti 
            # si aspettano questo segnale specificamente dopo un caricamento/ricaricamento.
            # Se show_image già emette image_loaded, questo potrebbe essere ridondante.
            # Per ora, lo lascio per mantenere il comportamento precedente, ma potrebbe essere rivisto.
            self.image_loaded.emit(image_path) 
            
            logger.info(f"Immagine ritagliata e segnale image_updated emesso per: {image_path}")
            
        except Exception as e:
            logger.error(f"Errore nel caricamento dell'immagine ritagliata o nell'emissione del segnale: {e}")
            self.error_occurred.emit("Errore Ritaglio", f"Impossibile caricare l'immagine ritagliata: {str(e)}")

    def set_image_list(self, image_paths):
        """
        Imposta la lista delle immagini disponibili per la navigazione.
        
        Args:
            image_paths (list): Lista dei percorsi delle immagini
        """
        self.image_list = image_paths
        logger.info(f"RightPanel: impostata lista di {len(image_paths)} immagini")
        
    def show_image(self, image_path):
        """
        Mostra un'immagine nel pannello di anteprima.
        Args:
            image_path (str): Percorso dell'immagine da mostrare
        """
        if not image_path or not os.path.exists(image_path):
            logger.error(f"Impossibile caricare l'immagine: {image_path} - File non trovato")
            self.preview_label.setText("Immagine non trovata")
            return
            
        try:
            # Aggiorna il percorso dell'immagine corrente
            self.current_image_path = image_path
            
            # Carica l'immagine dal disco
            # Nota: non usiamo la cache per l'immagine a dimensione piena
            # perché potrebbe essere troppo grande per la memoria
            pixmap = QPixmap(image_path)
                
            if pixmap.isNull():
                logger.error(f"Impossibile caricare l'immagine: {image_path} - Pixmap nullo")
                self.preview_label.setText("Impossibile caricare l'immagine")
                return
                
            # Memorizza il pixmap originale
            self.original_pixmap = pixmap
            
            # Reimposta lo zoom e la rotazione
            self.zoom_factor = 1.0
            self.rotation_angle = 0
            
            # Applica lo zoom iniziale per adattare l'immagine al pannello
            self._apply_zoom(reset=True)
            
            # Aggiorna lo stato dell'anteprima
            self._update_preview_status()
            
            # Aggiorna l'indice corrente nella lista delle immagini
            if image_path in self.image_list:
                self.current_image_index = self.image_list.index(image_path)
            
            # Emetti il segnale di caricamento dell'immagine
            self.image_loaded.emit(image_path)
            
            # Se c'è una sidebar destra, aggiorna i metadati EXIF
            if self.right_sidebar is not None:
                try:
                    self.right_sidebar.show_image_info(image_path)
                except Exception as sidebar_error:
                    logger.error(f"Errore nell'aggiornamento dei metadati: {sidebar_error}")
            
        except Exception as e:
            logger.error(f"Errore nel caricamento dell'immagine: {e}")
            self.preview_label.setText(f"Errore: {str(e)}")

    def _fit_image_to_panel(self):
        """
        Calcola il fattore di scala per adattare l'immagine al pannello.
        
        Returns:
            float: Fattore di scala
        """
        if not self.original_pixmap or self.original_pixmap.isNull():
            return 1.0
            
        # Ottieni le dimensioni dell'area di visualizzazione
        view_width = self.scroll_area.width() - 2 * PREVIEW_PADDING
        view_height = self.scroll_area.height() - 2 * PREVIEW_PADDING
        
        # Ottieni le dimensioni dell'immagine originale
        img_width = self.original_pixmap.width()
        img_height = self.original_pixmap.height()
        
        if img_width <= 0 or img_height <= 0 or view_width <= 0 or view_height <= 0:
            return 1.0
            
        # Calcola il fattore di scala per adattare l'immagine all'area di visualizzazione
        scale_x = view_width / img_width
        scale_y = view_height / img_height
        
        # Usa il fattore più piccolo per mantenere le proporzioni
        return min(scale_x, scale_y)

    def _apply_zoom(self, factor=None, reset=False):
        """
        Applica lo zoom all'immagine.
        
        Args:
            factor: Fattore di zoom da applicare (opzionale)
            reset: Se True, reimposta lo zoom al valore predefinito
        """
        if not self.original_pixmap or self.original_pixmap.isNull():
            return
            
        # Se reset è True, calcola il fattore di zoom per adattare l'immagine al pannello
        if reset:
            self.zoom_factor = self._fit_image_to_panel()
        # Altrimenti, se factor è specificato, aggiorna il fattore di zoom
        elif factor is not None:
            self.zoom_factor *= factor
            
        # Limita il fattore di zoom
        self.zoom_factor = max(0.1, min(10.0, self.zoom_factor))
        
        # Applica la rotazione e lo zoom all'immagine
        transform = QTransform()
        transform.rotate(self.rotation_angle)
        
        rotated_pixmap = self.original_pixmap.transformed(transform, Qt.TransformationMode.SmoothTransformation)

        # Calcola le nuove dimensioni
        new_width = int(rotated_pixmap.width() * self.zoom_factor)
        new_height = int(rotated_pixmap.height() * self.zoom_factor)

        # Ridimensiona l'immagine
        scaled_pixmap = rotated_pixmap.scaled(new_width, new_height, Qt.AspectRatioMode.KeepAspectRatio, Qt.TransformationMode.SmoothTransformation)
        
        # Imposta l'immagine nell'etichetta
        self.preview_label.setPixmap(scaled_pixmap)
        self.preview_label.setFixedSize(scaled_pixmap.size())
        
        # Aggiorna l'etichetta dello zoom
        self._update_zoom_label()
        
        # Emetti il segnale di cambio zoom
        self.zoom_changed.emit(self.zoom_factor)

    def _update_zoom_label(self):
        """Aggiorna l'etichetta dello zoom."""
        zoom_percent = int(self.zoom_factor * 100)
        self.zoom_label.setText(f"Zoom: {zoom_percent}%")

    def _update_preview_status(self):
        """Aggiorna le informazioni di stato dell'anteprima."""
        if not self.original_pixmap or self.original_pixmap.isNull() or not self.current_image_path:
            self.status_label.setText("")
            return
            
        # Ottieni le dimensioni dell'immagine
        width = self.original_pixmap.width()
        height = self.original_pixmap.height()
        
        # Ottieni il nome del file
        filename = os.path.basename(self.current_image_path)
        
        # Aggiorna l'etichetta di stato
        self.status_label.setText(f"{filename} | {width}x{height} px")

    def _zoom_in_preview(self):
        """Aumenta lo zoom dell'anteprima."""
        self._apply_zoom(factor=1.2)

    def _zoom_out_preview(self):
        """Diminuisce lo zoom dell'anteprima."""
        self._apply_zoom(factor=0.8)

    def _reset_preview_zoom(self):
        """Reimposta lo zoom dell'anteprima."""
        self._apply_zoom(reset=True)

    def _show_prev_image(self):
        """Richiede di mostrare l'immagine precedente."""
        self.request_prev_image.emit()

    def _show_next_image(self):
        """Richiede di mostrare l'immagine successiva."""
        self.request_next_image.emit()

    def rotate_image_90_cw(self):
        """Ruota l'immagine di 90 gradi in senso orario."""
        self.rotate_image(90)

    def rotate_image_90_ccw(self):
        """Ruota l'immagine di 90 gradi in senso antiorario."""
        self.rotate_image(-90)

    def rotate_image(self, angle: int):
        """
        Ruota l'immagine di un angolo specifico.
        
        Args:
            angle: Angolo di rotazione in gradi
        """
        if not self.original_pixmap or self.original_pixmap.isNull():
            return
            
        # Aggiorna l'angolo di rotazione
        self.rotation_angle = (self.rotation_angle + angle) % 360
        
        # Applica la rotazione
        self._apply_zoom()
        
        # Emetti il segnale di rotazione
        self.rotation_changed.emit(self.rotation_angle)
        
        # Se l'immagine è stata ruotata di 90 o 270 gradi, aggiorna le dimensioni
        if angle % 180 != 0:
            self._update_preview_status()

    def wheelEvent(self, event: QWheelEvent):
        """
        Gestisce l'evento della rotella del mouse.
        
        Args:
            event: Evento della rotella del mouse
        """
        # Ottieni il delta della rotella
        delta = event.angleDelta().y()
        
        # Applica lo zoom
        if delta > 0:
            self._zoom_in_preview()
        else:
            self._zoom_out_preview()

    def resizeEvent(self, event):
        """
        Gestisce l'evento di ridimensionamento del pannello.
        
        Args:
            event: Evento di ridimensionamento
        """
        super().resizeEvent(event)
        
        # Aggiorna l'anteprima quando il pannello viene ridimensionato
        if self.original_pixmap and not self.original_pixmap.isNull():
            self._apply_zoom()

    def _connect_signals(self):
        """Connette i segnali agli slot."""
        # Connessioni per il panning
        self.pan_timer.timeout.connect(self._apply_pending_scroll)
        
        # Connessioni per gli eventi del mouse
        self.preview_label.mousePressEvent = self._preview_mouse_press
        self.preview_label.mouseMoveEvent = self._preview_mouse_move
        self.preview_label.mouseReleaseEvent = self._preview_mouse_release

    def _preview_mouse_press(self, event: QMouseEvent):
        """
        Gestisce l'evento di pressione del mouse sull'anteprima.
        
        Args:
            event: Evento del mouse
        """
        if event.button() == Qt.MouseButton.LeftButton:
            self.panning = True
            self.last_mouse_position = event.pos()
            self.setCursor(Qt.CursorShape.ClosedHandCursor)

    def _preview_mouse_move(self, event: QMouseEvent):
        """
        Gestisce l'evento di movimento del mouse sull'anteprima.
        
        Args:
            event: Evento del mouse
        """
        if self.panning and self.last_mouse_position is not None:
            delta = event.pos() - self.last_mouse_position
            self.last_mouse_position = event.pos()
            
            # Aggiorna la posizione di scorrimento
            h_scroll = self.scroll_area.horizontalScrollBar()
            v_scroll = self.scroll_area.verticalScrollBar()
            
            h_scroll.setValue(h_scroll.value() - delta.x())
            v_scroll.setValue(v_scroll.value() - delta.y())

    def _preview_mouse_release(self, event: QMouseEvent):
        """
        Gestisce l'evento di rilascio del mouse sull'anteprima.
        
        Args:
            event: Evento del mouse
        """
        if event.button() == Qt.MouseButton.LeftButton:
            self.panning = False
            self.last_mouse_position = None
            self.setCursor(Qt.CursorShape.ArrowCursor)

    def _apply_pending_scroll(self):
        """Applica lo scorrimento pendente."""
        if self.pending_scroll_position is not None:
            self.scroll_area.horizontalScrollBar().setValue(self.pending_scroll_position.x())
            self.scroll_area.verticalScrollBar().setValue(self.pending_scroll_position.y())
            self.pending_scroll_position = None
