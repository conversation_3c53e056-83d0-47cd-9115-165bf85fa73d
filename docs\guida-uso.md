# Guida all'Uso dell'Applicazione PhotoDrop
*Versione 2.0 - Applicazione Completa e Pronta per Produzione*

## 🎉 **APPLICAZIONE COMPLETAMENTE IMPLEMENTATA**

**PhotoDrop v2.0 è ora completamente funzionale con tutte le funzionalità implementate, zero errori di lint e pronta per la produzione!**

### ✅ **Nuove Funzionalità Versione 2.0**
- ✅ **Sistema Spostamento File**: Spostamento multiplo con gestione conflitti
- ✅ **Design System 2.0**: Interfaccia moderna e responsive
- ✅ **Pattern MVC Completo**: Controller integrati per performance ottimali
- ✅ **Cache Placeholder Intelligente**: Sistema avanzato di icone placeholder
- ✅ **Configurazione Avanzata**: Dialog impostazioni completo
- ✅ **Zero Errori Lint**: Codice pulito e professionale

## Installazione

### Prerequisiti
- Python 3.13.3 o superiore (raccomandato)
- PySide6 6.9.0 (Qt per Python)
- Pillow (PIL) per elaborazione immagini

### Installazione delle dipendenze
```bash
pip install -r requirements.txt
```

### Avvio dell'applicazione
```bash
python main.py
```

### Avvio rapido Windows
```batch
run_app.bat
```

## 🚀 Nuove Funzionalità v2.0

### **🔧 Sistema Spostamento File**
- **Selezione Multipla**: Seleziona più file con Ctrl+Click
- **Spostamento Sicuro**: Gestione automatica conflitti file esistenti
- **Feedback Dettagliato**: Risultati completi con conteggio successi/errori
- **Aggiornamento Automatico**: La vista si aggiorna automaticamente dopo lo spostamento

**Come usare:**
1. Seleziona uno o più file nella lista (Ctrl+Click per selezione multipla)
2. Clicca destro e seleziona "Sposta File" dal menu contestuale
3. Scegli la cartella di destinazione
4. Conferma l'operazione
5. Visualizza i risultati dettagliati

### **🎨 Design System 2.0**
- **ModernTopbar**: Topbar responsive con navigazione avanzata
- **Layout Responsive**: Ottimizzazione automatica per diverse risoluzioni
- **Palette Colori Moderna**: Design professionale con contrasti ottimali
- **Typography Avanzata**: Font Segoe UI Variable per leggibilità ottimale

### **⚙️ Sistema Configurazione Avanzato**
- **Dialog Impostazioni**: Accesso tramite icona ingranaggio nella topbar
- **Configurazione Cache**: Controllo completo dimensioni e scadenza cache
- **Ultima Cartella**: Caricamento automatico dell'ultima cartella aperta
- **Salvataggio Automatico**: Geometria finestra e preferenze salvate automaticamente

**Come accedere:**
1. Clicca l'icona ingranaggio (⚙️) nella topbar
2. Seleziona "Impostazioni" dal menu
3. Naviga tra le tabs per configurare le diverse opzioni
4. Le modifiche vengono salvate automaticamente

## Struttura del Progetto

Il progetto PhotoDrop è organizzato nelle seguenti directory principali:

- `/core`: Contiene la logica di base dell'applicazione e le classi principali.
- `/ui`: Contiene i file relativi all'interfaccia utente, inclusi i dialoghi (`/ui/dialogs`).
- `/utils`: Contiene funzioni e classi di utilità generali.
- `/services`: Potrebbe contenere servizi esterni o logiche specifiche.
- `/assets`: Contiene risorse come icone (`/assets/icons`).
- `/docs`: Contiene la documentazione del progetto (incluso questo file e `app_map.md`).
- `/logs`: Contiene i file di log dell'applicazione.
- `/backup`: Potrebbe contenere backup automatici.

Il file `app_map.md` nella directory `/docs` fornisce una mappa dettagliata dell'intero albero del progetto ed è fondamentale per orientarsi.

## Linee Guida per lo Sviluppo

Quando si contribuisce al progetto, seguire queste linee guida:

- **Commenti:** Scrivere commenti chiari e concisi nel codice, iniziando con il nome del file e il percorso relativo.
- **Modularità:** Mantenere il codice ben organizzato in moduli e classi con responsabilità specifiche.
- **Processo Error Handling:** Implementare una gestione degli errori robusta e utilizzare il sistema di logging.
- **UI/Layout:** Non modificare il layout delle pagine esistenti a meno di indicazioni specifiche.
- **Documentazione:** Mantenere la documentazione (`/docs`) aggiornata, in particolare `app_map.md` e questa guida.
- **Commit Granulari:** Effettuare commit piccoli e focalizzati che risolvono un singolo problema o aggiungono una singola funzionalità.

## Componenti Chiave: Esempi di Implementazione

### Il Sistema della Guida

La visualizzazione della guida utente è gestita dalla classe `HelpDialog` nel file `ui/dialogs/help_dialog.py`. Questa classe si occupa di:

1.  **Caricamento del Contenuto:** Legge il contenuto markdown dal file `docs/help.md`.
2.  **Aggiunta Icone:** Utilizza la funzione `add_icons_to_markdown` per cercare nomi di comandi nel testo e inserire i tag HTML `<img>` corrispondenti. La mappatura tra nomi dei comandi e file delle icone si trova all'interno di questa funzione.
3.  **Conversione Markdown in HTML:** Utilizza la libreria esterna `markdown` (specificata in `requirements.txt`) con le estensioni `toc` (per la generazione automatica dell'indice) e `fenced_code` (per i blocchi di codice) per convertire il testo markdown (con le icone aggiunte) in HTML.
4.  **Applicazione Stile:** Applica uno stile CSS personalizzato all'HTML generato tramite la funzione `apply_html_style`. Lo stile è definito direttamente come una stringa multilinea all'interno del codice per facilità di gestione in questo progetto.
5.  **Visualizzazione:** Mostra l'HTML risultante in un `QTextBrowser`.

Per modificare il contenuto della guida utente, modificare il file `docs/help.md`. Per modificare l'aspetto o l'aggiunta delle icone, modificare `ui/dialogs/help_dialog.py`.

### Gestione delle Dipendenze

Le dipendenze del progetto sono elencate nel file `requirements.txt`. Per installare o aggiornare le dipendenze, utilizzare pip:

```bash
pip install -r requirements.txt
```

Assicurarsi di eseguire questo comando in un ambiente virtuale se necessario.

### Esecuzione dell'Applicazione

L'applicazione può essere avviata eseguendo lo script `run_app.bat` (su Windows) o un comando simile su altri sistemi operativi. Questo script probabilmente attiva un ambiente virtuale e lancia `main.py`.

## Aggiornamento della Documentazione

È fondamentale mantenere la documentazione aggiornata. In particolare:

- Ogni volta che si crea o elimina un file, aggiornare `docs/app_map.md`.
- Documentare le nuove funzionalità o modifiche significative in questa guida (`docs/guida-uso.md`).
- Mantenere `docs/help.md` aggiornato con le funzionalità correnti per gli utenti finali.

## Risorse Utili

- Documentazione PySide6: [Link alla documentazione ufficiale PySide6]
- Documentazione Python Markdown: [Link alla documentazione ufficiale markdown]

## Test delle ottimizzazioni

### Nota risoluzione bug preview
Se la preview immagini non funziona, assicurarsi di avere l'ultima versione di `right_panel.py` che usa solo QPixmap tramite load_image (senza codice PIL/Pillow manuale).

### 1. Test delle prestazioni di caricamento

Per verificare le ottimizzazioni di caricamento:

1. Preparare una cartella di test con almeno 100-200 immagini
2. Misurare il tempo di caricamento della versione precedente
3. Misurare il tempo di caricamento della nuova versione ottimizzata
4. Confrontare anche il consumo di memoria durante il caricamento
5. Verificare che non ci siano errori di compatibilità tra il formato dei dati scambiati tra i moduli

### 2. Test della reattività dell'interfaccia

Per verificare che l'interfaccia rimanga reattiva:

1. Aprire una cartella con molte immagini
2. Durante il caricamento, provare a:
   - Ridimensionare la finestra
   - Fare clic su vari pulsanti
   - Muovere il cursore della dimensione delle miniature
3. L'interfaccia dovrebbe rimanere reattiva senza blocchi

### 3. Test del caricamento asincrono

Per verificare il corretto funzionamento del caricamento asincrono e lazy loading:

1. Aprire una cartella con più di 50 immagini
2. Osservare che l'interfaccia resta reattiva durante il caricamento delle immagini
3. Verificare che le anteprime vengano caricate progressivamente e visualizzate correttamente
4. Controllare che non ci siano errori nei log relativi a incompatibilità di formato dati tra i vari componenti
5. Verificare che i segnali emessi vengano ricevuti correttamente dai rispettivi slot

### 4. Test della cache

Per verificare l'efficacia della cache:

1. Aprire una cartella con immagini
2. Navigare tra le immagini per caricarle in cache (sia RAM che disco)
3. Navigare nuovamente tra le stesse immagini - dovrebbero apparire istantaneamente
4. Utilizzare diverse dimensioni di zoom e verificare che vengano memorizzate in cache
5. **Test Gestione Cache su Disco (all'avvio):**
    - Modificare manualmente alcune date di file nella cartella `.cache` per renderli "vecchi".
    - Riempire la cartella `.cache` con file fittizi per superare la dimensione massima configurata.
    - Avviare l'applicazione e verificare che i file vecchi e/o in eccesso vengano eliminati dalla cartella `.cache` come da policy (controllare i log per conferma).
6. **Test Opzione "Svuota Cache Ora":**
    - Aprire il menu Opzioni dall'header.
    - Selezionare "Svuota Cache Ora" e confermare.
    - Verificare che la cartella `.cache` sia stata completamente svuotata.
7. **Test Opzioni Configurazione Cache (Dimensione/Età):**
    - Aprire il menu Opzioni e selezionare "Dimensione Cache Disco..." e "Età File Cache Disco...".
    - Verificare che i dialoghi mostrino i valori attuali (se implementata la lettura da configurazione) e permettano di inserire nuovi valori.
    - (Se l'applicazione delle modifiche è implementata) Verificare che le nuove impostazioni vengano salvate e applicate al successivo avvio o gestione della cache.

### 5. Test della rinomina ottimizzata

Per verificare le ottimizzazioni nella rinomina:

1. Preparare una cartella con diversi file immagine
2. Riordinare i file trascinandoli nella lista
3. Testare la rinomina con vari prefissi e numeri iniziali
4. Verificare la gestione dei conflitti (ad esempio tentando di rinominare file con lo stesso nome)

### 6. Test della gestione della memoria

Per verificare l'ottimizzazione dell'uso della memoria:

1. Monitorare l'uso della memoria (con Task Manager o Activity Monitor)
2. Aprire una cartella con molte immagini ad alta risoluzione
3. Navigare tra le immagini e verificare che l'uso della memoria rimanga stabile
4. Verificare che la memoria venga liberata quando si chiude l'applicazione

## Guida all'Uso di PhotoDrop

> **Nota:** La mappa aggiornata dell'intero albero delle cartelle/file è consultabile in `docs/app_tree.txt`.

L'applicazione **PhotoDrop** consente di rinominare, ordinare e visualizzare immagini in modo avanzato con un'interfaccia moderna e funzionalità di pan e zoom ottimizzate.

### Navigazione tra le immagini

1. **Selezione iniziale**
   - Aprire una cartella contenente immagini utilizzando il pulsante "Seleziona Cartella"
   - Le immagini verranno caricate nel pannello sinistro come anteprime
   - La prima immagine verrà mostrata automaticamente nel pannello di anteprima

2. **Navigazione di base**
   - Fare clic su un'anteprima nel pannello sinistro per visualizzarla
   - Utilizzare i pulsanti **< Precedente** e **Successivo >** per scorrere le immagini
   - Oppure utilizzare i tasti freccia **←** e **→** della tastiera
   - *Nota: Qualunque metodo di navigazione tu scelga, la miniatura selezionata nel pannello sinistro e l'immagine mostrata nel pannello di anteprima rimarranno sempre sincronizzate.*

3. **Zoom e Pan**
   - **Zoom avanti/indietro**: Usare la rotella del mouse o i pulsanti **+** e **-**
   - **Pan**: Tenere premuto il tasto sinistro del mouse e trascinare l'immagine
   - **Reset zoom**: Fare clic sul pulsante **Reset** per tornare alla visualizzazione completa

4. **Rotazione**
   - Utilizzare i pulsanti di rotazione per ruotare l'immagine di 90°
   - La rotazione aggiorna immediatamente sia l'anteprima che la miniatura nella lista
   - La gestione EXIF è automatica: le immagini ruotate sono sempre visualizzate correttamente

5. **Modalità schermo intero**
   - Premere **F11** o il pulsante di schermo intero nell'angolo in alto a destra
   - Per uscire, premere nuovamente **F11** o **Esc**

6. **Visualizzazione metadati**
   - I metadati EXIF vengono mostrati automaticamente nel pannello di destra
   - Scorrere per visualizzare tutte le informazioni disponibili

### Suggerimenti per la navigazione

- Tenere premuto **Ctrl** durante lo zoom per un effetto più fluido
- Fare doppio clic su un'immagine per adattarla automaticamente alla finestra
- Utilizzare la barra spaziatrice per passare all'immagine successiva
- Premere **Home** per tornare alla prima immagine
- Premere **Fine** per andare all'ultima immagine

L'interfaccia principale presenta un **header** in alto a destra con controlli globali:
- **Schermo Intero**: Per visualizzare l'applicazione a schermo intero (accessibile anche con F11).
- **Opzioni (⚙️)**: Apre un menu per la gestione delle impostazioni della cache.
- **Chiudi (X)**: Termina l'applicazione.

Sotto l'header, l'area di lavoro è divisa in due pannelli principali:

### Pannello Destro (Visualizzazione Immagini)

Il pannello destro è strutturato con:
1. **Area di anteprima**: Visualizza l'immagine selezionata con zoom e pan.
2. **Barra inferiore**: Mostra la percentuale di zoom e le dimensioni dell'immagine.
3. **Pulsanti di navigazione**: 
   - **←** (Prev): Mostra l'immagine precedente (tasto ←)
   - **→** (Next): Mostra l'immagine successiva (tasto →)
4. **Controlli zoom**: 
   - **-**: Zoom indietro
   - **+**: Zoom avanti
   - **Reset**: Reimposta lo zoom all'adattamento automatico
   - **Schermo intero**: Visualizza l'immagine a schermo intero
5. **Controlli rotazione**: 
   - **Rotate Left**: Ruota l'immagine a sinistra (Shift+R)
   - **Rotate Right**: Ruota l'immagine a destra (R)

### Funzionalità principali

#### 1. Selezione della cartella
- Fare clic sul pulsante "Sfoglia" per selezionare una cartella di immagini
- Le immagini verranno caricate e visualizzate nella lista a sinistra
- Il conteggio delle foto viene mostrato accanto al nome della cartella

#### 2. Gestione delle miniature
- Utilizzare il cursore "Dimensione anteprime" per regolare la dimensione delle miniature
- Le immagini verranno ridimensionate dinamicamente

#### 3. Visualizzazione e navigazione
- Fare clic su un'immagine nella lista per visualizzarla nel pannello di destra
- Utilizzare i pulsanti "←" e "→" per navigare tra le immagini
- **Zoom avanzato**:
  - Tenere premuto **Ctrl** e usare la rotella del mouse per zoomare avanti/indietro
  - Il punto sotto il cursore rimane fisso durante lo zoom
  - Utilizzare i pulsanti "+" e "-" per lo zoom incrementale
  - Fare clic su "Reset Zoom" per tornare alla dimensione ottimale
- **Panoramica (pan)**:
  - Tenere premuto il tasto sinistro del mouse e trascinare per spostarsi nell'immagine ingrandita
  - Il cursore cambia in una mano aperta quando è disponibile il pan
  - Le barre di scorrimento appaiono automaticamente quando necessario
- **Informazioni immagine**:
  - La barra inferiore mostra la percentuale di zoom attuale
  - Vengono mostrate le dimensioni originali dell'immagine in pixel
- Utilizzare il pan per muovere la foto all'interno della finestra
- Attivare/disattivare la modalità a schermo intero con l'apposito pulsante

#### 4. Riordinamento delle immagini
- Trascinare le immagini nella lista per riordinarle
- Utilizzare i pulsanti "Su" e "Giù" per spostare le immagini selezionate
- Selezionare più immagini tenendo premuto Ctrl o Shift mentre si fa clic

#### 5. Eliminazione delle immagini
- Selezionare una o più immagini nella lista
- Fare clic sul pulsante "Elimina" per rimuoverle
- Confermare l'eliminazione nella finestra di dialogo

#### 6. Rinomina delle immagini
- Specificare un prefisso nel campo "Prefisso" (es. "Vacanze_")
- Specificare un numero iniziale nel campo "Numero iniziale"
- Fare clic sul pulsante "Rinomina" per rinominare i file
- I file verranno rinominati nell'ordine in cui appaiono nella lista

#### 7. Menu Opzioni Cache
Accessibile cliccando l'icona "⚙️" nell'header, questo menu permette di:
- **Dimensione Cache Disco...**: Visualizzare (ed eventualmente in futuro impostare) la dimensione massima in MB consentita per la cache delle immagini su disco.
- **Età File Cache Disco...**: Visualizzare (ed eventualmente in futuro impostare) l'età massima in giorni consentita per i file nella cache su disco.
- **Svuota Cache Ora**: Eliminare immediatamente tutti i file presenti nella cache su disco.

### Footer
- In basso è presente un footer con il nome dell'applicazione **PhotoDrop** e la versione corrente.

## Risoluzione dei problemi comuni

### L'applicazione è lenta o mostra errori durante il caricamento di immagini
- Verificare che il sistema soddisfi i requisiti minimi
- Chiudere altre applicazioni che consumano molta memoria
- Ridurre la dimensione delle miniature per risparmiare memoria
- Controllare i log per eventuali errori relativi a parametri mancanti nei metodi
- Verificare che la cartella `.cache` sia accessibile in scrittura

### Errori durante la rinomina dei file
- Verificare che i file non siano utilizzati da altre applicazioni
- Verificare di avere i permessi di scrittura nella cartella
- Evitare caratteri speciali nel prefisso che potrebbero non essere validi per i nomi dei file

### Le immagini non vengono visualizzate correttamente
- Verificare che i file immagine non siano danneggiati
- Controllare che i formati siano supportati (.jpg, .png, .bmp, .gif, .tiff)
- Verificare che le immagini non siano troppo grandi per essere caricate in memoria

### L'interfaccia diventa non reattiva
- Se si verifica un blocco, attendere il completamento dell'operazione in corso
- Ridurre il numero di immagini caricate contemporaneamente
- Riavviare l'applicazione se necessario

## Limitazioni note
- Le immagini molto grandi (>50 MB) potrebbero causare problemi di memoria
- Il supporto per i formati di file è limitato a quelli più comuni
- I metadati EXIF diversi dall'orientamento non vengono preservati durante la rinomina

## Suggerimenti per le prestazioni ottimali
- Utilizzare cartelle con meno di 1000 immagini per sessione
- Mantenere le miniature a una dimensione ragionevole (100-150px)
- Evitare di caricare immagini di dimensioni eccessive
- Chiudere l'applicazione quando non è in uso per liberare memoria
