#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
ui/components/topbar.py
Componente Topbar moderna per PhotoDrop - Design System 2.0

Implementa una topbar fissa con:
- Logo/brand dell'applicazione
- Menu di navigazione principale
- Menu contestuali dinamici
- Area utente con controlli
- Design moderno e responsivo
"""

import logging
from typing import Dict, List, Optional, Callable
from PySide6.QtWidgets import (
    QWidget, QHBoxLayout, QVBoxLayout, QLabel, QPushButton, 
    QFrame, QSizePolicy, QSpacerItem, QButtonGroup, QMenu
)
from PySide6.QtCore import Qt, Signal, QSize
from PySide6.QtGui import QIcon, QFont, QAction

from ui.styles import Styles
from utils.logging_config import get_logger

logger = get_logger("PhotoDrop.ModernTopbar")

class ModernTopbar(QWidget):
    """
    Topbar moderna con menu contestuali e design system 2.0.
    
    Signals:
        navigation_changed: Emesso quando cambia la sezione di navigazione
        action_triggered: Emesso quando viene attivata un'azione
    """
    
    # Segnali
    navigation_changed = Signal(str)  # Sezione attiva
    action_triggered = Signal(str)    # Azione attivata
    
    def __init__(self, parent=None):
        """
        Inizializza la topbar moderna.
        
        Args:
            parent: Widget genitore
        """
        super().__init__(parent)
        
        # Configurazione widget
        self.setObjectName("topbar")
        self.setFixedHeight(48)  # Altezza fissa topbar
        self.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
        
        # Stato interno
        self.current_section = "browse"  # Sezione attiva corrente
        self.contextual_actions = {}     # Azioni contestuali per sezione
        self.is_compact_mode = False     # Modalità compatta per schermi piccoli

        # Gruppi di pulsanti per gestione esclusiva
        self.nav_button_group = QButtonGroup(self)
        self.nav_button_group.setExclusive(True)
        
        # Setup UI
        self._setup_ui()
        self._setup_navigation()
        self._connect_signals()
        
        logger.info("ModernTopbar inizializzata")
    
    def _setup_ui(self):
        """Configura l'interfaccia della topbar."""
        # Layout principale orizzontale
        main_layout = QHBoxLayout(self)
        main_layout.setContentsMargins(16, 0, 16, 0)
        main_layout.setSpacing(24)
        
        # === SEZIONE BRAND/LOGO ===
        self._create_brand_section(main_layout)
        
        # === SEZIONE NAVIGAZIONE ===
        self._create_navigation_section(main_layout)
        
        # === SEZIONE CONTESTUALE (dinamica) ===
        self._create_contextual_section(main_layout)
        
        # === SPACER CENTRALE ===
        spacer = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)
        main_layout.addItem(spacer)
        
        # === SEZIONE UTENTE ===
        self._create_user_section(main_layout)
    
    def _create_brand_section(self, layout: QHBoxLayout):
        """Crea la sezione brand/logo."""
        self.brand_label = QLabel("PhotoDrop")
        self.brand_label.setObjectName("brandLabel")
        
        # Font personalizzato per il brand
        brand_font = QFont(Styles.FONT_FAMILY_PRIMARY)
        brand_font.setPointSize(14)
        brand_font.setWeight(QFont.Weight.DemiBold)
        self.brand_label.setFont(brand_font)
        
        layout.addWidget(self.brand_label)
        
        # Separatore
        separator = self._create_separator()
        layout.addWidget(separator)
    
    def _create_navigation_section(self, layout: QHBoxLayout):
        """Crea la sezione di navigazione principale."""
        nav_container = QWidget()
        nav_container.setObjectName("navMenu")
        nav_layout = QHBoxLayout(nav_container)
        nav_layout.setContentsMargins(0, 0, 0, 0)
        nav_layout.setSpacing(8)
        
        # Pulsanti di navigazione principali
        nav_buttons = [
            ("browse", "Sfoglia", "folder_duotone.svg", "Sfoglia immagini"),
            ("edit", "Modifica", "crop_duotone.svg", "Modifica immagini"),
            ("organize", "Organizza", "rename_duotone.svg", "Organizza file"),
        ]
        
        for btn_id, text, icon_name, tooltip in nav_buttons:
            btn = self._create_nav_button(btn_id, text, icon_name, tooltip)
            nav_layout.addWidget(btn)
            self.nav_button_group.addButton(btn)
        
        layout.addWidget(nav_container)
    
    def _create_contextual_section(self, layout: QHBoxLayout):
        """Crea la sezione per menu contestuali."""
        self.contextual_container = QWidget()
        self.contextual_container.setObjectName("contextualMenu")
        self.contextual_layout = QHBoxLayout(self.contextual_container)
        self.contextual_layout.setContentsMargins(0, 0, 0, 0)
        self.contextual_layout.setSpacing(4)
        
        layout.addWidget(self.contextual_container)
        
        # Separatore
        separator = self._create_separator()
        layout.addWidget(separator)
    
    def _create_user_section(self, layout: QHBoxLayout):
        """Crea la sezione utente."""
        user_container = QWidget()
        user_container.setObjectName("userArea")
        user_layout = QHBoxLayout(user_container)
        user_layout.setContentsMargins(0, 0, 0, 0)
        user_layout.setSpacing(8)
        
        # Pulsanti area utente
        user_buttons = [
            ("settings", "⚙️", "Impostazioni"),
            ("help", "❓", "Guida"),
            ("fullscreen", "⛶", "Schermo intero"),
            ("close", "✕", "Chiudi")
        ]
        
        for btn_id, icon_text, tooltip in user_buttons:
            btn = self._create_user_button(btn_id, icon_text, tooltip)
            user_layout.addWidget(btn)
        
        layout.addWidget(user_container)
    
    def _create_nav_button(self, btn_id: str, text: str, icon_name: str, tooltip: str) -> QPushButton:
        """Crea un pulsante di navigazione."""
        btn = QPushButton(text)
        btn.setObjectName(f"nav_{btn_id}")
        btn.setCheckable(True)
        btn.setToolTip(tooltip)

        # Accessibilità: ARIA labels
        btn.setAccessibleName(f"Navigazione {text}")
        btn.setAccessibleDescription(tooltip)

        # Imposta icona se disponibile
        try:
            btn.setIcon(QIcon(f"assets/icons/{icon_name}"))
            btn.setIconSize(QSize(16, 16))
        except:
            pass

        # Connetti segnale
        btn.clicked.connect(lambda checked, id=btn_id: self._on_navigation_changed(id))

        return btn
    
    def _create_user_button(self, btn_id: str, icon_text: str, tooltip: str) -> QPushButton:
        """Crea un pulsante dell'area utente."""
        btn = QPushButton(icon_text)
        btn.setObjectName(f"user_{btn_id}")
        btn.setToolTip(tooltip)
        btn.setFixedSize(32, 32)

        # Accessibilità: ARIA labels
        btn.setAccessibleName(f"Azione {tooltip}")
        btn.setAccessibleDescription(tooltip)

        # Connetti segnale
        btn.clicked.connect(lambda: self.action_triggered.emit(btn_id))

        return btn
    
    def _create_separator(self) -> QFrame:
        """Crea un separatore verticale."""
        separator = QFrame()
        separator.setObjectName("separator")
        separator.setFrameShape(QFrame.Shape.VLine)
        separator.setFixedWidth(1)
        separator.setFixedHeight(24)
        return separator
    
    def _setup_navigation(self):
        """Configura la navigazione iniziale."""
        # Imposta sezione iniziale
        self.set_active_section("browse")
        
        # Configura azioni contestuali per ogni sezione
        self._setup_contextual_actions()
    
    def _setup_contextual_actions(self):
        """Configura le azioni contestuali per ogni sezione."""
        self.contextual_actions = {
            "browse": [
                ("refresh", "Aggiorna", self._refresh_images),
                ("view_mode", "Vista", self._toggle_view_mode),
                ("sort", "Ordina", self._show_sort_menu)
            ],
            "edit": [
                ("crop", "Ritaglia", self._crop_image),
                ("rotate", "Ruota", self._rotate_image),
                ("filters", "Filtri", self._show_filters)
            ],
            "organize": [
                ("rename", "Rinomina", self._rename_files),
                ("move", "Sposta", self._move_files),
                ("delete", "Elimina", self._delete_files)
            ]
        }
    
    def _connect_signals(self):
        """Connette i segnali interni."""
        # Il responsive design sarà gestito tramite metodi pubblici
        pass

    def resizeEvent(self, event):
        """Gestisce il resize del widget per responsive design."""
        super().resizeEvent(event)
        if event:
            # Ottieni la larghezza dalla finestra principale
            main_window = self.window()
            if main_window:
                width = main_window.width()
                self._update_responsive_mode(width)

    def _update_responsive_mode(self, width: int):
        """
        Aggiorna la modalità responsive in base alla larghezza.

        Args:
            width: Larghezza corrente della finestra
        """
        # Soglia per modalità compatta (< 1024px)
        should_be_compact = width < 1024

        if should_be_compact != self.is_compact_mode:
            self.is_compact_mode = should_be_compact
            self._apply_responsive_mode()
            logger.debug(f"Modalità responsive cambiata: {'compatta' if should_be_compact else 'normale'}")

    def _apply_responsive_mode(self):
        """Applica la modalità responsive corrente."""
        if self.is_compact_mode:
            self.setObjectName("topbar compact")
            self.setFixedHeight(40)  # Altezza ridotta
            # Nascondi alcuni elementi contestuali se necessario
            self._hide_contextual_overflow()
        else:
            self.setObjectName("topbar")
            self.setFixedHeight(48)  # Altezza normale
            self._show_all_contextual()

        # Riapplica gli stili
        self.style().unpolish(self)
        self.style().polish(self)

    def _hide_contextual_overflow(self):
        """Nasconde elementi contestuali in eccesso in modalità compatta."""
        # Mantieni solo i primi 2 pulsanti contestuali
        for i in range(self.contextual_layout.count()):
            item = self.contextual_layout.itemAt(i)
            if item and item.widget():
                item.widget().setVisible(i < 2)

    def _show_all_contextual(self):
        """Mostra tutti gli elementi contestuali in modalità normale."""
        for i in range(self.contextual_layout.count()):
            item = self.contextual_layout.itemAt(i)
            if item and item.widget():
                item.widget().setVisible(True)
    
    def set_active_section(self, section: str):
        """
        Imposta la sezione attiva.
        
        Args:
            section: ID della sezione da attivare
        """
        if section == self.current_section:
            return
            
        self.current_section = section
        
        # Aggiorna pulsanti navigazione
        for btn in self.nav_button_group.buttons():
            btn_id = btn.objectName().replace("nav_", "")
            btn.setChecked(btn_id == section)
        
        # Aggiorna menu contestuale
        self._update_contextual_menu()
        
        # Emetti segnale
        self.navigation_changed.emit(section)
        
        logger.debug(f"Sezione attiva cambiata: {section}")
    
    def _update_contextual_menu(self):
        """Aggiorna il menu contestuale per la sezione corrente."""
        # Rimuovi pulsanti esistenti
        for i in reversed(range(self.contextual_layout.count())):
            child = self.contextual_layout.itemAt(i).widget()
            if child:
                child.setParent(None)
        
        # Aggiungi pulsanti per sezione corrente
        actions = self.contextual_actions.get(self.current_section, [])
        for action_id, text, callback in actions:
            btn = QPushButton(text)
            btn.setObjectName(f"contextual_{action_id}")
            btn.clicked.connect(callback)
            self.contextual_layout.addWidget(btn)
    
    def _on_navigation_changed(self, section: str):
        """Gestisce il cambio di navigazione."""
        self.set_active_section(section)
    
    # === PLACEHOLDER METHODS PER AZIONI ===
    def _refresh_images(self): self.action_triggered.emit("refresh")
    def _toggle_view_mode(self): self.action_triggered.emit("view_mode")
    def _show_sort_menu(self): self.action_triggered.emit("sort")
    def _crop_image(self): self.action_triggered.emit("crop")
    def _rotate_image(self): self.action_triggered.emit("rotate")
    def _show_filters(self): self.action_triggered.emit("filters")
    def _rename_files(self): self.action_triggered.emit("rename")
    def _move_files(self): self.action_triggered.emit("move")
    def _delete_files(self): self.action_triggered.emit("delete")
