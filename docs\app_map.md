# App Map di PhotoDrop - Analisi Stato Sviluppo

Questo documento serve come mappa completa dell'applicazione PhotoDrop, aiutando a comprenderne la struttura e il funzionamento.

## STATO ATTUALE DELLO SVILUPPO (Aggiornato: 2025-06-04)

### 🟢 FUNZIONALITÀ COMPLETAMENTE IMPLEMENTATE

1. **Interfaccia Utente Base**
   - ✅ Finestra principale con layout a splitter
   - ✅ Pannello sinistro per lista immagini/miniature
   - ✅ Pannello destro per anteprima immagini
   - ✅ Sidebar destra per metadati EXIF
   - ✅ Header con controlli globali (schermo intero, guida, opzioni, chiudi)
   - ✅ Footer con informazioni di stato

2. **Gestione Immagini**
   - ✅ Caricamento asincrono delle immagini da cartella
   - ✅ Visualizzazione miniature in lista
   - ✅ Anteprima immagini ad alta risoluzione
   - ✅ Navigazione tra immagini (frecce, pulsanti)
   - ✅ Supporto formati: JPG, PNG, BMP, GIF, TIFF

3. **Sistema di Cache Avanzato**
   - ✅ Cache in memoria LRU per miniature
   - ✅ Cache su disco per immagini elaborate
   - ✅ Gestione automatica dimensioni cache
   - ✅ Pulizia automatica basata su età e dimensione
   - ✅ Controlli utente per gestione cache

4. **Visualizzazione Avanzata**
   - ✅ Sistema zoom e pan completo
   - ✅ Zoom con Ctrl+Rotella mouse
   - ✅ Pan con trascinamento mouse
   - ✅ Mantenimento punto sotto cursore durante zoom
   - ✅ Gestione intelligente barre scorrimento
   - ✅ Modalità schermo intero (F11)

5. **Metadati EXIF**
   - ✅ Lettura completa metadati EXIF
   - ✅ Visualizzazione formattata in sidebar
   - ✅ Gestione orientamento automatico
   - ✅ Informazioni tecniche dettagliate

6. **Sistema di Logging**
   - ✅ Logging configurabile per debug
   - ✅ File di log strutturati
   - ✅ Gestione errori robusta

### 🟢 FUNZIONALITÀ COMPLETATE OGGI (2025-06-09)

1. **Sistema Rinomina File** ✅ COMPLETATO
   - ✅ Modello FileRenamer con logica completa
   - ✅ Sistema di rollback per sicurezza
   - ✅ Verifica conflitti automatica
   - ✅ UI completamente integrata e funzionante
   - ✅ Uso del metodo sicuro execute_rename_plan

2. **Sistema Configurazione Utente** ✅ COMPLETATO
   - ✅ SettingsModel per gestione impostazioni persistenti
   - ✅ Dialog impostazioni completo con tabs
   - ✅ Salvataggio automatico geometria finestra
   - ✅ Caricamento ultima cartella aperta
   - ✅ Configurazioni cache personalizzabili
   - ✅ Integrazione completa nell'applicazione

3. **Sistema Crop/Ritaglio** ✅ COMPLETATO
   - ✅ CropDialog completamente funzionante
   - ✅ Integrazione con RightPanel
   - ✅ Segnali image_updated gestiti correttamente
   - ✅ Aggiornamento automatico miniature

### 🟡 FUNZIONALITÀ PARZIALMENTE IMPLEMENTATE

1. **Controllers MVC**
   - ✅ MainController base implementato
   - ✅ ImageListController completamente funzionante
   - ✅ MetadataController operativo
   - ⚠️ ImagePreviewController presente ma sottoutilizzato

### 🔴 FUNZIONALITÀ NON IMPLEMENTATE (Ridotte significativamente)

1. **Editing Avanzato**
   - ❌ Regolazioni luminosità/contrasto/saturazione
   - ❌ Filtri immagine avanzati
   - ❌ Integrazione completa Wand per editing avanzato

2. **Funzionalità Avanzate**
   - ❌ Sistema tagging immagini
   - ❌ Ricerca avanzata
   - ❌ Modalità slideshow
   - ❌ Supporto formati RAW, WebP, HEIC

3. **Localizzazione**
   - ❌ Supporto multilingua
   - ❌ Sistema traduzione

## 1. Mappatura del Progetto

Di seguito è rappresentata la struttura delle cartelle e dei file principali del progetto PhotoDrop. Le cartelle come `__pycache__`, `.git`, `.vscode`, `.trae` e i file di configurazione specifici dell'IDE sono stati omessi per chiarezza.

```
photodrop/
├── assets/
│   └── icons/
│       ├── arrow_down_duotone.svg
│       ├── arrow_left_duotone.svg
│       ├── arrow_right_duotone.svg
│       ├── arrow_up_duotone.svg
│       ├── close_modern.svg
│       ├── crop_duotone.svg
│       ├── folder_duotone.svg
│       ├── fullscreen_duotone.svg
│       ├── help_duotone.svg
│       ├── next_duotone.svg
│       ├── prev_duotone.svg
│       ├── rename_duotone.svg
│       ├── rotate_left_duotone.svg
│       ├── rotate_right_duotone.svg
│       ├── trash_duotone.svg
│       ├── zoom_in_duotone.svg
│       ├── zoom_out_duotone.svg
│       └── zoom_reset_duotone.svg
├── backup/
│   └── ui/
│       └── views/
│           └── panels/
│               └── right_panel_backup.py
├── controllers/
│   ├── image_list_controller.py
│   ├── image_preview_controller.py
│   ├── main_controller.py
│   └── metadata_controller.py
├── core/
│   ├── image_cache.py
│   └── image_operations.py
├── docs/
│   ├── README.md
│   ├── albero_progetto.txt
│   ├── app_map.md (Questo file)
│   ├── app_tree.txt
│   ├── architettura.md
│   ├── distribuzione.md
│   ├── guida-uso.md
│   ├── help.md
│   ├── ottimizzazioni-riassunto.md
│   └── todo_list.md
├── logs/
│   └── photodrop.log
├── main.py
├── models/
│   ├── file_renamer.py
│   ├── image_item.py
│   └── image_model.py
├── requirements.txt
├── run_app.bat
├── services/
│   └── image_loader.py
├── ui/
│   ├── __init__.py
│   ├── dialogs/
│   │   ├── crop_dialog.py
│   │   └── help_dialog.py
│   ├── left_panel.py
│   ├── main_window.py
│   ├── right_sidebar.py
│   ├── styles.py
│   ├── views/
│   │   ├── panels/
│   │   │   └── right_panel.py
│   │   └── widgets/ (vuota)
│   └── widgets/
│       ├── __init__.py
│       └── crop_widget.py
└── utils/
    ├── constants.py
    ├── exif_utils.py
    ├── image_utils.py
    └── logging_config.py
```

### Descrizione Funzionale dei Componenti Principali

#### **ARCHITETTURA ATTUALE (MVC Ibrido)**

L'applicazione segue un pattern MVC ibrido con le seguenti caratteristiche:

**📁 CONTROLLERS/** - Logica di controllo MVC
- `main_controller.py` (2.7KB) - Controller principale, coordina tutti gli altri
- `image_list_controller.py` (9.5KB) - Gestisce lista immagini e interazioni
- `image_preview_controller.py` (3.3KB) - Controlla anteprima immagini
- `metadata_controller.py` (2.0KB) - Gestisce visualizzazione metadati EXIF

**📁 MODELS/** - Gestione dati e logica business
- `image_model.py` (4.1KB) - Modello dati per immagini
- `image_item.py` (0.6KB) - Rappresentazione singolo item immagine
- `file_renamer.py` (9.0KB) - Sistema rinomina sicura con rollback

**📁 UI/** - Interfaccia utente (Views)
- `main_window.py` (23.1KB) - Finestra principale, assembla tutti i componenti
- `left_panel.py` (48.6KB) - Pannello sinistro con lista/griglia miniature
- `right_sidebar.py` (5.1KB) - Sidebar metadati EXIF
- `views/panels/right_panel.py` (23.4KB) - Pannello anteprima con zoom/pan
- `dialogs/` - Finestre dialogo (crop, help)
- `widgets/` - Widget riutilizzabili (crop_widget)
- `styles.py` (17.2KB) - Fogli stile QSS

**📁 CORE/** - Funzionalità centrali
- `image_cache.py` (16.3KB) - Sistema cache avanzato LRU
- `image_operations.py` (10.8KB) - Operazioni manipolazione immagini

**📁 SERVICES/** - Servizi applicazione
- `image_loader.py` (5.2KB) - Caricamento asincrono immagini

**📁 UTILS/** - Utilità di supporto
- `image_utils.py` (13.4KB) - Funzioni utility per immagini
- `exif_utils.py` (6.8KB) - Gestione metadati EXIF
- `constants.py` (0.7KB) - Costanti globali
- `logging_config.py` (0.8KB) - Configurazione logging

**📁 ASSETS/** - Risorse statiche
- `icons/` - 17 icone SVG moderne (Material/Fluent style)

#### **FLUSSO DATI PRINCIPALE**

1. **Avvio**: `main.py` → `PhotoDropApp` → inizializzazione componenti
2. **Caricamento**: Utente seleziona cartella → `ImageLoader` (asincrono) → cache → UI
3. **Navigazione**: Selezione miniatura → segnale → `RightPanel` → anteprima
4. **Metadati**: Caricamento immagine → `ExifUtils` → `MetadataController` → sidebar
5. **Cache**: Tutte le operazioni passano attraverso `ImageCache` per ottimizzazione

## 2. Guida di Riferimento

*   **Ambiente di Sviluppo**: (da completare)
*   **Regole Base da Seguire**: (da completare)
*   **Procedure Operative**: (da completare)
*   **File Critici da Non Modificare Leggermente**: (da completare)

## 3. Documentazione Tecnica

*   **Architettura del Sistema**: Vedi `architettura.md` e la memoria utente sull'architettura pianificata.
*   **Funzionamento del Database**: (N/A se non si usa un DB esplicito, altrimenti da completare)
*   **API Disponibili**: (da completare se ci sono API interne/esterne rilevanti)
*   **Metodo di Calcolo Sismico**: (N/A per questo progetto)

## 4. QUALITÀ DEL CODICE E ARCHITETTURA

### 🟢 PUNTI DI FORZA

1. **Architettura Solida**
   - Pattern MVC ben implementato
   - Separazione chiara delle responsabilità
   - Sistema di segnali Qt ben utilizzato

2. **Performance Ottimizzate**
   - Cache LRU efficiente per memoria
   - Caricamento asincrono non bloccante
   - Threading appropriato per operazioni pesanti

3. **Robustezza**
   - Gestione errori completa
   - Sistema logging dettagliato
   - Rollback automatico per operazioni critiche

4. **Codice Pulito**
   - Documentazione inline estensiva
   - Type hints utilizzati
   - Struttura modulare ben organizzata

### 🟡 AREE DI MIGLIORAMENTO

1. **Integrazione UI**
   - Alcuni dialog non completamente integrati
   - Controller sottoutilizzati in alcune aree

2. **Testing**
   - Mancano unit test automatizzati
   - Testing manuale limitato

3. **Configurazione**
   - Settings hardcoded, manca sistema configurazione utente

### 🔴 PROBLEMI CRITICI

1. **Nessun problema critico identificato**
   - L'applicazione è stabile e funzionale
   - Architettura scalabile per future estensioni

## 5. Registro Modifiche

### **Versione Corrente: v1.3-beta (UI MODERNIZATION)**

**🎨 MODERNIZZAZIONE UI IN CORSO (2025-06-10):**
- ✅ **Backup UI Esistente**: Backup completo creato in `backup/ui_modernization/`
- ✅ **Design System 2.0**: Palette colori moderna, typography Segoe UI Variable, spacing system
- ✅ **Topbar Moderna**: Componente ModernTopbar con navigazione e menu contestuali
- ✅ **Componenti Modulari**: Struttura `ui/components/` per componenti riutilizzabili
- 🔄 **Integrazione Completa**: Connessione segnali e funzionalità topbar
- 🔄 **Responsive Layout**: Ottimizzazione per diverse risoluzioni
- 🔄 **Testing UI**: Validazione nuova interfaccia

### **Versione Precedente: v1.2-beta (MAJOR UPDATE)**

**🎉 COMPLETAMENTI SIGNIFICATIVI (2025-06-09):**
- ✅ **Sistema Rinomina File**: Completamente integrato e sicuro
- ✅ **Sistema Configurazione**: Impostazioni persistenti complete
- ✅ **Sistema Crop/Ritaglio**: Funzionalità completa integrata
- ✅ **Gestione Cache Avanzata**: Configurabile dall'utente
- ✅ **Caricamento Ultima Cartella**: Automatico all'avvio

**Funzionalità Principali Completate:**
- ✅ Visualizzazione immagini con zoom/pan avanzato
- ✅ Sistema cache ottimizzato e configurabile
- ✅ Metadati EXIF completi
- ✅ Navigazione fluida tra immagini
- ✅ Interfaccia moderna e responsiva
- ✅ Rinomina batch sicura con rollback
- ✅ Ritaglio immagini integrato
- ✅ Sistema impostazioni completo

**Modifiche Significative Oggi (2025-06-09):**
- **Sistema Rinomina**: Integrato FileRenamer.execute_rename_plan per sicurezza
- **SettingsModel**: Creato sistema completo gestione impostazioni
- **SettingsDialog**: Dialog configurazione con tabs organizzate
- **MainWindow**: Integrazione salvataggio/caricamento geometria e impostazioni
- **LeftPanel**: Caricamento automatico ultima cartella
- **Cache**: Configurazione personalizzabile da impostazioni utente
- **Lint Fixes**: Corretti tutti gli errori lint in left_panel.py (Qt enums, attributi)

**Modifiche Precedenti:**
- **2025-06-04**: Analisi completa stato sviluppo
- **2025-05-28**: Risolto `TypeError` in `ui.left_panel.LeftPanel.update_items_after_rename`
- **2025-05-28**: Ottimizzazioni sistema cache e performance

## 6. RACCOMANDAZIONI PER SVILUPPO FUTURO

### 🎯 PRIORITÀ ALTA (Prossimi Sprint) - AGGIORNATO

1. **Testing e Stabilizzazione** ⭐ NUOVO FOCUS
   - Unit tests per componenti critici
   - Test integrazione sistema rinomina
   - Test configurazioni utente
   - Validazione sistema cache

2. **Miglioramenti UX Immediati**
   - Feedback visivo durante operazioni lunghe
   - Tooltips informativi
   - Shortcuts personalizzabili
   - Anteprima modifiche prima dell'applicazione

3. **Ottimizzazioni Performance**
   - Caricamento progressivo immagini grandi
   - Ottimizzazione memoria per cartelle con molte immagini
   - Cache intelligente basata su utilizzo

### 🎯 PRIORITÀ MEDIA (Versioni Future)

1. **Estensioni Funzionalità**
   - Supporto formati aggiuntivi (WebP, HEIC, RAW)
   - Sistema tagging e ricerca
   - Modalità slideshow

2. **Miglioramenti UX**
   - Localizzazione multilingua
   - Temi personalizzabili
   - Shortcuts configurabili

3. **Performance Avanzate**
   - Cache multi-livello
   - Caricamento progressivo
   - Ottimizzazioni memoria

### 🎯 PRIORITÀ BASSA (Roadmap Estesa)

1. **Funzionalità Avanzate**
   - Plugin system
   - Batch operations estese
   - Integrazione cloud storage

2. **Distribuzione**
   - Packaging per diverse piattaforme
   - Auto-updater
   - Installer professionale

## 7. Guida alla Manutenzione

### **Procedure di Debug**
1. **Log Analysis**: Controllare `logs/photodrop.log` per errori
2. **Performance**: Monitorare uso memoria cache
3. **UI Issues**: Verificare connessioni segnali/slot

### **Gestione Errori Comuni**
1. **Import Errors**: Verificare requirements.txt e installazione PySide6
2. **Cache Issues**: Svuotare cache disco tramite menu opzioni
3. **EXIF Problems**: Controllare piexif installation

### **Best Practices**
1. **Backup**: Sempre backup prima modifiche strutturali
2. **Testing**: Testare su diverse risoluzioni e formati immagine
3. **Performance**: Monitorare memoria durante operazioni batch
4. **Documentation**: Aggiornare app_map.md ad ogni modifica significativa

---
*Questo file deve essere mantenuto aggiornato ad ogni modifica significativa della struttura del progetto.*

## 6. Mappatura del Progetto Aggiornata

### Root Directory: `c:\xampp\htdocs\progetti\photodrop`

*   `.cache/` (Directory) - Cache per vari dati temporanei.
*   `.cursorrules` (File, 4412 bytes) - Regole specifiche dell'editor/IDE.
*   `.git/` (Directory) - Repository Git.
*   `.trae/` (Directory) - Probabilmente dati specifici di uno strumento di sviluppo o environment.
*   `.windsurfrules` (File, 4672 bytes) - Regole specifiche per l'ambiente Windsurf/Cascade.
*   `assets/` (Directory) - Risorse statiche dell'applicazione.
    *   `icons/` (Directory) - Icone SVG utilizzate nell'interfaccia.
        *   `arrow_down_duotone.svg` (File, 361 bytes)
        *   `arrow_left_duotone.svg` (File, 255 bytes)
        *   `arrow_right_duotone.svg` (File, 253 bytes)
        *   `arrow_up_duotone.svg` (File, 357 bytes)
        *   `close_modern.svg` (File, 235 bytes)
        *   `crop_duotone.svg` (File, 559 bytes)
        *   `folder_duotone.svg` (File, 248 bytes)
        *   `fullscreen_duotone.svg` (File, 251 bytes)
        *   `help_duotone.svg` (File, 928 bytes)
        *   `next_duotone.svg` (File, 253 bytes)
        *   `prev_duotone.svg` (File, 255 bytes)
        *   `rename_duotone.svg` (File, 333 bytes)
        *   `rotate_left_duotone.svg` (File, 452 bytes)
        *   `rotate_right_duotone.svg` (File, 451 bytes)
        *   `trash_duotone.svg` (File, 770 bytes)
        *   `zoom_in_duotone.svg` (File, 381 bytes)
        *   `zoom_out_duotone.svg` (File, 303 bytes)
        *   `zoom_reset_duotone.svg` (File, 385 bytes)
*   `backup/` (Directory) - Cartella per backup (contenuto specifico non listato).
*   `controllers/` (Directory) - Logica di controllo che collega UI e modelli.
    *   `__pycache__/` (Directory) - Cache bytecode Python.
    *   `image_list_controller.py` (File, 9471 bytes) - Gestisce la lista di immagini e le interazioni.
    *   `image_preview_controller.py` (File, 3316 bytes) - Gestisce l'anteprima dell'immagine.
    *   `main_controller.py` (File, 2722 bytes) - Controller principale dell'applicazione, coordina gli altri controller.
    *   `metadata_controller.py` (File, 2037 bytes) - Gestisce la visualizzazione e modifica dei metadati.
*   `core/` (Directory) - Componenti centrali e logica di basso livello.
    *   `__pycache__/` (Directory) - Cache bytecode Python.
    *   `image_cache.py` (File, 16309 bytes) - Sistema di caching per le immagini caricate.
    *   `image_operations.py` (File, 10782 bytes) - Funzioni per la manipolazione delle immagini (es. ritaglio, rotazione).
*   `docs/` (Directory) - Documentazione del progetto.
    *   `README.md` (File, 6499 bytes) - Descrizione generale del progetto.
    *   `albero_progetto.txt` (File, 3005 bytes) - Mappa testuale della struttura del progetto (potrebbe essere ridondante con app_map.md).
    *   `app_map.md` (File, 5074 bytes) - **Questo file, mappa dettagliata della struttura del progetto.**
    *   `app_tree.txt` (File, 2968 bytes) - Altra mappa testuale (potrebbe essere ridondante).
    *   `architettura.md` (File, 11174 bytes) - Documentazione sull'architettura dell'applicazione.
    *   `distribuzione.md` (File, 3969 bytes) - Informazioni sulla distribuzione dell'applicazione.
    *   `guida-uso.md` (File, 16128 bytes) - Guida utente per l'applicazione.
    *   `help.md` (File, 3371 bytes) - Contenuto per la finestra di dialogo di aiuto.
    *   `ottimizzazioni-riassunto.md` (File, 7466 bytes) - Riassunto delle ottimizzazioni implementate.
    *   `todo_list.md` (File, 4860 bytes) - Lista delle funzionalità da implementare o bug da correggere.
*   `logs/` (Directory) - File di log generati dall'applicazione (contenuto specifico non listato).
*   `main.py` (File, 2827 bytes) - Script principale per avviare l'applicazione PhotoDrop.
*   `models/` (Directory) - Modelli di dati dell'applicazione.
    *   `__pycache__/` (Directory) - Cache bytecode Python.
    *   `file_renamer.py` (File, 9030 bytes) - Logica per la rinomina dei file basata su pattern e metadati.
    *   `image_item.py` (File, 594 bytes) - Rappresenta un singolo item immagine nella lista.
    *   `image_model.py` (File, 4124 bytes) - Modello dati che rappresenta un'immagine e le sue proprietà.
*   `requirements.txt` (File, 74 bytes) - Lista delle dipendenze Python del progetto.
*   `run_app.bat` (File, 30 bytes) - Script batch per eseguire comodamente l'applicazione.
*   `services/` (Directory) - Servizi utilizzati dall'applicazione.
    *   `__pycache__/` (Directory) - Cache bytecode Python.
    *   `image_loader.py` (File, 5207 bytes) - Servizio per il caricamento (probabilmente asincrono) delle immagini.
*   `ui/` (Directory) - Componenti dell'interfaccia utente (basati su PySide6).
    *   `__init__.py` (File, 1286 bytes) - Inizializzazione del modulo UI.
    *   `__pycache__/` (Directory) - Cache bytecode Python.
    *   `dialogs/` (Directory) - Finestre di dialogo personalizzate.
        *   `__pycache__/` (Directory) - Cache bytecode Python.
        *   `crop_dialog.py` (File, 10153 bytes) - Finestra di dialogo per la funzionalità di ritaglio immagine.
        *   `help_dialog.py` (File, 11872 bytes) - Finestra di dialogo per visualizzare la guida (contenuto da `docs/help.md`).
    *   `left_panel.py` (File, 48645 bytes) - Gestisce il pannello sinistro con la lista/griglia delle miniature delle immagini.
    *   `main_window.py` (File, 23109 bytes) - Finestra principale dell'applicazione che assembla i vari pannelli e widget.
    *   `right_sidebar.py` (File, 5114 bytes) - Pannello/barra laterale destra, probabilmente per metadati o controlli aggiuntivi.
    *   `styles.py` (File, 17172 bytes) - Fogli di stile (QSS) per personalizzare l'aspetto dell'applicazione.
    *   `views/` (Directory) - Viste specifiche o pannelli complessi.
        *   `panels/` (Directory) - Sotto-pannelli specifici.
            *   `__pycache__/` (Directory) - Cache bytecode Python.
            *   `right_panel.py` (File, 23411 bytes) - Gestisce il pannello destro, tipicamente per l'anteprima dell'immagine selezionata e i relativi controlli.
        *   `widgets/` (Directory) - (Attualmente vuota) Potrebbe contenere widget riutilizzabili specifici delle viste.
    *   `widgets/` (Directory) - Widget UI personalizzati e riutilizzabili.
        *   `__init__.py` (File, 257 bytes) - Inizializzazione del modulo widgets.
        *   `__pycache__/` (Directory) - Cache bytecode Python.
        *   `crop_widget.py` (File, 23512 bytes) - Widget specifico per l'interfaccia di ritaglio dell'immagine.
*   `utils/` (Directory) - Utilità e funzioni di supporto.
    *   `__pycache__/` (Directory) - Cache bytecode Python.
    *   `constants.py` (File, 714 bytes) - Definizioni di costanti usate nel progetto.
    *   `exif_utils.py` (File, 6770 bytes) - Utilità per leggere e scrivere metadati EXIF dalle immagini.
    *   `image_utils.py` (File, 13375 bytes) - Funzioni di utilità generiche per la manipolazione e gestione delle immagini.
    *   `logging_config.py` (File, 761 bytes) - Configurazione del sistema di logging per l'applicazione.

## Descrizione Generale dei Componenti Principali

*   **`main.py`**: Punto di ingresso dell'applicazione, inizializza e avvia la `MainWindow`.
*   **`controllers/`**: Collegano l'interfaccia utente (`ui/`) con la logica di business e i dati (`models/`, `core/`).
*   **`core/`**: Contiene la logica fondamentale non direttamente legata all'interfaccia, come la gestione della cache delle immagini e le operazioni su di esse.
*   **`models/`**: Definiscono la struttura dei dati (es. `ImageItem`, `ImageModel`) e la logica di manipolazione dei dati (es. `FileRenamer`).
*   **`services/`**: Forniscono funzionalità specifiche, come il caricamento delle immagini (`ImageLoader`).
*   **`ui/`**: Contiene tutti i componenti visivi. `MainWindow` è il contenitore principale. `LeftPanel` e `RightPanel` (in `ui/views/panels/`) sono i due componenti maggiori dell'interfaccia. `Dialogs` e `Widgets` forniscono UI specializzate.
*   **`utils/`**: Moduli di supporto per operazioni comuni come la gestione EXIF, utilità per immagini e configurazione del logging.
*   **`assets/`**: Risorse grafiche.
*   **`docs/`**: Documentazione varia sul progetto.

---
