#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Definizione degli stili CSS per l'interfaccia utente dell'applicazione.
"""

class Styles:
    """Classe contenente gli stili CSS per l'applicazione - Design System Moderno v2.0"""

    # 🎨 PALETTE COLORI MODERNA - Design System 2.0
    # Sfondo e superfici
    SURFACE_PRIMARY = "#0f0f0f"      # Sfondo principale ultra-scuro
    SURFACE_SECONDARY = "#1a1a1a"    # Pannelli e contenitori
    SURFACE_TERTIARY = "#242424"     # Elementi elevati
    SURFACE_INTERACTIVE = "#2d2d2d"  # Elementi interattivi
    SURFACE_HOVER = "#353535"        # Stato hover
    SURFACE_PRESSED = "#404040"      # Stato pressed
    SURFACE_SELECTED = "#2a4a6b"     # Elementi selezionati

    # Colori di accento e brand
    ACCENT_PRIMARY = "#0078d7"       # Blu Microsoft moderno
    ACCENT_SECONDARY = "#106ebe"     # Blu più scuro per contrasto
    ACCENT_TERTIARY = "#4a9eff"      # Blu più chiaro per highlights
    SUCCESS = "#107c10"              # Verde per successo
    WARNING = "#ff8c00"              # Arancione per avvisi
    ERROR = "#d13438"                # Rosso per errori
    INFO = "#0078d7"                 # Blu per informazioni

    # Testo e contenuto
    TEXT_PRIMARY = "#ffffff"         # Testo principale
    TEXT_SECONDARY = "#e1e1e1"       # Testo secondario
    TEXT_TERTIARY = "#b3b3b3"        # Testo terziario
    TEXT_DISABLED = "#6d6d6d"        # Testo disabilitato
    TEXT_ON_ACCENT = "#ffffff"       # Testo su colori di accento

    # Bordi e separatori
    BORDER_PRIMARY = "#404040"       # Bordi principali
    BORDER_SECONDARY = "#2d2d2d"     # Bordi secondari
    BORDER_FOCUS = "#0078d7"         # Bordi in focus
    DIVIDER = "#2d2d2d"             # Linee di separazione

    # Ombre e elevazione
    SHADOW_LIGHT = "rgba(0, 0, 0, 0.1)"
    SHADOW_MEDIUM = "rgba(0, 0, 0, 0.2)"
    SHADOW_HEAVY = "rgba(0, 0, 0, 0.4)"

    # 📝 TYPOGRAPHY MODERNA
    FONT_FAMILY_PRIMARY = "'Segoe UI Variable', 'Segoe UI', system-ui, -apple-system, sans-serif"
    FONT_FAMILY_MONOSPACE = "'Cascadia Code', 'Consolas', 'Courier New', monospace"

    # Dimensioni font
    FONT_SIZE_CAPTION = "11px"       # Caption text
    FONT_SIZE_BODY = "13px"          # Body text standard
    FONT_SIZE_BODY_LARGE = "15px"    # Body text large
    FONT_SIZE_SUBTITLE = "16px"      # Subtitle
    FONT_SIZE_TITLE = "20px"         # Title
    FONT_SIZE_DISPLAY = "28px"       # Display text

    # Pesi font
    FONT_WEIGHT_LIGHT = "300"
    FONT_WEIGHT_REGULAR = "400"
    FONT_WEIGHT_MEDIUM = "500"
    FONT_WEIGHT_SEMIBOLD = "600"
    FONT_WEIGHT_BOLD = "700"

    # 📐 SPACING SYSTEM
    SPACING_XS = "4px"
    SPACING_SM = "8px"
    SPACING_MD = "12px"
    SPACING_LG = "16px"
    SPACING_XL = "24px"
    SPACING_XXL = "32px"
    SPACING_XXXL = "48px"

    # 🔄 BORDER RADIUS
    RADIUS_SM = "4px"
    RADIUS_MD = "6px"
    RADIUS_LG = "8px"
    RADIUS_XL = "12px"
    RADIUS_ROUND = "50%"

    # ⚡ TRANSIZIONI
    TRANSITION_FAST = "0.15s ease-out"
    TRANSITION_NORMAL = "0.25s ease-out"
    TRANSITION_SLOW = "0.35s ease-out"

    # 📏 DIMENSIONI COMPONENTI
    TOPBAR_HEIGHT = "48px"
    SIDEBAR_WIDTH = "280px"
    SIDEBAR_WIDTH_COMPACT = "240px"
    BUTTON_HEIGHT_SM = "28px"
    BUTTON_HEIGHT_MD = "32px"
    BUTTON_HEIGHT_LG = "40px"

    # 📱 RESPONSIVE BREAKPOINTS
    BREAKPOINT_MOBILE = "768px"
    BREAKPOINT_TABLET = "1024px"
    BREAKPOINT_DESKTOP = "1440px"

    # 📐 LAYOUT RESPONSIVE
    PANEL_MIN_WIDTH = "200px"
    PANEL_MAX_WIDTH = "400px"
    CONTENT_MIN_WIDTH = "300px"

    # Compatibilità con codice esistente (deprecati ma mantenuti)
    DARK_1 = SURFACE_PRIMARY
    DARK_2 = SURFACE_SECONDARY
    DARK_3 = SURFACE_TERTIARY
    DARK_4 = SURFACE_INTERACTIVE
    ACCENT = ACCENT_PRIMARY
    TEXT_MAIN = TEXT_PRIMARY
    TEXT_SECONDARY = TEXT_SECONDARY
    BORDER_COLOR = BORDER_PRIMARY
    HIGHLIGHT = SURFACE_SELECTED
    HOVER = SURFACE_HOVER
    PRESSED = SURFACE_PRESSED
    
    # 🎨 STILE PRINCIPALE MODERNO - Design System 2.0
    MAIN_STYLE = f"""
        /* === STILE BASE APPLICAZIONE === */
        QWidget {{
            background-color: {SURFACE_PRIMARY};
            color: {TEXT_PRIMARY};
            font-family: {FONT_FAMILY_PRIMARY};
            font-size: {FONT_SIZE_BODY};
            font-weight: {FONT_WEIGHT_REGULAR};
            selection-background-color: {ACCENT_PRIMARY};
            selection-color: {TEXT_ON_ACCENT};
        }}

        /* === FINESTRA PRINCIPALE === */
        QMainWindow {{
            background-color: {SURFACE_PRIMARY};
            border: none;
        }}

        QMainWindow::separator {{
            width: 0;
            background: transparent;
        }}

        /* === CONTENITORI E PANNELLI === */
        QFrame {{
            background-color: {SURFACE_SECONDARY};
            border: none;
            border-radius: {RADIUS_MD};
            padding: 0;
        }}

        /* === SPLITTER MODERNO === */
        QSplitter::handle {{
            background: {DIVIDER};
            width: 1px;
            height: 1px;
        }}

        QSplitter::handle:hover {{
            background: {BORDER_FOCUS};
        }}

        /* === LAYOUT PANNELLI === */
        #leftPanel {{
            background-color: {SURFACE_SECONDARY};
            border-right: 1px solid {DIVIDER};
        }}

        #rightPanel {{
            background-color: {SURFACE_PRIMARY};
        }}

        #rightSidebar {{
            background-color: {SURFACE_SECONDARY};
            border-left: 1px solid {DIVIDER};
        }}
        
        /* Elementi di input */
        QLineEdit, QTextEdit, QPlainTextEdit, QSpinBox, QComboBox, QDateEdit, QTimeEdit, QDateTimeEdit {{
            background-color: transparent;
            border: 1px solid {BORDER_COLOR};
            border-radius: 4px;
            padding: 6px 8px;
            color: {TEXT_MAIN};
            selection-background-color: {ACCENT};
            selection-color: white;
        }}
        
        QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {{
            border: 1px solid {ACCENT};
        }}
        
        /* Pulsanti */
        QPushButton {{
            background-color: {DARK_3};
            color: {TEXT_MAIN};
            border: 1px solid {BORDER_COLOR};
            border-radius: 6px;
            padding: 6px 12px;
            font-weight: 500;
            min-width: 80px;
        }}
        
        QPushButton:hover {{
            background-color: {HOVER};
            border-color: {BORDER_COLOR};
        }}
        
        QPushButton:pressed {{
            background-color: {PRESSED};
            border-color: {ACCENT};
        }}
        
        QPushButton:disabled {{
            background-color: #1a1a1a;
            color: #505050;
            border-color: #2d2d2d;
        }}
        
        /* Barra di stato */
        QStatusBar {{
            background: {DARK_2};
            color: {TEXT_SECONDARY};
            border-top: 1px solid {BORDER_COLOR};
            padding: 2px;
        }}
        
        /* Barra di avanzamento */
        QProgressBar {{
            height: 16px;
            text-align: center;
            background: {DARK_3};
            border: 1px solid {BORDER_COLOR};
            border-radius: 8px;
        }}
        
        QProgressBar::chunk {{
            background-color: {ACCENT};
            border-radius: 6px;
        }}
        
        /* Lista elementi */
        QListWidget, QTreeView, QTableView {{
            background-color: {DARK_3};
            border: 1px solid {BORDER_COLOR};
            border-radius: 4px;
            padding: 2px;
            outline: 0;
        }}
        
        QListWidget::item, QTreeWidget::item, QTableWidget::item {{
            padding: 6px 8px;
            border-radius: 4px;
            margin: 1px 2px;
        }}
        
        QListWidget::item:selected, QTreeWidget::item:selected, QTableWidget::item:selected {{
            background-color: {HIGHLIGHT};
            color: white;
        }}
        
        QListWidget::item:hover, QTreeWidget::item:hover, QTableWidget::item:hover {{
            background-color: {HOVER};
        }}
        
        /* Slider */
        QSlider::groove:horizontal {{
            border: 1px solid {BORDER_COLOR};
            background: {DARK_2};
            height: 6px;
            border-radius: 3px;
        }}
        
        QSlider::handle:horizontal {{
            background: #242424;
            border: 1px solid #666;
            width: 16px;
            margin: -6px 0;
            border-radius: 8px;
        }}
        
        QSlider::handle:horizontal:hover {{
            background: {TEXT_MAIN};
            border-color: {ACCENT};
        }}
        
        QSlider::add-page:horizontal {{
            background: {DARK_2};
            border: 1px solid {BORDER_COLOR};
            border-radius: 3px;
        }}
         
        QSlider::sub-page:horizontal {{
            background: {ACCENT};
            border: 1px solid {ACCENT};
            border-radius: 3px;
        }}
        
        /* Tab Widget */
        QTabWidget::pane {{
            border: 1px solid {BORDER_COLOR};
            border-top: none;
            padding: 0px;
            margin: 0px;
        }}
        
        QTabBar::tab {{
            background: {DARK_3};
            color: {TEXT_SECONDARY};
            border: 1px solid {BORDER_COLOR};
            border-bottom: none;
            padding: 6px 12px;
            margin-right: 2px;
            border-top-left-radius: 4px;
            border-top-right-radius: 4px;
        }}
        
        QTabBar::tab:selected, QTabBar::tab:hover {{
            background: {DARK_2};
            color: {TEXT_MAIN};
            border-color: {BORDER_COLOR};
            border-bottom-color: {DARK_2};
        }}
        
        QTabBar::tab:selected {{
            font-weight: bold;
        }}
    """
    
    @classmethod
    def get_left_panel_style(cls):
        return f"""
        QFrame#leftPanel {{
            background-color: {cls.DARK_2};
            border-right: 1px solid {cls.BORDER_COLOR};
            border-top: none;
            border-bottom: none;
            border-left: none;
            border-top-left-radius: 0;
            border-bottom-left-radius: 0;
        }}
        
        QListWidget {{
            background-color: transparent;
            border: none;
            border-radius: 0;
            padding: 4px;
            outline: none;
        }}
        
        QListWidget::item {{
            background-color: {cls.DARK_3};
            border: 1px solid {cls.BORDER_COLOR};
            border-radius: 2px;
            margin: 2px;
            padding: 4px;
        }}
        
        /* Stile per le miniature delle immagini */
        QLabel[thumbnail="true"] {{
            background-color: transparent;
            border: none;
            padding: 0;
            margin: 0;
        }}
        
        QListWidget::item:selected {{
            background-color: {cls.HIGHLIGHT};
            border: 1px solid {cls.ACCENT};
            color: white;
        }}
        
        QListWidget::item:hover {{
            background-color: {cls.HOVER};
            border: 1px solid {cls.ACCENT};
        }}
        
        QScrollBar:vertical {{
            border: none;
            background: {cls.DARK_2};
            width: 10px;
            margin: 0px;
        }}
        
        QScrollBar::handle:vertical {{
            background: {cls.DARK_4};
            min-height: 20px;
            border-radius: 5px;
        }}
        
        QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {{
            height: 0px;
        }}
    """
    
    # Stile specifico per il pannello sinistro (proprietà per compatibilità)
    LEFT_PANEL_STYLE = ""  # Sarà popolato nel metodo __init_subclass__
    
    @classmethod
    def get_right_panel_style(cls):
        return f"""
        QFrame#rightPanel {{
            background-color: {cls.DARK_1};
            border: none;
        }}
        
        QLabel#previewLabel {{
            background-color: {cls.DARK_1};
            border: none;
            border-radius: 0;
            padding: 0;
            margin: 0;
        }}
        
        /* Stile per la barra degli strumenti dell'anteprima */
        QToolBar {{
            background-color: {cls.DARK_2};
            border: none;
            border-bottom: 1px solid {cls.BORDER_COLOR};
            padding: 4px;
            spacing: 4px;
        }}
        
        QToolBar QToolButton {{
            background-color: transparent;
            border: 1px solid transparent;
            border-radius: 4px;
            padding: 4px;
        }}
        
        QToolBar QToolButton:hover {{
            background-color: {cls.HOVER};
            border: 1px solid {cls.BORDER_COLOR};
        }}
        
        QToolBar QToolButton:pressed {{
            background-color: {cls.PRESSED};
        }}
    """
    
    # Stile specifico per il pannello destro (proprietà per compatibilità)
    RIGHT_PANEL_STYLE = ""  # Sarà popolato nel metodo __init_subclass__
    
    @classmethod
    def get_action_button_style(cls):
        return f"""
        QPushButton {{
            background-color: {cls.ACCENT};
            color: white;
            border: none;
            border-radius: 6px;
            padding: 8px 16px;
            font-weight: 500;
            min-width: 100px;
        }}
        QPushButton:hover {{
            background-color: #1a8cff;
        }}
        QPushButton:pressed {{
            background-color: #0066cc;
        }}
        QPushButton:disabled {{
            background-color: #1a3d5c;
            color: #6b8ba3;
        }}
    """
    
    # Stile per i pulsanti di azione (proprietà per compatibilità)
    ACTION_BUTTON_STYLE = ""  # Sarà popolato nel metodo __init_subclass__
    
    @classmethod
    def get_danger_button_style(cls):
        return f"""
        QPushButton {{
            background-color: #c42b1c;
            color: white;
            border: none;
            border-radius: 6px;
            padding: 8px 16px;
            font-weight: 500;
            min-width: 100px;
        }}
        QPushButton:hover {{
            background-color: #e04a3f;
        }}
        QPushButton:pressed {{
            background-color: #a32012;
        }}
        QPushButton:disabled {{
            background-color: #4a1f1a;
            color: #8a5e59;
        }}
    """
    
    # Stile per i pulsanti di pericolo (proprietà per compatibilità)
    DANGER_BUTTON_STYLE = ""  # Sarà popolato nel metodo __init_subclass__
    
    @classmethod
    def get_secondary_button_style(cls):
        return f"""
        QPushButton {{
            background-color: {cls.DARK_3};
            color: {cls.TEXT_MAIN};
            border: 1px solid {cls.BORDER_COLOR};
            border-radius: 6px;
            padding: 8px 16px;
            font-weight: 500;
            min-width: 100px;
        }}
        QPushButton:hover {{
            background-color: {cls.HOVER};
            border-color: {cls.ACCENT};
        }}
        QPushButton:pressed {{
            background-color: {cls.PRESSED};
        }}
        QPushButton:disabled {{
            background-color: #1a1a1a;
            color: #505050;
            border-color: #2d2d2d;
        }}
    """
    
    # Stile per i pulsanti secondari (proprietà per compatibilità)
    SECONDARY_BUTTON_STYLE = ""  # Sarà popolato nel metodo __init_subclass__
    
    @classmethod
    def get_close_button_style(cls):
        return f"""
        QPushButton {{
            background-color: transparent;
            color: {cls.TEXT_SECONDARY};
            border: 1px solid transparent;
            border-radius: 4px;
            padding: 4px 8px;
            min-width: 24px;
            max-width: 24px;
            min-height: 24px;
            max-height: 24px;
        }}
        QPushButton:hover {{
            background-color: #e81123;
            color: white;
        }}
        QPushButton:pressed {{
            background-color: #a0111a;
        }}
    """
    
    # Stile per i pulsanti di chiusura (proprietà per compatibilità)
    CLOSE_BUTTON_STYLE = ""  # Sarà popolato nel metodo __init_subclass__
    
    @classmethod
    def get_status_label_style(cls):
        return f"""
        QLabel {{
            background-color: {cls.DARK_3};
            color: {cls.TEXT_MAIN};
            border: 1px solid {cls.BORDER_COLOR};
            border-radius: 4px;
            padding: 6px 10px;
            margin: 2px;
        }}
        
        QLabel[status="info"] {{
            border-left: 3px solid {cls.ACCENT};
        }}
        
        QLabel[status="warning"] {{
            border-left: 3px solid #ffcc00;
        }}
        
        QLabel[status="error"] {{
            border-left: 3px solid #ff4444;
        }}
        
        /* Stile per il percorso dell'immagine */
        QLabel#pathLabel {{
            background-color: {cls.DARK_2};
            color: {cls.TEXT_SECONDARY};
            border: none;
            border-bottom: 1px solid {cls.BORDER_COLOR};
            border-radius: 0;
            padding: 4px 8px;
            font-size: 12px;
            font-family: 'Consolas', 'Courier New', monospace;
        }}
        
        /* Stile per il footer */
        QStatusBar {{
            background-color: {cls.DARK_2};
            color: {cls.TEXT_SECONDARY};
            border-top: 1px solid {cls.BORDER_COLOR};
            min-height: 24px;
        }}
        
        QStatusBar QLabel {{
            background-color: transparent;
            border: none;
            padding: 2px 8px;
            font-size: 12px;
        }}
    """
    
    # Stile per i messaggi di stato (proprietà per compatibilità)
    STATUS_LABEL_STYLE = ""  # Sarà popolato nel metodo __init_subclass__
    
    @classmethod
    def get_tooltip_style(cls):
        return f"""
        QToolTip {{
            background-color: {cls.DARK_3};
            color: {cls.TEXT_MAIN};
            border: 1px solid {cls.BORDER_COLOR};
            border-radius: 4px;
            padding: 6px 10px;
            opacity: 240;
            font-size: 12px;
        }}
    """
    
    # Stile tooltip migliorato (proprietà per compatibilità)
    TOOLTIP_STYLE = ""  # Sarà popolato nel metodo __init_subclass__
    
    @classmethod
    def get_icon_button_style(cls):
        return f"""
        QPushButton {{
            background-color: transparent;
            border: 1px solid transparent;
            border-radius: 4px;
            padding: 8px;
            min-width: 36px;
            min-height: 36px;
        }}
        QPushButton:hover {{
            background-color: {cls.HOVER};
            border: 1px solid {cls.BORDER_COLOR};
        }}
        QPushButton:pressed {{
            background-color: {cls.PRESSED};
            border: 1px solid {cls.ACCENT};
        }}
        QPushButton:checked {{
            background-color: {cls.HIGHLIGHT};
            border: 1px solid {cls.ACCENT};
        }}
        QPushButton:disabled {{
            background-color: transparent;
            color: #505050;
        }}
    """
    
    # Stile specifico per i pulsanti con icone (proprietà per compatibilità)
    ICON_BUTTON_STYLE = ""  # Sarà popolato nel metodo __init_subclass__
    
    # 🔝 TOPBAR MODERNA
    @classmethod
    def get_topbar_style(cls):
        return f"""
        /* === TOPBAR CONTAINER === */
        #topbar {{
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 {cls.SURFACE_TERTIARY},
                stop:1 {cls.SURFACE_SECONDARY});
            border: none;
            border-bottom: 1px solid {cls.DIVIDER};
            min-height: {cls.TOPBAR_HEIGHT};
            max-height: {cls.TOPBAR_HEIGHT};
            padding: 0 {cls.SPACING_LG};
        }}

        /* === LOGO E BRAND === */
        #topbar #brandLabel {{
            color: {cls.TEXT_PRIMARY};
            font-size: {cls.FONT_SIZE_TITLE};
            font-weight: {cls.FONT_WEIGHT_SEMIBOLD};
            padding: 0 {cls.SPACING_LG} 0 0;
            background: transparent;
            border: none;
        }}

        /* === MENU NAVIGATION === */
        #topbar #navMenu {{
            background: transparent;
            border: none;
            spacing: {cls.SPACING_SM};
        }}

        #topbar #navMenu QPushButton {{
            background: transparent;
            color: {cls.TEXT_SECONDARY};
            border: none;
            border-radius: {cls.RADIUS_MD};
            padding: {cls.SPACING_SM} {cls.SPACING_LG};
            font-size: {cls.FONT_SIZE_BODY};
            font-weight: {cls.FONT_WEIGHT_MEDIUM};
            min-width: 80px;
        }}

        #topbar #navMenu QPushButton:hover {{
            background-color: {cls.SURFACE_HOVER};
            color: {cls.TEXT_PRIMARY};
        }}

        #topbar #navMenu QPushButton:pressed {{
            background-color: {cls.SURFACE_PRESSED};
        }}

        #topbar #navMenu QPushButton:checked {{
            background-color: {cls.ACCENT_PRIMARY};
            color: {cls.TEXT_ON_ACCENT};
        }}

        /* === MENU CONTESTUALI === */
        #topbar #contextualMenu {{
            background: transparent;
            border: none;
            spacing: {cls.SPACING_XS};
        }}

        #topbar #contextualMenu QPushButton {{
            background: transparent;
            color: {cls.TEXT_TERTIARY};
            border: none;
            border-radius: {cls.RADIUS_SM};
            padding: {cls.SPACING_XS} {cls.SPACING_MD};
            font-size: {cls.FONT_SIZE_CAPTION};
            font-weight: {cls.FONT_WEIGHT_REGULAR};
        }}

        #topbar #contextualMenu QPushButton:hover {{
            background-color: {cls.SURFACE_HOVER};
            color: {cls.TEXT_SECONDARY};
        }}

        /* === USER AREA === */
        #topbar #userArea {{
            background: transparent;
            border: none;
            spacing: {cls.SPACING_SM};
        }}

        #topbar #userArea QPushButton {{
            background: transparent;
            color: {cls.TEXT_SECONDARY};
            border: 1px solid transparent;
            border-radius: {cls.RADIUS_MD};
            padding: {cls.SPACING_XS};
            min-width: {cls.BUTTON_HEIGHT_MD};
            max-width: {cls.BUTTON_HEIGHT_MD};
            min-height: {cls.BUTTON_HEIGHT_MD};
            max-height: {cls.BUTTON_HEIGHT_MD};
        }}

        #topbar #userArea QPushButton:hover {{
            background-color: {cls.SURFACE_HOVER};
            border-color: {cls.BORDER_SECONDARY};
        }}

        #topbar #userArea QPushButton:pressed {{
            background-color: {cls.SURFACE_PRESSED};
        }}

        /* === SEPARATORI === */
        #topbar .separator {{
            background-color: {cls.DIVIDER};
            width: 1px;
            margin: {cls.SPACING_SM} 0;
        }}

        /* === RESPONSIVE DESIGN === */
        /* Modalità compatta per schermi piccoli */
        #topbar.compact {{
            min-height: 40px;
            max-height: 40px;
            padding: 0 {cls.SPACING_MD};
        }}

        #topbar.compact #brandLabel {{
            font-size: {cls.FONT_SIZE_SUBTITLE};
        }}

        #topbar.compact #navMenu QPushButton {{
            padding: {cls.SPACING_XS} {cls.SPACING_MD};
            min-width: 60px;
            font-size: {cls.FONT_SIZE_CAPTION};
        }}

        #topbar.compact #contextualMenu QPushButton {{
            padding: {cls.SPACING_XS};
        }}

        #topbar.compact #userArea QPushButton {{
            min-width: 28px;
            max-width: 28px;
            min-height: 28px;
            max-height: 28px;
        }}
        """

    # Inizializza le proprietà statiche
    @classmethod
    def initialize(cls):
        # Popola le proprietà statiche con i valori generati dai metodi
        cls.LEFT_PANEL_STYLE = cls.get_left_panel_style()
        cls.RIGHT_PANEL_STYLE = cls.get_right_panel_style()
        cls.ACTION_BUTTON_STYLE = cls.get_action_button_style()
        cls.DANGER_BUTTON_STYLE = cls.get_danger_button_style()
        cls.SECONDARY_BUTTON_STYLE = cls.get_secondary_button_style()
        cls.CLOSE_BUTTON_STYLE = cls.get_close_button_style()
        cls.STATUS_LABEL_STYLE = cls.get_status_label_style()
        cls.TOOLTIP_STYLE = cls.get_tooltip_style()
        cls.ICON_BUTTON_STYLE = cls.get_icon_button_style()
        cls.TOPBAR_STYLE = cls.get_topbar_style()
    
    @classmethod
    def get_combined_style(cls):
        """
        Restituisce lo stile CSS combinato per l'applicazione.

        Returns:
            str: Lo stile CSS combinato
        """
        return (
            cls.MAIN_STYLE +
            cls.TOPBAR_STYLE +
            cls.LEFT_PANEL_STYLE +
            cls.RIGHT_PANEL_STYLE +
            cls.ACTION_BUTTON_STYLE +
            cls.DANGER_BUTTON_STYLE +
            cls.SECONDARY_BUTTON_STYLE +
            cls.CLOSE_BUTTON_STYLE +
            cls.STATUS_LABEL_STYLE +
            cls.TOOLTIP_STYLE +
            cls.ICON_BUTTON_STYLE
        )

# Inizializza gli stili statici
Styles.initialize()
