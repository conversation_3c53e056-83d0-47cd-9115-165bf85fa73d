#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Implementazione della finestra principale dell'applicazione.
Coordina i vari pannelli e componenti.
"""

import logging
import os
from typing import Optional

from PySide6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QSplitter,
    QMessageBox, QStatusBar, QLabel, QHBoxLayout, QPushButton,
    QMenu, QInputDialog, QDialog
)
from PySide6.QtCore import Qt, QSize
from PySide6.QtGui import QIcon, QCloseEvent, QShortcut, QKeySequence, QAction

from ui.left_panel import LeftPanel
from ui.views.panels.right_panel import RightPanel
from ui.right_sidebar import RightSidebar
from ui.dialogs.settings_dialog import SettingsDialog
from ui.styles import Styles
from ui.components.topbar import ModernTopbar
from core.image_cache import ImageCache
from controllers.main_controller import MainController
from models.settings_model import SettingsModel
from utils.logging_config import get_logger

logger = get_logger("PhotoDrop.MainWindow")

class PhotoDropApp(QMainWindow):
    """
    Finestra principale dell'applicazione PhotoDrop.
    Coordina tutti i componenti dell'interfaccia utente.
    """
    
    def __init__(self):
        """Inizializza la finestra principale."""
        super().__init__()

        # Configurazione finestra principale
        self.setWindowTitle("PhotoDrop - Gestione Foto")
        self.setMinimumSize(800, 600)

        # Inizializza il sistema di impostazioni
        self.settings = SettingsModel()

        # Stato dell'applicazione
        self.original_size = QSize(1000, 700)
        self.current_folder = None

        # Inizializza i servizi con impostazioni personalizzate
        cache_enabled = self.settings.get('cache.enable_disk_cache', True)
        cache_dir = "cache/images" if cache_enabled else "cache/images"  # Sempre una stringa valida
        memory_size = self.settings.get('cache.max_memory_items', 1000)
        disk_size_mb = self.settings.get('cache.max_disk_cache_size_mb', 500)
        max_age_days = self.settings.get('cache.max_disk_cache_file_age_days', 30)

        self.image_cache = ImageCache(
            cache_dir=cache_dir,
            memory_size=memory_size,
            disk_size_mb=disk_size_mb,
            max_age_days=max_age_days
        )

        # Controller MVC
        self.controller = MainController(self)

        # Inizializza l'interfaccia
        self._setup_ui()
        self._connect_signals()

        # 🎨 Imposta lo stile globale moderno - Design System 2.0
        self.setStyleSheet(Styles.get_combined_style())

        # Footer
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        
        # Etichetta per il percorso corrente (a sinistra)
        self.path_status_label = QLabel()
        self.path_status_label.setAlignment(Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignVCenter)
        self.status_bar.addWidget(self.path_status_label, 1)

        # Etichetta per il conteggio delle immagini (al centro)
        self.count_status_label = QLabel()
        self.count_status_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.status_bar.addWidget(self.count_status_label, 1)

        # Etichetta per informazioni aggiuntive (a destra)
        self.info_status_label = QLabel()
        self.info_status_label.setAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
        self.status_bar.addWidget(self.info_status_label, 1)
        
        self._update_footer()
        # Icona moderna per la finestra principale
        self.setWindowIcon(QIcon("assets/icons/folder_duotone.svg"))
        
        # Shortcut per schermo intero/uscita
        self.fullscreen_shortcut = QShortcut(QKeySequence("F11"), self)
        self.fullscreen_shortcut.activated.connect(self._toggle_fullscreen_shortcut)
        
        # Shortcut per la guida
        self.help_shortcut = QShortcut(QKeySequence("F1"), self)
        self.help_shortcut.activated.connect(self._show_help_dialog)
        
        self.is_fullscreen = False
        
        # 🔧 NUOVO: Carica geometria finestra dalle impostazioni
        self._load_window_geometry()

        logger.info("Applicazione inizializzata")
    
    def _setup_ui(self):
        """Configura l'interfaccia utente."""
        # Widget principale
        main_widget = QWidget()
        self.setCentralWidget(main_widget)
        main_layout = QVBoxLayout(main_widget)
        main_layout.setContentsMargins(2, 2, 2, 2)  # Margini minimi per l'interfaccia

        # 🔝 TOPBAR MODERNA - Design System 2.0
        self.topbar = ModernTopbar(self)
        main_layout.addWidget(self.topbar)

        # Connetti segnali topbar
        self.topbar.action_triggered.connect(self._handle_topbar_action)
        self.topbar.navigation_changed.connect(self._handle_navigation_change)

        # Crea splitter per pannelli sinistro/destro
        self.splitter = QSplitter(Qt.Orientation.Horizontal)
        self.splitter.setHandleWidth(1)  # Riduci lo spessore della maniglia
        main_layout.addWidget(self.splitter, 1)  # Diamo peso 1 allo splitter per farlo espandere
        
        # Imposta stile per i pannelli
        self.setStyleSheet("""
            QSplitter::handle {
                background-color: #2d2d2d;
                width: 1px;
                height: 1px;
            }
        """)
        
        # Pannello sinistro
        self.left_panel = LeftPanel(self)
        self.splitter.addWidget(self.left_panel)
        
        # Pannello destro con splitter orizzontale per anteprima e sidebar
        right_splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # Inizializza il modello di dati per le immagini
        from models.image_model import ImageModel
        self.image_model = ImageModel(self.image_cache) # Passa l'istanza di ImageCache
        
        # Pannello di anteprima con cache delle immagini
        self.right_panel = RightPanel(
            image_model=self.image_model,
            image_cache=self.image_cache,
            parent=self
        )
        
        # Sidebar per i metadati EXIF
        self.right_sidebar = RightSidebar(self)
        
        # Imposta la sidebar anche nel pannello destro per la comunicazione
        self.right_panel.right_sidebar = self.right_sidebar
        
        # Aggiungi i pannelli allo splitter destro
        right_splitter.addWidget(self.right_panel)
        right_splitter.addWidget(self.right_sidebar)
        
        # Aggiungi lo splitter destro allo splitter principale
        self.splitter.addWidget(right_splitter)
        
        # 🔧 NUOVO: Carica dimensioni splitter dalle impostazioni
        splitter_sizes = self.settings.get('ui.splitter_sizes', [300, 700])
        right_splitter_sizes = self.settings.get('ui.right_splitter_sizes', [600, 200])

        self.splitter.setSizes(splitter_sizes)
        right_splitter.setSizes(right_splitter_sizes)

        # Salva le dimensioni quando cambiano
        self.splitter.splitterMoved.connect(self._on_splitter_moved)
        right_splitter.splitterMoved.connect(self._on_right_splitter_moved)
    
    def _connect_signals(self):
        """Connette i segnali agli slot."""
        # Connetti i segnali dal pannello sinistro
        if self.left_panel and hasattr(self.left_panel, 'selection_changed'):
            self.left_panel.selection_changed.connect(self._on_selection_changed)
            logger.info("[MW._connect_signals] Connesso left_panel.selection_changed a _on_selection_changed")
        else:
            logger.error("[MW._connect_signals] Impossibile connettere left_panel.selection_changed: left_panel o segnale non trovato.")
        
        self.left_panel.folder_changed.connect(self._on_folder_changed)
        self.left_panel.images_loaded.connect(self._on_images_loaded)
        
        # Connetti i segnali del pannello destro
        self.right_panel.fullscreen_toggled.connect(self._toggle_fullscreen)
        self.right_panel.zoom_changed.connect(self._on_zoom_changed)
        self.right_panel.rotation_changed.connect(self._on_rotation_changed)
        self.right_panel.image_loaded.connect(self._on_image_loaded)
        self.right_panel.error_occurred.connect(self._on_error_occurred)
        self.right_panel.image_updated.connect(self.left_panel.update_thumbnail_for_path) # NUOVA CONNESSIONE

        # Connessioni per la navigazione richiesta dal RightPanel
        if hasattr(self.right_panel, 'request_prev_image') and hasattr(self.left_panel, 'show_prev_image'):
            self.right_panel.request_prev_image.connect(self.left_panel.show_prev_image)
        if hasattr(self.right_panel, 'request_next_image') and hasattr(self.left_panel, 'show_next_image'):
            self.right_panel.request_next_image.connect(self.left_panel.show_next_image)

        # Connetti i segnali della sidebar
        if hasattr(self, 'right_sidebar') and self.right_sidebar:
            # Connessioni aggiuntive per la sidebar, se necessario
            pass
    
    def _on_folder_changed(self, folder_path: str):
        """
        Gestisce il cambio della cartella corrente.
        
        Args:
            folder_path (str): Percorso della nuova cartella
        """
        self.controller.on_folder_changed(folder_path)

    def _on_images_loaded(self, image_paths: list):
        """Gestisce il caricamento di una nuova lista di immagini."""
        if self.controller:
            self.controller.on_images_loaded(image_paths)

    def _on_selection_changed(self, image_path: str):
        """
        Gestisce il cambio dell'immagine selezionata.
        
        Args:
            image_path (str): Percorso dell'immagine selezionata
        """
        # self.controller.on_selection_changed(image_path) # Rimosso/Commentato
        if hasattr(self, 'right_panel') and self.right_panel and image_path:
            self.right_panel.show_image(image_path)
            logger.info(f"MainWindow: _on_selection_changed ha chiamato right_panel.show_image con {os.path.basename(image_path) if image_path else 'None'}")
        elif not image_path:
            logger.warning("MainWindow: _on_selection_changed ha ricevuto un image_path vuoto.")
        else:
            logger.warning("MainWindow: _on_selection_changed chiamata ma right_panel non disponibile o image_path vuoto.")
    
    def _on_zoom_changed(self, zoom_factor: float):
        """
        Gestisce il cambio dello zoom dell'immagine.
        
        Args:
            zoom_factor (float): Fattore di zoom corrente
        """
        self.controller.on_zoom_changed(zoom_factor)
    
    def _on_rotation_changed(self, angle: int):
        """
        Gestisce la rotazione dell'immagine.
        
        Args:
            angle (int): Angolo di rotazione in gradi
        """
        self.controller.on_rotation_changed(angle)
    
    def _on_image_loaded(self, image_path: str):
        """
        Gestisce il caricamento di una nuova immagine.
        
        Args:
            image_path (str): Percorso dell'immagine caricata
        """
        # Aggiorna eventuali controlli UI che mostrano il percorso dell'immagine
        if hasattr(self, 'path_status_label'):
            self.path_status_label.setText(f"Immagine: {os.path.basename(image_path)}")
    
    def _on_error_occurred(self, title: str, message: str):
        """
        Gestisce gli errori segnalati dal pannello destro.
        
        Args:
            title (str): Titolo dell'errore
            message (str): Messaggio di errore
        """
        self.controller.on_error_occurred(title, message)
    
    def browse_previous_image(self):
        """Naviga all'immagine precedente nella lista del pannello sinistro."""
        if not self.left_panel or not hasattr(self.left_panel, 'photo_list_widget'):
            logger.debug("PhotoDropApp: Nessun pannello sinistro o photo_list_widget per navigare indietro.")
            return

        if not self.left_panel.photo_list_widget:
            logger.error("PhotoDropApp: photo_list_widget non trovato in left_panel.")
            return

        num_images = self.left_panel.photo_list_widget.count()
        if num_images == 0:
            logger.debug("PhotoDropApp: Nessuna immagine nella lista per navigare.")
            return

        current_row = self.left_panel.photo_list_widget.currentRow()
        # Se nessuna riga è selezionata (currentRow == -1), inizia dall'ultima
        if current_row == -1 and num_images > 0:
            prev_row = num_images - 1
        else:
            prev_row = (current_row - 1 + num_images) % num_images

        self.left_panel.photo_list_widget.setCurrentRow(prev_row)
        logger.debug(f"PhotoDropApp: Navigato all'immagine precedente, riga {prev_row}")

    def browse_next_image(self):
        """Naviga all'immagine successiva nella lista del pannello sinistro."""
        if not self.left_panel or not hasattr(self.left_panel, 'photo_list_widget'):
            logger.debug("PhotoDropApp: Nessun pannello sinistro o photo_list_widget per navigare avanti.")
            return

        if not self.left_panel.photo_list_widget:
            logger.error("PhotoDropApp: photo_list_widget non trovato in left_panel.")
            return

        num_images = self.left_panel.photo_list_widget.count()
        if num_images == 0:
            logger.debug("PhotoDropApp: Nessuna immagine nella lista per navigare.")
            return

        current_row = self.left_panel.photo_list_widget.currentRow()
        # Se nessuna riga è selezionata (currentRow == -1), inizia dalla prima
        if current_row == -1 and num_images > 0:
            next_row = 0
        else:
            next_row = (current_row + 1) % num_images

        self.left_panel.photo_list_widget.setCurrentRow(next_row)
        logger.debug(f"PhotoDropApp: Navigato all'immagine successiva, riga {next_row}")

    def _toggle_fullscreen(self, fullscreen: bool):
        """
        Attiva/disattiva la modalità a schermo intero.
        
        Args:
            fullscreen (bool): True per attivare lo schermo intero
        """
        if fullscreen:
            # Salva le dimensioni normali prima di andare a schermo intero
            self.original_size = self.size()
            self.normal_geometry = self.geometry()  # Salva la geometria attuale
            self.showFullScreen()
            self.is_fullscreen = True
        else:
            self.showNormal()
            if hasattr(self, 'normal_geometry'):
                self.setGeometry(self.normal_geometry)
            self.is_fullscreen = False
    
    def _show_help_dialog(self):
        """Mostra la finestra di dialogo della guida."""
        from ui.dialogs.help_dialog import HelpDialog
        help_dialog = HelpDialog(self)
        help_dialog.exec_()

    def _handle_topbar_action(self, action: str):
        """
        Gestisce le azioni provenienti dalla topbar.

        Args:
            action: ID dell'azione da eseguire
        """
        logger.info(f"Azione topbar ricevuta: {action}")

        # Mappa azioni a metodi esistenti
        action_map = {
            'settings': self._show_settings_dialog,
            'help': self._show_help_dialog,
            'fullscreen': self._toggle_fullscreen_shortcut,
            'close': self.close,
            'refresh': self._refresh_current_folder,
            'view_mode': self._toggle_view_mode,
            'sort': self._show_sort_options,
            'crop': self._crop_current_image,
            'rotate': self._rotate_current_image,
            'rename': self._rename_selected_files,
            'move': self._move_selected_files,
            'delete': self._delete_selected_files
        }

        # Esegui azione se disponibile
        if action in action_map:
            try:
                action_map[action]()
            except Exception as e:
                logger.error(f"Errore nell'esecuzione dell'azione {action}: {e}")
        else:
            logger.warning(f"Azione non riconosciuta: {action}")

    def _handle_navigation_change(self, section: str):
        """
        Gestisce il cambio di sezione nella navigazione.

        Args:
            section: ID della sezione attiva
        """
        logger.info(f"Navigazione cambiata: {section}")

        # Aggiorna UI in base alla sezione
        if section == "browse":
            self._show_browse_mode()
        elif section == "edit":
            self._show_edit_mode()
        elif section == "organize":
            self._show_organize_mode()

    # === METODI PER AZIONI TOPBAR ===
    def _refresh_current_folder(self):
        """Aggiorna la cartella corrente."""
        if hasattr(self.left_panel, '_load_images_async') and self.left_panel.current_folder:
            logger.info("Aggiornamento cartella corrente...")
            self.left_panel._load_images_async(self.left_panel.current_folder)

    def _toggle_view_mode(self):
        """Cambia modalità di visualizzazione delle miniature."""
        if hasattr(self.left_panel, 'thumbnail_slider'):
            current_value = self.left_panel.thumbnail_slider.value()
            # Alterna tra vista piccola (50) e grande (150)
            new_value = 150 if current_value < 100 else 50
            self.left_panel.thumbnail_slider.setValue(new_value)
            logger.info(f"Modalità vista cambiata: {new_value}")

    def _show_sort_options(self):
        """Mostra opzioni di ordinamento."""
        if hasattr(self.left_panel, 'sort_combo'):
            # Cicla tra le opzioni di ordinamento
            current_index = self.left_panel.sort_combo.currentIndex()
            max_index = self.left_panel.sort_combo.count() - 1
            new_index = (current_index + 1) % (max_index + 1)
            self.left_panel.sort_combo.setCurrentIndex(new_index)
            logger.info(f"Ordinamento cambiato: {self.left_panel.sort_combo.currentText()}")

    def _crop_current_image(self):
        """Ritaglia l'immagine corrente."""
        if hasattr(self.right_panel, '_open_crop_dialog'):
            self.right_panel._open_crop_dialog()
        else:
            logger.warning("Funzione ritaglio non disponibile")

    def _rotate_current_image(self):
        """Ruota l'immagine corrente di 90 gradi."""
        if hasattr(self.right_panel, 'rotate_image'):
            self.right_panel.rotate_image(90)  # Ruota di 90 gradi
            logger.info("Immagine ruotata di 90 gradi")
        else:
            logger.warning("Funzione rotazione non disponibile")

    def _rename_selected_files(self):
        """Rinomina i file selezionati."""
        if hasattr(self.left_panel, '_rename_files'):
            self.left_panel._rename_files()
        else:
            logger.warning("Funzione rinomina non disponibile")

    def _move_selected_files(self):
        """Sposta i file selezionati in una nuova cartella."""
        try:
            # Ottieni i file selezionati dal pannello sinistro
            if not hasattr(self.left_panel, 'get_selected_files'):
                logger.warning("Metodo get_selected_files non disponibile nel pannello sinistro")
                QMessageBox.warning(self, "Errore", "Impossibile ottenere i file selezionati")
                return

            selected_files = self.left_panel.get_selected_files()
            if not selected_files:
                QMessageBox.information(self, "Sposta File", "Nessun file selezionato")
                return

            # Chiedi all'utente di selezionare la cartella di destinazione
            from PySide6.QtWidgets import QFileDialog
            destination_folder = QFileDialog.getExistingDirectory(
                self,
                "Seleziona cartella di destinazione",
                self.current_folder or ""
            )

            if not destination_folder:
                return  # Utente ha annullato

            # Conferma l'operazione
            file_count = len(selected_files)
            reply = QMessageBox.question(
                self,
                "Conferma Spostamento",
                f"Vuoi spostare {file_count} file in:\n{destination_folder}?",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )

            if reply == QMessageBox.StandardButton.Yes:
                self._perform_move_operation(selected_files, destination_folder)

        except Exception as e:
            logger.error(f"Errore nella funzione sposta file: {e}")
            QMessageBox.critical(self, "Errore", f"Errore durante lo spostamento:\n{str(e)}")

    def _perform_move_operation(self, files, destination_folder):
        """Esegue l'operazione di spostamento file."""
        import shutil
        import os
        from pathlib import Path

        moved_files = []
        failed_files = []

        try:
            for file_path in files:
                source_path = None
                try:
                    source_path = Path(file_path)
                    dest_path = Path(destination_folder) / source_path.name

                    # Controlla se il file di destinazione esiste già
                    if dest_path.exists():
                        reply = QMessageBox.question(
                            self,
                            "File Esistente",
                            f"Il file '{source_path.name}' esiste già nella destinazione.\nSovrascrivere?",
                            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No | QMessageBox.StandardButton.Cancel,
                            QMessageBox.StandardButton.No
                        )

                        if reply == QMessageBox.StandardButton.Cancel:
                            break
                        elif reply == QMessageBox.StandardButton.No:
                            failed_files.append(f"{source_path.name} (saltato)")
                            continue

                    # Sposta il file
                    shutil.move(str(source_path), str(dest_path))
                    moved_files.append(source_path.name)
                    logger.info(f"File spostato: {source_path} -> {dest_path}")

                except Exception as e:
                    file_name = source_path.name if source_path else os.path.basename(file_path)
                    failed_files.append(f"{file_name} ({str(e)})")
                    logger.error(f"Errore nello spostamento di {file_path}: {e}")

            # Aggiorna la vista
            if moved_files:
                self.left_panel.refresh_current_folder()

            # Mostra risultati
            self._show_move_results(moved_files, failed_files)

        except Exception as e:
            logger.error(f"Errore nell'operazione di spostamento: {e}")
            QMessageBox.critical(self, "Errore", f"Errore durante lo spostamento:\n{str(e)}")

    def _show_move_results(self, moved_files, failed_files):
        """Mostra i risultati dell'operazione di spostamento."""
        message_parts = []

        if moved_files:
            message_parts.append(f"✅ File spostati con successo: {len(moved_files)}")
            if len(moved_files) <= 5:
                message_parts.append("• " + "\n• ".join(moved_files))
            else:
                message_parts.append(f"• {moved_files[0]}")
                message_parts.append(f"• ... e altri {len(moved_files)-1} file")

        if failed_files:
            message_parts.append(f"\n❌ File non spostati: {len(failed_files)}")
            if len(failed_files) <= 3:
                message_parts.append("• " + "\n• ".join(failed_files))
            else:
                message_parts.append(f"• {failed_files[0]}")
                message_parts.append(f"• ... e altri {len(failed_files)-1} file")

        message = "\n".join(message_parts)

        if failed_files and moved_files:
            QMessageBox.warning(self, "Spostamento Completato con Avvisi", message)
        elif failed_files:
            QMessageBox.critical(self, "Errore Spostamento", message)
        else:
            QMessageBox.information(self, "Spostamento Completato", message)

    def _delete_selected_files(self):
        """Elimina i file selezionati."""
        if hasattr(self.left_panel, '_delete_selected_photos'):
            self.left_panel._delete_selected_photos()
        else:
            logger.warning("Funzione elimina non disponibile")

    def _show_browse_mode(self):
        """Mostra modalità sfoglia."""
        logger.info("Modalità sfoglia attivata")

    def _show_edit_mode(self):
        """Mostra modalità modifica."""
        logger.info("Modalità modifica attivata")

    def _show_organize_mode(self):
        """Mostra modalità organizza."""
        logger.info("Modalità organizza attivata")
    
    def _toggle_fullscreen_shortcut(self):
        """Gestisce il passaggio a schermo intero tramite scorciatoia da tastiera."""
        self._toggle_fullscreen(not self.is_fullscreen)
    
    # === METODI LEGACY CACHE (mantenuti per compatibilità) ===
    def _setup_options_menu(self):
        """Crea il menu a tendina per le opzioni della cache (legacy)."""
        # Questo metodo è mantenuto per compatibilità ma non più utilizzato
        # Le opzioni sono ora gestite tramite la topbar moderna
        pass

    def _show_options_menu(self):
        """Mostra il menu opzioni (legacy)."""
        # Ora gestito tramite topbar
        self._show_settings_dialog()

    def _change_max_disk_cache_size(self):
        """Modifica la dimensione massima della cache su disco."""
        current_value = self.settings.get('cache.max_disk_cache_size_mb', 500)
        new_size, ok = QInputDialog.getInt(self, "Dimensione Cache Disco",
                                           f"Dimensione massima cache su disco (MB):\nAttuale: {current_value} MB",
                                           current_value, 10, 2048, 10)
        if ok and new_size:
            confirm = QMessageBox.question(self, "Conferma Modifica", f"Vuoi davvero impostare la dimensione massima della cache su disco a {new_size} MB?", QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No, QMessageBox.StandardButton.No)
            if confirm == QMessageBox.StandardButton.Yes:
                self.settings.set('cache.max_disk_cache_size_mb', new_size)
                self.settings.save_settings()
                QMessageBox.information(self, "Opzione Cache", f"Dimensione cache impostata a {new_size} MB.\nRiavvia l'applicazione per applicare le modifiche.")

    def _change_max_disk_cache_age(self):
        """Modifica l'età massima dei file nella cache su disco."""
        current_value = self.settings.get('cache.max_disk_cache_file_age_days', 30)
        new_age, ok = QInputDialog.getInt(self, "Età File Cache Disco",
                                          f"Età massima file cache (giorni):\nAttuale: {current_value} giorni",
                                          current_value, 1, 365, 1)
        if ok and new_age:
            confirm = QMessageBox.question(self, "Conferma Modifica", f"Vuoi davvero impostare l'età massima dei file cache su disco a {new_age} giorni?", QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No, QMessageBox.StandardButton.No)
            if confirm == QMessageBox.StandardButton.Yes:
                self.settings.set('cache.max_disk_cache_file_age_days', new_age)
                self.settings.save_settings()
                QMessageBox.information(self, "Opzione Cache", f"Età massima file cache impostata a {new_age} giorni.\nRiavvia l'applicazione per applicare le modifiche.")

    def _clear_disk_cache_now(self):
        """Svuota immediatamente la cache su disco."""
        reply = QMessageBox.question(self, "Svuota Cache",
                                     "Sei sicuro di voler svuotare tutta la cache su disco ora?\nQuesta operazione non può essere annullata.",
                                     QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No, QMessageBox.StandardButton.No)

        if reply == QMessageBox.StandardButton.Yes:
            if hasattr(self, 'image_cache') and hasattr(self.image_cache, 'clear'):
                logger.info("Richiesta di svuotamento manuale della cache.")
                self.image_cache.clear()
                QMessageBox.information(self, "Cache Svuotata", "La cache è stata svuotata completamente.")
            else:
                logger.error("ImageCache o metodo clear non trovati per lo svuotamento manuale.")
                QMessageBox.warning(self, "Errore", "Impossibile svuotare la cache in questo momento.")

    def _update_footer(self):
        """Aggiorna le informazioni nel footer dell'applicazione."""
        # Aggiorna il percorso corrente
        if hasattr(self, 'left_panel') and hasattr(self.left_panel, 'current_folder') and self.left_panel.current_folder:
            self.path_status_label.setText(f"Cartella: {self.left_panel.current_folder}")
        else:
            self.path_status_label.setText("Nessuna cartella selezionata")
            
        # Aggiorna il conteggio delle immagini
        if hasattr(self, 'left_panel') and hasattr(self.left_panel, 'photo_list_widget'):
            count = self.left_panel.photo_list_widget.count()
            if count > 0:
                self.count_status_label.setText(f"{count} immagini")
            else:
                self.count_status_label.setText("Nessuna immagine")
        else:
            self.count_status_label.setText("")
            
        # Aggiorna le informazioni aggiuntive (copyright e data)
        self.info_status_label.setText("PhotoDrop | 2025 Archiwave.tech")
    
    def keyPressEvent(self, event):
        """
        Gestisce gli eventi della tastiera a livello di finestra principale.

        Args:
            event (QKeyEvent): Evento di tastiera
        """
        # Gestisci i tasti freccia per la navigazione tra le immagini
        if event.key() == Qt.Key.Key_Left:
            # Freccia sinistra: immagine precedente
            self.browse_previous_image()
            return
        elif event.key() == Qt.Key.Key_Right:
            # Freccia destra: immagine successiva
            self.browse_next_image()
            return

        # Passa l'evento al gestore predefinito
        super().keyPressEvent(event)

    # 🔧 NUOVO: Metodi per gestione impostazioni
    def _load_window_geometry(self):
        """Carica la geometria della finestra dalle impostazioni."""
        try:
            # Carica stato massimizzato
            if self.settings.get('ui.window_maximized', True):
                self.showMaximized()
            else:
                # Carica geometria specifica se disponibile
                geometry = self.settings.get('ui.window_geometry')
                if geometry:
                    self.setGeometry(geometry[0], geometry[1], geometry[2], geometry[3])
                else:
                    # Usa dimensioni predefinite
                    self.resize(1000, 700)

        except Exception as e:
            logger.error(f"Errore nel caricamento della geometria finestra: {e}")
            self.resize(1000, 700)

    def _save_window_geometry(self):
        """Salva la geometria della finestra nelle impostazioni."""
        try:
            # Salva stato massimizzato
            self.settings.set('ui.window_maximized', self.isMaximized())

            # Salva geometria se non massimizzata
            if not self.isMaximized():
                geometry = self.geometry()
                self.settings.set('ui.window_geometry', [
                    geometry.x(), geometry.y(),
                    geometry.width(), geometry.height()
                ])

        except Exception as e:
            logger.error(f"Errore nel salvataggio della geometria finestra: {e}")

    def _on_splitter_moved(self, pos, index):
        """Gestisce il movimento dello splitter principale."""
        try:
            sizes = self.splitter.sizes()
            self.settings.set('ui.splitter_sizes', sizes)
            logger.debug(f"Splitter principale spostato: {sizes}")
        except Exception as e:
            logger.error(f"Errore nel salvataggio dimensioni splitter: {e}")

    def _on_right_splitter_moved(self, pos, index):
        """Gestisce il movimento dello splitter destro."""
        try:
            # Trova lo splitter destro
            right_splitter = None
            for i in range(self.splitter.count()):
                widget = self.splitter.widget(i)
                if isinstance(widget, QSplitter):
                    right_splitter = widget
                    break

            if right_splitter:
                sizes = right_splitter.sizes()
                self.settings.set('ui.right_splitter_sizes', sizes)
                logger.debug(f"Splitter destro spostato: {sizes}")
        except Exception as e:
            logger.error(f"Errore nel salvataggio dimensioni splitter destro: {e}")



    # 🔧 NUOVO: Metodo per mostrare dialog impostazioni
    def _show_settings_dialog(self):
        """Mostra il dialog delle impostazioni."""
        try:
            dialog = SettingsDialog(self.settings, self)
            dialog.settings_changed.connect(self._on_settings_changed)

            if dialog.exec() == QDialog.DialogCode.Accepted:
                logger.info("Impostazioni modificate dall'utente")

        except Exception as e:
            logger.error(f"Errore nell'apertura del dialog impostazioni: {e}")
            QMessageBox.critical(self, "Errore", f"Impossibile aprire le impostazioni:\n{str(e)}")

    def _on_settings_changed(self):
        """Gestisce i cambiamenti nelle impostazioni."""
        try:
            # Le impostazioni della cache richiedono un riavvio per essere applicate
            logger.info("Le modifiche alla cache saranno applicate al prossimo riavvio")

            # Aggiorna dimensione miniature se cambiata
            thumbnail_size = self.settings.get('ui.thumbnail_size', 128)
            if hasattr(self.left_panel, 'thumbnail_slider'):
                self.left_panel.thumbnail_slider.setValue(thumbnail_size)

            logger.info("Impostazioni applicate all'applicazione")

        except Exception as e:
            logger.error(f"Errore nell'applicazione delle nuove impostazioni: {e}")

    def closeEvent(self, event: QCloseEvent):
        """
        Gestisce l'evento di chiusura della finestra.
        
        Args:
            event (QCloseEvent): Evento di chiusura
        """
        # Pulisci le cache prima di uscire
        if hasattr(self, 'image_cache'):
            self.image_cache.clear()  # Il metodo si chiama 'clear', non 'clear_all'
        
        # Accetta l'evento di chiusura
        event.accept()
        
        logger.info("Applicazione terminata")
