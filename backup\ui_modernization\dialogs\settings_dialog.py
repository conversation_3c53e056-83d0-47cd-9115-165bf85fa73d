#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Dialog per la gestione delle impostazioni dell'applicazione PhotoDrop.
ui/dialogs/settings_dialog.py
"""

from typing import Optional
from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QTabWidget, QWidget,
    QPushButton, QLabel, QSpinBox, QCheckBox, QLineEdit,
    QGroupBox, QFormLayout, QSlider, QComboBox, QMessageBox,
    QFileDialog, QDialogButtonBox
)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QIntValidator, QDoubleValidator

from models.settings_model import SettingsModel
from ui.styles import Styles
from utils.logging_config import get_logger

logger = get_logger("PhotoDrop.SettingsDialog")

class SettingsDialog(QDialog):
    """
    Dialog per la configurazione delle impostazioni dell'applicazione.
    """
    
    # Segnali
    settings_changed = Signal()  # Emesso quando le impostazioni cambiano
    
    def __init__(self, settings_model: SettingsModel, parent=None):
        super().__init__(parent)
        
        self.settings = settings_model
        self.original_settings = settings_model.get_all_settings()
        
        self._setup_ui()
        self._load_current_settings()
        self._connect_signals()
        
        logger.info("SettingsDialog inizializzato")
    
    def _setup_ui(self):
        """Configura l'interfaccia utente del dialog."""
        self.setWindowTitle("Impostazioni - PhotoDrop")
        self.setModal(True)
        self.resize(600, 500)
        
        # Layout principale
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        
        # Tab widget per organizzare le impostazioni
        self.tab_widget = QTabWidget()
        layout.addWidget(self.tab_widget)
        
        # Crea le varie schede
        self._create_ui_tab()
        self._create_cache_tab()
        self._create_viewer_tab()
        self._create_folders_tab()
        self._create_advanced_tab()
        
        # Pulsanti
        button_box = QDialogButtonBox(
            QDialogButtonBox.Ok | QDialogButtonBox.Cancel | QDialogButtonBox.Apply | QDialogButtonBox.RestoreDefaults
        )
        
        self.ok_button = button_box.button(QDialogButtonBox.Ok)
        self.cancel_button = button_box.button(QDialogButtonBox.Cancel)
        self.apply_button = button_box.button(QDialogButtonBox.Apply)
        self.reset_button = button_box.button(QDialogButtonBox.RestoreDefaults)
        
        layout.addWidget(button_box)
        
        # Stile del dialog
        self.setStyleSheet(f"""
            QDialog {{
                background-color: {Styles.DARK_1};
                color: {Styles.TEXT_MAIN};
            }}
            QTabWidget::pane {{
                border: 1px solid {Styles.BORDER_COLOR};
                background-color: {Styles.DARK_2};
            }}
            QTabBar::tab {{
                background-color: {Styles.DARK_3};
                color: {Styles.TEXT_MAIN};
                padding: 8px 16px;
                margin-right: 2px;
            }}
            QTabBar::tab:selected {{
                background-color: {Styles.ACCENT};
                color: {Styles.TEXT_MAIN};
            }}
        """)
    
    def _create_ui_tab(self):
        """Crea la scheda per le impostazioni dell'interfaccia utente."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Gruppo Finestra
        window_group = QGroupBox("Finestra")
        window_layout = QFormLayout(window_group)
        
        self.window_maximized_cb = QCheckBox("Avvia massimizzata")
        window_layout.addRow("Stato finestra:", self.window_maximized_cb)
        
        layout.addWidget(window_group)
        
        # Gruppo Miniature
        thumbnails_group = QGroupBox("Miniature")
        thumbnails_layout = QFormLayout(thumbnails_group)
        
        self.thumbnail_size_spin = QSpinBox()
        self.thumbnail_size_spin.setRange(64, 512)
        self.thumbnail_size_spin.setSuffix(" px")
        thumbnails_layout.addRow("Dimensione miniature:", self.thumbnail_size_spin)
        
        layout.addWidget(thumbnails_group)
        
        layout.addStretch()
        self.tab_widget.addTab(tab, "Interfaccia")
    
    def _create_cache_tab(self):
        """Crea la scheda per le impostazioni della cache."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Gruppo Cache Memoria
        memory_group = QGroupBox("Cache in Memoria")
        memory_layout = QFormLayout(memory_group)
        
        self.max_memory_items_spin = QSpinBox()
        self.max_memory_items_spin.setRange(100, 10000)
        self.max_memory_items_spin.setSuffix(" elementi")
        memory_layout.addRow("Massimo elementi:", self.max_memory_items_spin)
        
        layout.addWidget(memory_group)
        
        # Gruppo Cache Disco
        disk_group = QGroupBox("Cache su Disco")
        disk_layout = QFormLayout(disk_group)
        
        self.enable_disk_cache_cb = QCheckBox("Abilita cache su disco")
        disk_layout.addRow("Stato:", self.enable_disk_cache_cb)
        
        self.max_disk_size_spin = QSpinBox()
        self.max_disk_size_spin.setRange(50, 5000)
        self.max_disk_size_spin.setSuffix(" MB")
        disk_layout.addRow("Dimensione massima:", self.max_disk_size_spin)
        
        self.max_disk_age_spin = QSpinBox()
        self.max_disk_age_spin.setRange(1, 365)
        self.max_disk_age_spin.setSuffix(" giorni")
        disk_layout.addRow("Età massima file:", self.max_disk_age_spin)
        
        layout.addWidget(disk_group)
        
        layout.addStretch()
        self.tab_widget.addTab(tab, "Cache")
    
    def _create_viewer_tab(self):
        """Crea la scheda per le impostazioni del visualizzatore."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Gruppo Zoom
        zoom_group = QGroupBox("Zoom")
        zoom_layout = QFormLayout(zoom_group)
        
        self.auto_fit_cb = QCheckBox("Adatta automaticamente l'immagine")
        zoom_layout.addRow("Adattamento:", self.auto_fit_cb)
        
        self.smooth_zoom_cb = QCheckBox("Zoom fluido")
        zoom_layout.addRow("Qualità:", self.smooth_zoom_cb)
        
        self.zoom_step_spin = QSpinBox()
        self.zoom_step_spin.setRange(110, 200)
        self.zoom_step_spin.setSuffix(" %")
        zoom_layout.addRow("Passo zoom:", self.zoom_step_spin)
        
        layout.addWidget(zoom_group)
        
        layout.addStretch()
        self.tab_widget.addTab(tab, "Visualizzatore")
    
    def _create_folders_tab(self):
        """Crea la scheda per le impostazioni delle cartelle."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Gruppo Cartelle
        folders_group = QGroupBox("Gestione Cartelle")
        folders_layout = QFormLayout(folders_group)
        
        self.remember_last_folder_cb = QCheckBox("Ricorda ultima cartella aperta")
        folders_layout.addRow("Memoria:", self.remember_last_folder_cb)
        
        # Mostra ultima cartella (solo lettura)
        self.last_folder_label = QLabel("Nessuna")
        self.last_folder_label.setStyleSheet(f"color: {Styles.TEXT_SECONDARY}; font-style: italic;")
        folders_layout.addRow("Ultima cartella:", self.last_folder_label)
        
        layout.addWidget(folders_group)
        
        layout.addStretch()
        self.tab_widget.addTab(tab, "Cartelle")
    
    def _create_advanced_tab(self):
        """Crea la scheda per le impostazioni avanzate."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Gruppo Logging
        logging_group = QGroupBox("Logging e Debug")
        logging_layout = QFormLayout(logging_group)
        
        self.logging_level_combo = QComboBox()
        self.logging_level_combo.addItems(["DEBUG", "INFO", "WARNING", "ERROR"])
        logging_layout.addRow("Livello logging:", self.logging_level_combo)
        
        self.enable_debug_cb = QCheckBox("Abilita stampe di debug")
        logging_layout.addRow("Debug:", self.enable_debug_cb)
        
        layout.addWidget(logging_group)
        
        # Gruppo Sistema
        system_group = QGroupBox("Sistema")
        system_layout = QFormLayout(system_group)
        
        self.auto_save_cb = QCheckBox("Salva automaticamente le impostazioni")
        system_layout.addRow("Salvataggio:", self.auto_save_cb)
        
        layout.addWidget(system_group)
        
        layout.addStretch()
        self.tab_widget.addTab(tab, "Avanzate")
    
    def _load_current_settings(self):
        """Carica le impostazioni correnti nei controlli."""
        try:
            # UI
            self.window_maximized_cb.setChecked(self.settings.get('ui.window_maximized', True))
            self.thumbnail_size_spin.setValue(self.settings.get('ui.thumbnail_size', 128))
            
            # Cache
            self.max_memory_items_spin.setValue(self.settings.get('cache.max_memory_items', 1000))
            self.enable_disk_cache_cb.setChecked(self.settings.get('cache.enable_disk_cache', True))
            self.max_disk_size_spin.setValue(self.settings.get('cache.max_disk_cache_size_mb', 500))
            self.max_disk_age_spin.setValue(self.settings.get('cache.max_disk_cache_file_age_days', 30))
            
            # Viewer
            self.auto_fit_cb.setChecked(self.settings.get('viewer.auto_fit_image', True))
            self.smooth_zoom_cb.setChecked(self.settings.get('viewer.smooth_zoom', True))
            zoom_step = int(self.settings.get('viewer.zoom_step', 1.2) * 100)
            self.zoom_step_spin.setValue(zoom_step)
            
            # Folders
            self.remember_last_folder_cb.setChecked(self.settings.get('folders.remember_last_folder', True))
            last_folder = self.settings.get('folders.last_opened_folder', "Nessuna")
            self.last_folder_label.setText(last_folder or "Nessuna")
            
            # Advanced
            self.logging_level_combo.setCurrentText(self.settings.get('advanced.logging_level', 'INFO'))
            self.enable_debug_cb.setChecked(self.settings.get('advanced.enable_debug_prints', False))
            self.auto_save_cb.setChecked(self.settings.get('advanced.auto_save_settings', True))
            
            logger.debug("Impostazioni caricate nei controlli")
            
        except Exception as e:
            logger.error(f"Errore nel caricamento delle impostazioni: {e}")
    
    def _connect_signals(self):
        """Connette i segnali ai slot."""
        self.ok_button.clicked.connect(self._on_ok_clicked)
        self.cancel_button.clicked.connect(self._on_cancel_clicked)
        self.apply_button.clicked.connect(self._on_apply_clicked)
        self.reset_button.clicked.connect(self._on_reset_clicked)
    
    def _on_ok_clicked(self):
        """Gestisce il click su OK."""
        if self._apply_settings():
            self.accept()
    
    def _on_cancel_clicked(self):
        """Gestisce il click su Annulla."""
        self.reject()
    
    def _on_apply_clicked(self):
        """Gestisce il click su Applica."""
        self._apply_settings()
    
    def _on_reset_clicked(self):
        """Gestisce il click su Ripristina predefiniti."""
        reply = QMessageBox.question(
            self,
            "Ripristina Impostazioni",
            "Vuoi davvero ripristinare tutte le impostazioni ai valori predefiniti?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            self.settings.reset_to_defaults()
            self._load_current_settings()
            self.settings_changed.emit()
            QMessageBox.information(self, "Ripristino Completato", "Le impostazioni sono state ripristinate ai valori predefiniti.")
    
    def _apply_settings(self) -> bool:
        """
        Applica le impostazioni modificate.
        
        Returns:
            True se l'applicazione è riuscita, False altrimenti
        """
        try:
            # UI
            self.settings.set('ui.window_maximized', self.window_maximized_cb.isChecked())
            self.settings.set('ui.thumbnail_size', self.thumbnail_size_spin.value())
            
            # Cache
            self.settings.set('cache.max_memory_items', self.max_memory_items_spin.value())
            self.settings.set('cache.enable_disk_cache', self.enable_disk_cache_cb.isChecked())
            self.settings.set('cache.max_disk_cache_size_mb', self.max_disk_size_spin.value())
            self.settings.set('cache.max_disk_cache_file_age_days', self.max_disk_age_spin.value())
            
            # Viewer
            self.settings.set('viewer.auto_fit_image', self.auto_fit_cb.isChecked())
            self.settings.set('viewer.smooth_zoom', self.smooth_zoom_cb.isChecked())
            zoom_step = self.zoom_step_spin.value() / 100.0
            self.settings.set('viewer.zoom_step', zoom_step)
            
            # Folders
            self.settings.set('folders.remember_last_folder', self.remember_last_folder_cb.isChecked())
            
            # Advanced
            self.settings.set('advanced.logging_level', self.logging_level_combo.currentText())
            self.settings.set('advanced.enable_debug_prints', self.enable_debug_cb.isChecked())
            self.settings.set('advanced.auto_save_settings', self.auto_save_cb.isChecked())
            
            # Salva le impostazioni
            if self.settings.save_settings():
                self.settings_changed.emit()
                logger.info("Impostazioni applicate e salvate")
                return True
            else:
                QMessageBox.warning(self, "Errore", "Impossibile salvare le impostazioni.")
                return False
                
        except Exception as e:
            logger.error(f"Errore nell'applicazione delle impostazioni: {e}")
            QMessageBox.critical(self, "Errore", f"Errore nell'applicazione delle impostazioni:\n{str(e)}")
            return False
