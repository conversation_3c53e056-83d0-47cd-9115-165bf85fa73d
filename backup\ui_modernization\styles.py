#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Definizione degli stili CSS per l'interfaccia utente dell'applicazione.
"""

class Styles:
    """Classe contenente gli stili CSS per l'applicazione."""
    
    # Palette di colori personalizzata
    DARK_1 = "#1a1a1a"  # Sfondo principale
    DARK_2 = "#242424"  # Pannelli e barre laterali
    DARK_3 = "#2d2d2d"  # Elementi interattivi
    DARK_4 = "#353535"  # Elementi selezionati
    ACCENT = "#0078d7"   # Colore di accento principale
    TEXT_MAIN = "#f0f0f0"
    TEXT_SECONDARY = "#b0b0b0"
    BORDER_COLOR = "#454545"
    HIGHLIGHT = "#2b5797"
    HOVER = "#3a3a3a"
    PRESSED = "#454545"
    
    # Stile generale scuro e moderno
    MAIN_STYLE = f"""
        /* Stile di base per l'applicazione */
        QWidget {{
            background-color: {DARK_1};
            color: {TEXT_MAIN};
            font-family: 'Segoe UI', 'Arial', sans-serif;
            font-size: 13px;
            selection-background-color: {ACCENT};
            selection-color: white;
        }}
        
        /* Barra del titolo personalizzata */
        QMainWindow::separator {{
            width: 0;
            background: transparent;
        }}
        
        /* Pannelli e contenitori */
        QFrame {{
            background-color: {DARK_2};
            border: none;
            border-radius: 0;
            padding: 0;
        }}
        
        /* Splitter handle */
        QSplitter::handle {{
            background: {DARK_3};
            width: 1px;
            height: 1px;
        }}
        
        /* Pannello sinistro */
        #leftPanel {{
            background-color: {DARK_2};
        }}
        
        /* Pannello destro */
        #rightPanel {{
            background-color: {DARK_1};
        }}
        
        /* Sidebar destra */
        #rightSidebar {{
            background-color: {DARK_2};
        }}
        
        /* Elementi di input */
        QLineEdit, QTextEdit, QPlainTextEdit, QSpinBox, QComboBox, QDateEdit, QTimeEdit, QDateTimeEdit {{
            background-color: transparent;
            border: 1px solid {BORDER_COLOR};
            border-radius: 4px;
            padding: 6px 8px;
            color: {TEXT_MAIN};
            selection-background-color: {ACCENT};
            selection-color: white;
        }}
        
        QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {{
            border: 1px solid {ACCENT};
        }}
        
        /* Pulsanti */
        QPushButton {{
            background-color: {DARK_3};
            color: {TEXT_MAIN};
            border: 1px solid {BORDER_COLOR};
            border-radius: 6px;
            padding: 6px 12px;
            font-weight: 500;
            min-width: 80px;
        }}
        
        QPushButton:hover {{
            background-color: {HOVER};
            border-color: {BORDER_COLOR};
        }}
        
        QPushButton:pressed {{
            background-color: {PRESSED};
            border-color: {ACCENT};
        }}
        
        QPushButton:disabled {{
            background-color: #1a1a1a;
            color: #505050;
            border-color: #2d2d2d;
        }}
        
        /* Barra di stato */
        QStatusBar {{
            background: {DARK_2};
            color: {TEXT_SECONDARY};
            border-top: 1px solid {BORDER_COLOR};
            padding: 2px;
        }}
        
        /* Barra di avanzamento */
        QProgressBar {{
            height: 16px;
            text-align: center;
            background: {DARK_3};
            border: 1px solid {BORDER_COLOR};
            border-radius: 8px;
        }}
        
        QProgressBar::chunk {{
            background-color: {ACCENT};
            border-radius: 6px;
        }}
        
        /* Lista elementi */
        QListWidget, QTreeView, QTableView {{
            background-color: {DARK_3};
            border: 1px solid {BORDER_COLOR};
            border-radius: 4px;
            padding: 2px;
            outline: 0;
        }}
        
        QListWidget::item, QTreeWidget::item, QTableWidget::item {{
            padding: 6px 8px;
            border-radius: 4px;
            margin: 1px 2px;
        }}
        
        QListWidget::item:selected, QTreeWidget::item:selected, QTableWidget::item:selected {{
            background-color: {HIGHLIGHT};
            color: white;
        }}
        
        QListWidget::item:hover, QTreeWidget::item:hover, QTableWidget::item:hover {{
            background-color: {HOVER};
        }}
        
        /* Slider */
        QSlider::groove:horizontal {{
            border: 1px solid {BORDER_COLOR};
            background: {DARK_2};
            height: 6px;
            border-radius: 3px;
        }}
        
        QSlider::handle:horizontal {{
            background: #242424;
            border: 1px solid #666;
            width: 16px;
            margin: -6px 0;
            border-radius: 8px;
        }}
        
        QSlider::handle:horizontal:hover {{
            background: {TEXT_MAIN};
            border-color: {ACCENT};
        }}
        
        QSlider::add-page:horizontal {{
            background: {DARK_2};
            border: 1px solid {BORDER_COLOR};
            border-radius: 3px;
        }}
         
        QSlider::sub-page:horizontal {{
            background: {ACCENT};
            border: 1px solid {ACCENT};
            border-radius: 3px;
        }}
        
        /* Tab Widget */
        QTabWidget::pane {{
            border: 1px solid {BORDER_COLOR};
            border-top: none;
            padding: 0px;
            margin: 0px;
        }}
        
        QTabBar::tab {{
            background: {DARK_3};
            color: {TEXT_SECONDARY};
            border: 1px solid {BORDER_COLOR};
            border-bottom: none;
            padding: 6px 12px;
            margin-right: 2px;
            border-top-left-radius: 4px;
            border-top-right-radius: 4px;
        }}
        
        QTabBar::tab:selected, QTabBar::tab:hover {{
            background: {DARK_2};
            color: {TEXT_MAIN};
            border-color: {BORDER_COLOR};
            border-bottom-color: {DARK_2};
        }}
        
        QTabBar::tab:selected {{
            font-weight: bold;
        }}
    """
    
    @classmethod
    def get_left_panel_style(cls):
        return f"""
        QFrame#leftPanel {{
            background-color: {cls.DARK_2};
            border-right: 1px solid {cls.BORDER_COLOR};
            border-top: none;
            border-bottom: none;
            border-left: none;
            border-top-left-radius: 0;
            border-bottom-left-radius: 0;
        }}
        
        QListWidget {{
            background-color: transparent;
            border: none;
            border-radius: 0;
            padding: 4px;
            outline: none;
        }}
        
        QListWidget::item {{
            background-color: {cls.DARK_3};
            border: 1px solid {cls.BORDER_COLOR};
            border-radius: 2px;
            margin: 2px;
            padding: 4px;
        }}
        
        /* Stile per le miniature delle immagini */
        QLabel[thumbnail="true"] {{
            background-color: transparent;
            border: none;
            padding: 0;
            margin: 0;
        }}
        
        QListWidget::item:selected {{
            background-color: {cls.HIGHLIGHT};
            border: 1px solid {cls.ACCENT};
            color: white;
        }}
        
        QListWidget::item:hover {{
            background-color: {cls.HOVER};
            border: 1px solid {cls.ACCENT};
        }}
        
        QScrollBar:vertical {{
            border: none;
            background: {cls.DARK_2};
            width: 10px;
            margin: 0px;
        }}
        
        QScrollBar::handle:vertical {{
            background: {cls.DARK_4};
            min-height: 20px;
            border-radius: 5px;
        }}
        
        QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {{
            height: 0px;
        }}
    """
    
    # Stile specifico per il pannello sinistro (proprietà per compatibilità)
    LEFT_PANEL_STYLE = ""  # Sarà popolato nel metodo __init_subclass__
    
    @classmethod
    def get_right_panel_style(cls):
        return f"""
        QFrame#rightPanel {{
            background-color: {cls.DARK_1};
            border: none;
        }}
        
        QLabel#previewLabel {{
            background-color: {cls.DARK_1};
            border: none;
            border-radius: 0;
            padding: 0;
            margin: 0;
        }}
        
        /* Stile per la barra degli strumenti dell'anteprima */
        QToolBar {{
            background-color: {cls.DARK_2};
            border: none;
            border-bottom: 1px solid {cls.BORDER_COLOR};
            padding: 4px;
            spacing: 4px;
        }}
        
        QToolBar QToolButton {{
            background-color: transparent;
            border: 1px solid transparent;
            border-radius: 4px;
            padding: 4px;
        }}
        
        QToolBar QToolButton:hover {{
            background-color: {cls.HOVER};
            border: 1px solid {cls.BORDER_COLOR};
        }}
        
        QToolBar QToolButton:pressed {{
            background-color: {cls.PRESSED};
        }}
    """
    
    # Stile specifico per il pannello destro (proprietà per compatibilità)
    RIGHT_PANEL_STYLE = ""  # Sarà popolato nel metodo __init_subclass__
    
    @classmethod
    def get_action_button_style(cls):
        return f"""
        QPushButton {{
            background-color: {cls.ACCENT};
            color: white;
            border: none;
            border-radius: 6px;
            padding: 8px 16px;
            font-weight: 500;
            min-width: 100px;
        }}
        QPushButton:hover {{
            background-color: #1a8cff;
        }}
        QPushButton:pressed {{
            background-color: #0066cc;
        }}
        QPushButton:disabled {{
            background-color: #1a3d5c;
            color: #6b8ba3;
        }}
    """
    
    # Stile per i pulsanti di azione (proprietà per compatibilità)
    ACTION_BUTTON_STYLE = ""  # Sarà popolato nel metodo __init_subclass__
    
    @classmethod
    def get_danger_button_style(cls):
        return f"""
        QPushButton {{
            background-color: #c42b1c;
            color: white;
            border: none;
            border-radius: 6px;
            padding: 8px 16px;
            font-weight: 500;
            min-width: 100px;
        }}
        QPushButton:hover {{
            background-color: #e04a3f;
        }}
        QPushButton:pressed {{
            background-color: #a32012;
        }}
        QPushButton:disabled {{
            background-color: #4a1f1a;
            color: #8a5e59;
        }}
    """
    
    # Stile per i pulsanti di pericolo (proprietà per compatibilità)
    DANGER_BUTTON_STYLE = ""  # Sarà popolato nel metodo __init_subclass__
    
    @classmethod
    def get_secondary_button_style(cls):
        return f"""
        QPushButton {{
            background-color: {cls.DARK_3};
            color: {cls.TEXT_MAIN};
            border: 1px solid {cls.BORDER_COLOR};
            border-radius: 6px;
            padding: 8px 16px;
            font-weight: 500;
            min-width: 100px;
        }}
        QPushButton:hover {{
            background-color: {cls.HOVER};
            border-color: {cls.ACCENT};
        }}
        QPushButton:pressed {{
            background-color: {cls.PRESSED};
        }}
        QPushButton:disabled {{
            background-color: #1a1a1a;
            color: #505050;
            border-color: #2d2d2d;
        }}
    """
    
    # Stile per i pulsanti secondari (proprietà per compatibilità)
    SECONDARY_BUTTON_STYLE = ""  # Sarà popolato nel metodo __init_subclass__
    
    @classmethod
    def get_close_button_style(cls):
        return f"""
        QPushButton {{
            background-color: transparent;
            color: {cls.TEXT_SECONDARY};
            border: 1px solid transparent;
            border-radius: 4px;
            padding: 4px 8px;
            min-width: 24px;
            max-width: 24px;
            min-height: 24px;
            max-height: 24px;
        }}
        QPushButton:hover {{
            background-color: #e81123;
            color: white;
        }}
        QPushButton:pressed {{
            background-color: #a0111a;
        }}
    """
    
    # Stile per i pulsanti di chiusura (proprietà per compatibilità)
    CLOSE_BUTTON_STYLE = ""  # Sarà popolato nel metodo __init_subclass__
    
    @classmethod
    def get_status_label_style(cls):
        return f"""
        QLabel {{
            background-color: {cls.DARK_3};
            color: {cls.TEXT_MAIN};
            border: 1px solid {cls.BORDER_COLOR};
            border-radius: 4px;
            padding: 6px 10px;
            margin: 2px;
        }}
        
        QLabel[status="info"] {{
            border-left: 3px solid {cls.ACCENT};
        }}
        
        QLabel[status="warning"] {{
            border-left: 3px solid #ffcc00;
        }}
        
        QLabel[status="error"] {{
            border-left: 3px solid #ff4444;
        }}
        
        /* Stile per il percorso dell'immagine */
        QLabel#pathLabel {{
            background-color: {cls.DARK_2};
            color: {cls.TEXT_SECONDARY};
            border: none;
            border-bottom: 1px solid {cls.BORDER_COLOR};
            border-radius: 0;
            padding: 4px 8px;
            font-size: 12px;
            font-family: 'Consolas', 'Courier New', monospace;
        }}
        
        /* Stile per il footer */
        QStatusBar {{
            background-color: {cls.DARK_2};
            color: {cls.TEXT_SECONDARY};
            border-top: 1px solid {cls.BORDER_COLOR};
            min-height: 24px;
        }}
        
        QStatusBar QLabel {{
            background-color: transparent;
            border: none;
            padding: 2px 8px;
            font-size: 12px;
        }}
    """
    
    # Stile per i messaggi di stato (proprietà per compatibilità)
    STATUS_LABEL_STYLE = ""  # Sarà popolato nel metodo __init_subclass__
    
    @classmethod
    def get_tooltip_style(cls):
        return f"""
        QToolTip {{
            background-color: {cls.DARK_3};
            color: {cls.TEXT_MAIN};
            border: 1px solid {cls.BORDER_COLOR};
            border-radius: 4px;
            padding: 6px 10px;
            opacity: 240;
            font-size: 12px;
        }}
    """
    
    # Stile tooltip migliorato (proprietà per compatibilità)
    TOOLTIP_STYLE = ""  # Sarà popolato nel metodo __init_subclass__
    
    @classmethod
    def get_icon_button_style(cls):
        return f"""
        QPushButton {{
            background-color: transparent;
            border: 1px solid transparent;
            border-radius: 4px;
            padding: 8px;
            min-width: 36px;
            min-height: 36px;
        }}
        QPushButton:hover {{
            background-color: {cls.HOVER};
            border: 1px solid {cls.BORDER_COLOR};
        }}
        QPushButton:pressed {{
            background-color: {cls.PRESSED};
            border: 1px solid {cls.ACCENT};
        }}
        QPushButton:checked {{
            background-color: {cls.HIGHLIGHT};
            border: 1px solid {cls.ACCENT};
        }}
        QPushButton:disabled {{
            background-color: transparent;
            color: #505050;
        }}
    """
    
    # Stile specifico per i pulsanti con icone (proprietà per compatibilità)
    ICON_BUTTON_STYLE = ""  # Sarà popolato nel metodo __init_subclass__
    
    # Inizializza le proprietà statiche
    @classmethod
    def initialize(cls):
        # Popola le proprietà statiche con i valori generati dai metodi
        cls.LEFT_PANEL_STYLE = cls.get_left_panel_style()
        cls.RIGHT_PANEL_STYLE = cls.get_right_panel_style()
        cls.ACTION_BUTTON_STYLE = cls.get_action_button_style()
        cls.DANGER_BUTTON_STYLE = cls.get_danger_button_style()
        cls.SECONDARY_BUTTON_STYLE = cls.get_secondary_button_style()
        cls.CLOSE_BUTTON_STYLE = cls.get_close_button_style()
        cls.STATUS_LABEL_STYLE = cls.get_status_label_style()
        cls.TOOLTIP_STYLE = cls.get_tooltip_style()
        cls.ICON_BUTTON_STYLE = cls.get_icon_button_style()
    
    @classmethod
    def get_combined_style(cls):
        """
        Restituisce lo stile CSS combinato per l'applicazione.
        
        Returns:
            str: Lo stile CSS combinato
        """
        return (
            cls.MAIN_STYLE +
            cls.LEFT_PANEL_STYLE +
            cls.RIGHT_PANEL_STYLE +
            cls.ACTION_BUTTON_STYLE +
            cls.DANGER_BUTTON_STYLE +
            cls.SECONDARY_BUTTON_STYLE +
            cls.CLOSE_BUTTON_STYLE +
            cls.STATUS_LABEL_STYLE +
            cls.TOOLTIP_STYLE +
            cls.ICON_BUTTON_STYLE
        )

# Inizializza gli stili statici
Styles.initialize()
