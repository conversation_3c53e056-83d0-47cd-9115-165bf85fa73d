#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Finestra di dialogo per la visualizzazione della guida dell'applicazione.
"""

from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QTextBrowser, QDialogButtonBox,
    QSizePolicy
)
from PySide6.QtCore import QUrl, Qt, QSize
from PySide6.QtGui import QIcon, QTextDocument

import markdown # Importa la libreria markdown
import re # Importo la libreria re per le espressioni regolari

from utils.logging_config import get_logger

logger = get_logger("PhotoDrop.HelpDialog")

class HelpDialog(QDialog):
    """
    Finestra di dialogo che mostra la guida all'utilizzo dell'applicazione.
    """
    
    def __init__(self, parent=None):
        """
        Inizializza la finestra di dialogo della guida.
        
        Args:
            parent: Widget genitore
        """
        super().__init__(parent)
        
        # Configurazione finestra
        self.setWindowTitle("Guida - PhotoDrop")
        self.setMinimumSize(800, 600)
        self.setWindowIcon(QIcon("assets/icons/help_duotone.svg"))
        
        # Layout principale
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # Browser per visualizzare la guida
        self.browser = QTextBrowser()
        self.browser.setOpenExternalLinks(True)
        self.browser.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        
        # Pulsanti di chiusura
        self.button_box = QDialogButtonBox(QDialogButtonBox.Close)
        self.button_box.rejected.connect(self.accept)
        
        # Aggiungi i widget al layout
        layout.addWidget(self.browser, 1)
        layout.addWidget(self.button_box)
        
        # Carica il contenuto della guida
        self.load_help_content()
        
        # Stile
        self.setStyleSheet("""
            QDialog {
                background-color: #1e1e1e;
                color: #e0e0e0;
            }
            QTextBrowser {
                background-color: #252525;
                border: 1px solid #3a3a3a;
                border-radius: 4px;
                padding: 10px;
                font-family: 'Segoe UI', Arial, sans-serif;
                font-size: 13px; /* Aumento la dimensione del testo base */
            }
            QPushButton {
                background-color: #3a3a3a;
                color: #e0e0e0;
                border: 1px solid #4a4a4a;
                border-radius: 4px;
                padding: 5px 15px;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #4a4a4a;
            }
            QPushButton:pressed {
                background-color: #2a2a2a;
            }
        """)
    
    def load_help_content(self):
        """Carica il contenuto della guida dal file markdown."""
        try:
            # Leggi il contenuto del file di guida
            with open("docs/help.md", "r", encoding="utf-8") as f:
                content = f.read()
            
            # Processa il contenuto per aggiungere icone prima della conversione markdown
            processed_content = self.add_icons_to_markdown(content)

            # Converti il markdown (con icone aggiunte) in HTML usando la libreria
            # Aggiungo l'estensione 'toc' per l'indice automatico e 'fenced_code' per i blocchi di codice
            html_body = markdown.markdown(processed_content, extensions=['toc', 'fenced_code'])

            # Imposta il contenuto HTML nel browser con gli stili
            self.browser.setHtml(self.apply_html_style(html_body))
            
        except Exception as e:
            logger.error(f"Errore nel caricamento della guida: {str(e)}")
            self.browser.setPlainText(
                "Impossibile caricare la guida. Assicurati che il file 'docs/help.md' sia presente.\n\n"
                f"Errore: {str(e)}"
            )

    def add_icons_to_markdown(self, markdown_text):
        """
        Aggiunge tag HTML img per le icone ai punti rilevanti nel testo markdown.
        
        Args:
            markdown_text: Testo in formato markdown
            
        Returns:
            Testo markdown con tag img aggiunti.
        """
        # Mappa dei comandi e delle icone corrispondenti
        icon_map = {
            "Navigazione Immagine": "prev_duotone.svg", 
            "Zoom": "zoom_in_duotone.svg", 
            "Adatta allo Schermo/Reset Zoom": "zoom_reset_duotone.svg", 
            "Rotazione": "rotate_right_duotone.svg", 
            "Schermo Intero": "fullscreen_duotone.svg",
            "Ritaglia (Crop)": "crop_duotone.svg", 
            "Aiuto": "help_duotone.svg",
            "Cartelle": "folder_duotone.svg",
            "Rinomina": "rename_duotone.svg",
            "Elimina": "trash_duotone.svg",
            # Aggiungere altre mappature se ci sono icone per altri comandi
        }

        processed_text = markdown_text

        # Itera sulla mappa delle icone e sostituisci il testo del comando con testo + icona
        # Uso un pattern regex per trovare il comando seguito da ': ' o un newline
        # Questo cerca di evitare di sostituire il nome del comando se appare in un contesto diverso
        for command, icon_file in icon_map.items():
            # Pattern per trovare il comando seguito da ': ' o da un newline
            # e catturare il resto della riga
            pattern = r"(\*\*" + re.escape(command) + r"\*\*:.*?)"
            
            # Sostituisci solo se il pattern corrisponde
            def replace_with_icon(match):
                # match.group(1) contiene il testo originale da sostituire (es. **Zoom**: ...)
                original_text = match.group(1)
                # Aggiungi il tag img con la classe css 'command-icon'
                icon_html = f'<img src="assets/icons/{icon_file}" class="command-icon" alt="{command} Icon"/>'
                # Inserisci l'icona prima del testo originale (o modificalo come preferisci)
                return f'{icon_html} {original_text}'

            # Usa re.sub per fare la sostituzione
            processed_text = re.sub(pattern, replace_with_icon, processed_text, flags=re.IGNORECASE)
            
        return processed_text

    def apply_html_style(self, html_body):
        """
        Applica lo stile CSS all'HTML generato.
        
        Args:
            html_body: Stringa HTML generata dalla libreria markdown.
            
        Returns:
            Stringa HTML completa con stili.
        """
        # Aggiungi lo stile CSS e avvolgi l'HTML generato
        return f"""
        <html>
        <head>
            <style>
                body {{
                    font-family: 'Segoe UI', Arial, sans-serif;
                    color: #e0e0e0;
                    line-height: 1.6;
                    margin: 0;
                    padding: 10px;
                    background-color: #252525;
                    font-size: 12px; /* Testo base più leggibile */
                }}
                h1, h2, h3, h4, h5, h6 {{
                    color: #4dabf7; /* Colore diverso per i titoli */
                    margin-top: 25px;
                    margin-bottom: 15px;
                    font-weight: 700; /* Font più spesso per i titoli */
                }}
                h1 {{
                    font-size: 2em;
                    border-bottom: 2px solid #4dabf7;
                    padding-bottom: 0.4em;
                }}
                h2 {{
                    font-size: 1.6em;
                    border-bottom: 1px solid #4dabf7;
                    padding-bottom: 0.3em;
                }}
                h3 {{
                    font-size: 1.3em;
                    color: #66bb6a; /* Colore diverso per i sottotitoli */
                    border-left: 4px solid #66bb6a;
                    padding-left: 10px;
                }}
                p {{
                    margin-bottom: 15px;
                }}
                pre, code {{
                    background-color: #2d2d2d;
                    border-radius: 3px;
                    font-family: 'Consolas', 'Monaco', monospace;
                    padding: 0.2em 0.4em;
                    margin: 0;
                    font-size: 0.9em;
                }}
                pre {{
                    padding: 15px;
                    overflow: auto;
                    line-height: 1.5;
                    border-radius: 5px;
                    margin: 15px 0;
                }}
                pre code {{
                    background-color: transparent;
                    padding: 0;
                }}
                a {{
                    color: #4dabf7;
                    text-decoration: none;
                }}
                a:hover {{
                    text-decoration: underline;
                }}
                ul, ol {{
                    padding-left: 2em;
                    margin-bottom: 15px;
                }}
                li {{
                    margin-bottom: 0.5em;
                }}
                blockquote {{
                    border-left: 4px solid #4a4a4a;
                    color: #a0a0a0;
                    padding: 0 1em;
                    margin: 0 0 15px 0;
                }}
                table {{
                    border-collapse: collapse;
                    width: 100%;
                    margin: 15px 0;
                    display: block;
                    overflow-x: auto;
                }}
                th, td {{
                    border: 1px solid #4a4a4a;
                    padding: 8px 12px;
                }}
                th {{
                    background-color: #2d2d2d;
                    font-weight: 600;
                }}
                tr:nth-child(2n) {{
                    background-color: #2a2a2a;
                }}
                strong {{
                    color: #4dabf7;
                }}
                /* Stile specifico per la table of contents */
                .toc {{
                    background-color: #2d2d2d;
                    border: 1px solid #4a4a4a;
                    border-radius: 4px;
                    padding: 15px;
                    margin: 15px 0;
                }}
                .toc ul {{
                    list-style-type: none;
                    padding-left: 0;
                }}
                .toc li {{
                    margin: 8px 0;
                }}
                .toc a {{
                    color: #e0e0e0; /* Colore base link TOC */
                    text-decoration: none;
                    display: block;
                    padding: 4px 8px;
                    border-radius: 2px;
                }}
                .toc a:hover {{
                    background-color: #3a3a3a;
                    color: #4dabf7; /* Colore link TOC al passaggio del mouse */
                }}
                 .toc h2 {{
                    color: #ffffff; /* Assicura che il titolo Indice sia bianco */
                    background: none;
                    border-bottom: none;
                    padding: 0;
                    margin-top: 0;
                    margin-bottom: 10px;
                }}

                /* Stile per le icone dei comandi */
                .command-icon {{
                    height: 1.2em; /* Rende l'altezza dell'icona proporzionale al testo */
                    vertical-align: middle; /* Allinea verticalmente l'icona con il testo */
                    margin-right: 5px; /* Spazio tra icona e testo */
                }}
                
            </style>
        </head>
        <body>
            {html_body}
        </body>
        </html>
        """

    def sizeHint(self):
        """Restituisce la dimensione preferita della finestra."""
        return QSize(900, 700)
