"""
Pannello laterale destro che mostra informazioni dettagliate sull'immagine e i metadati EXIF.
"""

import os
from typing import Optional, Dict, Any

import logging
logger = logging.getLogger(__name__)

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QScrollArea, QFrame
)
from PySide6.QtCore import Qt, Signal, QFileInfo
from PySide6.QtGui import QPixmap

from utils.exif_utils import get_exif_data, format_exif_data
from controllers.metadata_controller import MetadataController

class RightSidebar(QWidget):
    """
    Widget laterale destro che mostra i metadati EXIF e altre informazioni sull'immagine.
    """
    
    def __init__(self, parent=None):
        """Inizializza la sidebar destra."""
        super().__init__(parent)
        self.current_image_path = None
        self.exif_data = {}
        # Controller MVC
        self.controller = MetadataController(self)
        
        self._setup_ui()
    
    def _setup_ui(self):
        """Configura l'interfaccia utente della sidebar."""
        # Layout principale
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(5)
        
        # Area di scorrimento per i metadati
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setFrameShape(QFrame.NoFrame)
        
        # Widget contenitore per i metadati
        self.metadata_container = QWidget()
        self.metadata_layout = QVBoxLayout(self.metadata_container)
        self.metadata_layout.setContentsMargins(2, 2, 2, 2)
        self.metadata_layout.setSpacing(3)
        self.metadata_layout.addStretch()
        
        # Aggiungi il contenitore all'area di scorrimento
        scroll_area.setWidget(self.metadata_container)
        
        # Aggiungi l'area di scorrimento al layout principale
        layout.addWidget(scroll_area)
        
        # Stile
        self.setStyleSheet("""
            QLabel {
                color: #e0e0e0;
                font-size: 11px;
                margin: 0;
                padding: 0;
            }
            QLabel[title="section"] {
                color: #4fc3f7;
                font-weight: bold;
                margin: 8px 0 2px 0;
                font-size: 11px;
                border-bottom: 1px solid #444;
                padding-bottom: 1px;
            }
            QLabel[title="value"] {
                color: #b0b0b0;
                margin: 0;
                padding: 0;
            }
        """)
    
    def show_image_info(self, image_path: Optional[str] = None):
        """Mostra le informazioni dell'immagine corrente.
        
        Args:
            image_path: Percorso dell'immagine di cui mostrare le informazioni.
        """
        self.controller.load_metadata(image_path)
    
    def _add_metadata_section(self, title: str):
        """Aggiunge un'intestazione di sezione."""
        label = QLabel(title.upper())
        label.setProperty("class", "metadata-section")
        label.setAttribute(Qt.WA_TranslucentBackground)
        label.setStyleSheet("""
            QLabel {
                color: #4fc3f7;
                font-weight: bold;
                margin-top: 15px;
                margin-bottom: 5px;
                font-size: 13px;
                border-bottom: 1px solid #444;
                padding-bottom: 3px;
            }
        """)
        self.metadata_layout.insertWidget(self.metadata_layout.count() - 1, label)
    
    def _add_metadata_item(self, label: str, value: str):
        """Aggiunge una voce di metadato."""
        if not value:
            return
            
        # Crea un widget orizzontale per contenere label e valore
        container = QWidget()
        layout = QHBoxLayout(container)
        layout.setContentsMargins(2, 0, 2, 0)
        layout.setSpacing(5)
        
        # Label a larghezza fissa
        label_widget = QLabel(f"{label}:")
        label_widget.setStyleSheet("color: #e0e0e0; font-weight: bold;")
        label_widget.setMinimumWidth(120)
        label_widget.setMaximumWidth(120)
        
        # Valore che occupa lo spazio rimanente
        value_widget = QLabel(value)
        value_widget.setStyleSheet("color: #b0b0b0; padding: 0; margin: 0;")
        value_widget.setWordWrap(True)
        
        layout.addWidget(label_widget, 0, Qt.AlignLeft | Qt.AlignTop)
        layout.addWidget(value_widget, 1)
        
        self.metadata_layout.insertWidget(self.metadata_layout.count() - 1, container)
        container.setMaximumHeight(40)  # Altezza massima per ogni riga
    
    def _clear_metadata(self):
        """Rimuove tutti i widget dei metadati."""
        # Rimuovi tutti i widget tranne l'ultimo stretch
        while self.metadata_layout.count() > 1:
            item = self.metadata_layout.takeAt(0)
            widget = item.widget()
            if widget:
                widget.deleteLater()
        
        # Assicurati che ci sia sempre uno stretch alla fine
        if self.metadata_layout.count() == 0:
            self.metadata_layout.addStretch()
