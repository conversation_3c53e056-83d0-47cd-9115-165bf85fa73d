#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Implementazione del pannello sinistro dell'applicazione.
Contiene la lista delle immagini e i controlli per la rinomina.
"""

import logging
import os
import time
from typing import List, Dict, Optional, Any
from functools import partial

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QFileDialog,
    QListWidget, QListWidgetItem, QLineEdit, QProgressBar, QSlider,
    QMessageBox, QSizePolicy, QComboBox, QAbstractItemView
)
from PySide6.QtCore import Qt, QSize, Signal, Slot, QTimer, QMimeData, QPoint, QRect
from PySide6.QtGui import QIcon, QIntValidator, QPixmap, QColor, QDrag, QPainter, QPen, QFont

from services.image_loader import ImageLoader
from services.image_loader import ThumbnailWorker
from models.file_renamer import FileRenamer
from utils.constants import THUMBNAIL_SIZE
from ui.styles import Styles
from controllers.image_list_controller import ImageListController
from utils.logging_config import get_logger

logger = get_logger("PhotoRenamer.LeftPanel")

class DraggableListWidget(QListWidget):
    """
    QListWidget custom per drag&drop ordinamento.
    Aggiorna l'ordine delle immagini nel pannello sinistro dopo il drop.
    """
    items_reordered = Signal(list)  # Segnale per notificare riordinamento
    
    def __init__(self, parent_panel):
        super().__init__(parent_panel)
        self.parent_panel = parent_panel
        self.controller = parent_panel.controller # Aggiunta inizializzazione controller
        self._drop_indicator_position = -1  # Posizione dell'indicatore di drop
        
        logger.info('[DND] DraggableListWidget inizializzato')
        # Configurazione drag&drop manuale per evitare conflitti
        self.setAcceptDrops(True) # Assicura che il widget accetti i drop
        self.setDragDropMode(QListWidget.DragDropMode.DragDrop) # Abilita il drag, ma non il drop automatico interno
        self.setDefaultDropAction(Qt.DropAction.MoveAction)
        self.setSelectionMode(QListWidget.SelectionMode.ExtendedSelection)
        # Disabilita il riordinamento automatico di Qt, la nostra logica manuale lo gestirà
        self.setDragDropOverwriteMode(False) 
        
        logger.info(f"[DND] DraggableListWidget configurato con gestione manuale")


    def dragEnterEvent(self, event):
        """Gestisce l'ingresso del drag nella lista"""
        if event.mimeData().hasFormat('application/x-qabstractitemmodeldatalist'):
            event.acceptProposedAction()
        else:
            event.ignore()
    
    def dragMoveEvent(self, event):
        """Gestisce il movimento durante il drag per mostrare la posizione di drop"""
        if event.mimeData().hasFormat('application/x-qabstractitemmodeldatalist'):
            # Calcola la posizione di inserimento con maggiore precisione
            drop_position = event.position().toPoint()
            item_at_position = self.itemAt(drop_position)
            
            if item_at_position:
                # Calcola se inserire sopra o sotto l'item con margine più preciso
                item_rect = self.visualItemRect(item_at_position)
                # Usa un terzo superiore/inferiore invece del centro per maggiore precisione
                threshold_y = item_rect.top() + (item_rect.height() * 0.33)
                if drop_position.y() < threshold_y:
                    self._drop_indicator_position = self.row(item_at_position)
                else:
                    self._drop_indicator_position = self.row(item_at_position) + 1
            else:
                # Drop alla fine della lista
                self._drop_indicator_position = self.count()
            
            event.acceptProposedAction()
            self.viewport().update()  # Aggiorna la visualizzazione
            logger.debug(f'[DND] Drag move: posizione indicatore {self._drop_indicator_position}')
        else:
            event.ignore()
    
    def dropEvent(self, event):
        """Override del dropEvent per gestire la posizione precisa del drop"""
        if not event.mimeData().hasFormat('application/x-qabstractitemmodeldatalist'):
            event.ignore()
            return
        
        # Ottieni gli item selezionati prima del drop
        selected_items = self.selectedItems()
        if not selected_items:
            event.ignore()
            return
        
        # Calcola la posizione di drop usando la stessa logica di dragMoveEvent
        drop_position = event.position().toPoint()
        target_item = self.itemAt(drop_position)
        
        if target_item:
            target_row = self.row(target_item)
            item_rect = self.visualItemRect(target_item)
            # Usa la stessa logica di dragMoveEvent per coerenza
            threshold_y = item_rect.top() + (item_rect.height() * 0.33)
            if drop_position.y() >= threshold_y:
                target_row += 1
        else:
            target_row = self.count()
        
        logger.info(f'[DND] Drop iniziato: posizione target calcolata {target_row}')
        
        # IMPORTANTE: Accetta l'evento PRIMA del riordinamento per prevenire il comportamento di default
        event.accept()
        
        # Esegui il riordinamento manuale
        self._perform_manual_reorder(selected_items, target_row)
        
        # Nasconde l'indicatore dopo il drop
        self._drop_indicator_position = -1
        self.viewport().update()
        
        logger.info(f'[DND] Drop completato alla posizione target {target_row}')
    
    def _perform_manual_reorder(self, selected_items, target_row):
        """Esegue il riordinamento manuale degli elementi"""
        logger.info(f'[DND] Inizio riordinamento: {len(selected_items)} elementi verso posizione {target_row}')
        logger.info(f'[DND] Numero totale elementi prima: {self.count()}')
        
        # Raccogli i dati degli elementi selezionati
        items_data = []
        for item in selected_items:
            current_icon = item.icon()
            logger.debug(f"[DND._perform_manual_reorder] Icona per {item.text()}: icon.isNull: {current_icon.isNull()}, icon.availableSizes: {current_icon.availableSizes()}")
            items_data.append({
                'text': item.text(),
                'icon': current_icon, # Salva l'icona originale
                'data': item.data(Qt.ItemDataRole.UserRole),
                'row': self.row(item) # Salva la riga originale
            })

        # Ordina gli elementi selezionati per la loro riga originale in ordine decrescente
        # Questo previene problemi di indice quando si rimuovono più elementi
        items_data_ordered_by_original_row = sorted(items_data, key=lambda x: x['row'], reverse=True)
        
        logger.info(f"[DND] Elementi da spostare: {[d['text'] for d in items_data_ordered_by_original_row]}")
        logger.info(f"[DND] Posizioni originali: {[d['row'] for d in items_data_ordered_by_original_row]}")

        # Rimuovi gli elementi selezionati dalla lista (partendo dal basso)
        for item_data_value_dict in items_data_ordered_by_original_row:
            original_row = item_data_value_dict['row']
            removed_item = self.takeItem(original_row)
            if removed_item:
                logger.info(f"[DND] Rimosso elemento \"{removed_item.text()}\" dalla posizione {original_row}")
            del removed_item # Assicurati che l'oggetto C++ sia eliminato
        logger.info(f'[DND] Numero elementi dopo rimozione: {self.count()}')

        # Calcola la posizione di inserimento corretta
        # Se target_row è -1, significa che il drop è avvenuto su un'area vuota in fondo
        adjusted_target_row = target_row
        if adjusted_target_row == -1 or adjusted_target_row > self.count():
            adjusted_target_row = self.count() # Inserisci alla fine
        
        # Riordina items_data per riga originale crescente per reinserirli nell'ordine corretto
        items_data_ordered_for_insertion = sorted(items_data, key=lambda x: x['row'])

        # Reinserisci gli elementi nella nuova posizione
        for i, item_data_value_dict in enumerate(items_data_ordered_for_insertion):
            item_text = item_data_value_dict['text']
            original_icon = item_data_value_dict['icon'] # ICONA ORIGINALE
            user_data = item_data_value_dict['data']

            new_item = QListWidgetItem()
            new_item.setText(item_text)
            new_item.setData(Qt.ItemDataRole.UserRole, user_data)
            
            # UTILIZZA L'ICONA ORIGINALE RECUPERATA PRECEDENTEMENTE
            if original_icon and not original_icon.isNull():
                new_item.setIcon(original_icon)
                logger.debug(f"[DND] Reimpostata icona originale per {item_text}. isNull: {original_icon.isNull()}, availableSizes: {original_icon.availableSizes()}")
            else:
                # Fallback molto improbabile, ma per sicurezza
                logger.warning(f"[DND] Icona originale per {item_text} non valida o mancante durante il reinserimento. Uso placeholder.")
                # Crea un'icona placeholder semplice
                placeholder_pixmap = QPixmap(100, 60)
                placeholder_pixmap.fill(Qt.GlobalColor.lightGray)
                new_item.setIcon(QIcon(placeholder_pixmap))
            
            actual_insert_row = adjusted_target_row + i
            self.insertItem(actual_insert_row, new_item)
            logger.info(f'[DND] Reinserito elemento "{item_text}" alla posizione {actual_insert_row}')
        
        logger.info(f'[DND] Numero elementi dopo inserimento: {self.count()}')

        # Seleziona gli elementi appena spostati
        for i in range(len(items_data)):
            item_to_select = self.item(adjusted_target_row + i)
            if item_to_select:
                item_to_select.setSelected(True)
                logger.info(f'[DND] Selezionato elemento "{item_to_select.text()}" alla posizione {adjusted_target_row + i}')

        self.clearSelection() # Deseleziona tutto prima
        if len(items_data) == 1:
            self.setCurrentRow(adjusted_target_row)
        else: # Se si spostano più elementi, seleziona il primo del blocco spostato
             first_moved_item_index = adjusted_target_row
             self.setCurrentRow(first_moved_item_index)

        logger.debug(f"[DND] _perform_manual_reorder: Elementi spostati internamente. Nuova lista: {[self.item(i).text() for i in range(self.count())]}")

        # Sincronizza lo stato del controller principale dopo il riordino
        # logger.debug(f"[DND] _perform_manual_reorder: Tentativo di chiamata a _sync_controller_after_reorder su {self.parent_panel} (tipo: {type(self.parent_panel)})")
        if hasattr(self.parent_panel, '_sync_controller_after_reorder'):
            self.parent_panel._sync_controller_after_reorder() # Chiamare il metodo su LeftPanel
            logger.debug(f"[DND] _perform_manual_reorder: Chiamato _sync_controller_after_reorder su {self.parent_panel}")
        else:
            logger.error(f"[DND] Errore: self.parent_panel ({type(self.parent_panel)}) non ha il metodo _sync_controller_after_reorder")

        # Emetti un segnale per notificare che l'ordine è cambiato manualmente
        # Questo potrebbe non essere più necessario se _sync_controller_after_reorder gestisce tutto
        self.items_reordered.emit([self.item(i).text() for i in range(self.count())])
        logger.info(f'[DND] Riordinamento manuale completato: {len(items_data)} elementi spostati alla posizione {adjusted_target_row}')
    
    def dragLeaveEvent(self, event):
        """Nasconde l'indicatore quando il drag esce dalla lista"""
        self._drop_indicator_position = -1
        self.viewport().update()
        super().dragLeaveEvent(event)

    def paintEvent(self, event):
        """Disegna l'indicatore di drop durante il drag"""
        super().paintEvent(event)
        
        # Disegna l'indicatore di drop se necessario
        if self._drop_indicator_position >= 0:
            painter = QPainter(self.viewport())
            painter.setPen(QPen(QColor(0, 120, 215), 2))  # Blu Windows
            
            if self._drop_indicator_position < self.count():
                # Disegna sopra l'elemento alla posizione indicata
                item = self.item(self._drop_indicator_position)
                if item:
                    rect = self.visualItemRect(item)
                    painter.drawLine(rect.left(), rect.top(), rect.right(), rect.top())
            else:
                # Disegna alla fine della lista
                if self.count() > 0:
                    last_item = self.item(self.count() - 1)
                    if last_item:
                        rect = self.visualItemRect(last_item)
                        painter.drawLine(rect.left(), rect.bottom(), rect.right(), rect.bottom())

    def startDrag(self, supportedActions) -> None:
        # ui/left_panel.py
        logger.critical("CUSTOM STARTDRAG CALLED!") # Log di test molto visibile

        if not self.selectedItems():
            logger.warning("[DND.startDrag] No items selected, exiting startDrag.")
            return

        drag = QDrag(self)
        mime_data = self.mimeData(self.selectedItems())
        drag.setMimeData(mime_data)

        item = self.selectedItems()[0]
        icon = item.icon()
        pixmap = QPixmap()  # Inizializza a un pixmap nullo/vuoto

        if icon.isNull():
            logger.warning(f"[DND.startDrag] Icon for {item.text()} is null.")
        else:
            available_sizes = icon.availableSizes()
            if available_sizes:
                # Usa la prima dimensione disponibile (spesso l'unica, es. 100x60)
                # Questo dovrebbe corrispondere al thumbnail originale.
                pixmap_size_to_request = available_sizes[0]
                pixmap = icon.pixmap(pixmap_size_to_request)
                logger.debug(f"[DND.startDrag] Pixmap for {item.text()} requested with available_sizes[0] ({pixmap_size_to_request}).isNull: {pixmap.isNull()}, Size: {pixmap.size()}")
            else:
                logger.warning(f"[DND.startDrag] Icon for {item.text()} has no available_sizes. Trying with self.iconSize().")
                # Fallback a iconSize se non ci sono available_sizes (improbabile se l'icona è valida)
                pixmap = icon.pixmap(self.iconSize())
                logger.debug(f"[DND.startDrag] Pixmap for {item.text()} requested with self.iconSize() ({self.iconSize()}).isNull: {pixmap.isNull()}, Size: {pixmap.size()}")

        if pixmap.isNull():
            logger.warning(f"[DND.startDrag] Pixmap for {item.text()} is still null. Using placeholder.")
            pixmap = QPixmap(self.iconSize() if not self.iconSize().isEmpty() else QSize(100,60)) # Assicura una dimensione valida
            pixmap.fill(Qt.GlobalColor.transparent)
            painter = QPainter(pixmap)
            try:
                painter.setPen(Qt.GlobalColor.gray)
                painter.drawText(pixmap.rect(), Qt.AlignmentFlag.AlignCenter, "...")
            finally:
                painter.end()
        else:
            logger.debug(f"[DND.startDrag] Pixmap for {item.text()} successfully created. Size: {pixmap.size()}")

        drag.setPixmap(pixmap)
        drag.setHotSpot(pixmap.rect().center())

        logger.info(f"[DND.startDrag] Avvio drag per {item.text()} con pixmap size {pixmap.size()}")
        # drag.exec() è bloccante; i log successivi appaiono solo dopo il drop o la cancellazione.
        if drag.exec(supportedActions, Qt.DropAction.MoveAction) == Qt.DropAction.MoveAction:
            # La rimozione dell'elemento avviene in dropEvent se l'azione è accettata
            logger.debug(f"[DND.startDrag] Drag operation completed with MoveAction for {item.text()}")
        else:
            logger.debug(f"[DND.startDrag] Drag operation cancelled or failed for {item.text()}")

class LeftPanel(QWidget):
    # Segnali personalizzati
    folder_changed = Signal(str)  # Emesso quando la cartella viene modificata
    images_loaded = Signal(list)  # Emesso quando le immagini sono caricate
    selection_changed = Signal(str)  # Emette il percorso del file dell'immagine selezionata

    def _update_image_paths_from_widget(self):
        """
        Aggiorna self.image_paths secondo l'ordine corrente degli item nel widget dopo un drag&drop.
        Estrae il percorso dell'immagine dal dizionario memorizzato in Qt.UserRole.
        """
        new_paths = []
        for i in range(self.photo_list_widget.count()):
            item = self.photo_list_widget.item(i)
            if item:
                item_data = item.data(Qt.ItemDataRole.UserRole) # Questo dovrebbe essere un dizionario
                if isinstance(item_data, dict):
                    path = item_data.get('image_path')
                    if path:
                        new_paths.append(path)
                    else:
                        logger.warning(f"[DND] 'image_path' non trovato nel dizionario UserRole per l'item {item.text()}")
                elif isinstance(item_data, str): # Fallback se per errore fosse una stringa
                    logger.warning(f"[DND] UserRole per l'item {item.text()} è una stringa: {item_data}. Previsto dizionario. Usando la stringa come percorso.")
                    new_paths.append(item_data)
                else:
                    logger.error(f"[DND] Dati utente (UserRole) non validi o mancanti per l'item {item.text()}: {item_data}")
            else:
                logger.warning(f"[DND] Trovato item nullo all'indice {i} durante l'aggiornamento dei percorsi.")
        self.image_paths = new_paths
        logger.info(f"[DND] Ordine immagini aggiornato dopo drag&drop: {len(self.image_paths)} immagini")
    
    def _sync_controller_after_reorder(self):
        """
        Sincronizza il controller dopo il riordinamento drag&drop.
        """
        # Aggiorna il controller con il nuovo ordine
        self.controller.set_image_list(self.image_paths)
        
        # Mantieni la selezione corrente se possibile
        current_item = self.photo_list_widget.currentItem()
        if current_item:
            current_path = current_item.data(Qt.ItemDataRole.UserRole)
            if current_path in self.image_paths:
                new_index = self.image_paths.index(current_path)
                self.controller.select_image(new_index)
        
        # Emetti il segnale di aggiornamento
        self.images_loaded.emit(self.image_paths)
        logger.info(f"[DND] Controller sincronizzato dopo riordinamento")

    """
    Pannello sinistro dell'applicazione.
    Contiene la lista delle immagini e i controlli per la rinomina.
    """
    def __init__(self, parent=None):
        """
        Inizializza il pannello sinistro.
        
        Args:
            parent (QWidget, optional): Widget genitore
        """
        super().__init__(parent)
        logger.info("LeftPanel inizializzato - test logger")

        # 🔧 NUOVO: Carica ultima cartella se abilitato
        self._load_last_folder_if_enabled()
        
        # Riferimento all'applicazione principale
        self.main_app = parent
        
        # Stato del pannello
        self.current_folder = ""  # Cartella corrente
        self.image_paths = []  # Percorsi delle immagini
        self.selected_image = None  # Immagine selezionata
        self.loader = None  # Caricatore di immagini
        self.pending_images_data = [] # Lista temporanea per i dati delle immagini caricate
        self.placeholder_icon_path = "assets/icons/image_placeholder.svg" # Aggiunto percorso icona placeholder

        # 🔧 NUOVO: Cache per icone placeholder
        self._placeholder_cache = {}
        self._create_placeholder_icons()
        
        # Controller MVC
        image_cache = None
        if hasattr(self.main_app, 'image_cache'):
            image_cache = getattr(self.main_app, 'image_cache', None)
        else:
            logger.error("LeftPanel: self.main_app non ha image_cache. ImageListController potrebbe non avere ImageCache.")
            
        self.controller = ImageListController(self, image_cache_instance=image_cache)
         
        # Inizializza l'interfaccia utente
        self._setup_ui()
        self._connect_signals()
    
    def _setup_ui(self):
        """
        Configura l'interfaccia utente del pannello sinistro.
        """
        # Layout principale
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)
        
        # Header con selezione cartella
        header_layout = QHBoxLayout()
        header_layout.setSpacing(5)
        
        self.folder_label = QLabel("Nessuna cartella selezionata")
        self.folder_label.setStyleSheet("color: #aaa; font-style: italic;")
        
        self.folder_button = QPushButton()
        self.folder_button.setIcon(QIcon("assets/icons/folder_duotone.svg"))
        self.folder_button.setToolTip("Seleziona cartella")
        self.folder_button.setFixedSize(32, 32)
        self.folder_button.setStyleSheet(Styles.ICON_BUTTON_STYLE)
        
        header_layout.addWidget(self.folder_label, 1)
        header_layout.addWidget(self.folder_button)
        
        main_layout.addLayout(header_layout)
        
        # Lista delle foto
        self.photo_list_widget = DraggableListWidget(self)
        logger.info('[DND] LeftPanel: creata istanza DraggableListWidget')
        # RIMOSSA: self.photo_list_widget.setDragDropMode(QListWidget.InternalMove) # Forza modalità drag&drop
        self._setup_list_widget(self.photo_list_widget)
        
        # Controllo dimensioni thumbnail
        thumb_layout = QHBoxLayout()
        thumb_layout.setContentsMargins(0, 5, 0, 5)
        thumb_small_label = QLabel("Small")
        thumb_small_label.setStyleSheet("color: #aaa;")
        thumb_large_label = QLabel("Large")
        thumb_large_label.setStyleSheet("color: #aaa;")
        
        self.thumbnail_slider = QSlider(Qt.Orientation.Horizontal)
        self.thumbnail_slider.setRange(50, 400)  # Aumentato il valore massimo da 200 a 400
        self.thumbnail_slider.setValue(THUMBNAIL_SIZE[0])
        self.thumbnail_slider.setFixedHeight(20)
        
        thumb_layout.addWidget(thumb_small_label)
        thumb_layout.addWidget(self.thumbnail_slider, 1)
        thumb_layout.addWidget(thumb_large_label)
        
        # Layout per i controlli di ordinamento
        sort_layout = QHBoxLayout()
        sort_layout.setContentsMargins(0, 0, 0, 0)
        
        sort_label = QLabel("Ordina per:")
        sort_label.setStyleSheet("color: #aaa;")
        
        self.sort_combo = QComboBox()
        self.sort_combo.addItems([
            "Nome (A→Z)", 
            "Nome (Z→A)", 
            "Data (vecchie→nuove)", 
            "Data (nuove→vecchie)",
            "Manuale"
        ])
        
        sort_layout.addWidget(sort_label)
        sort_layout.addWidget(self.sort_combo, 1)
        
        # Barra di controllo
        control_layout = QHBoxLayout()
        control_layout.setSpacing(5)
        
        self.rename_button = QPushButton()
        self.rename_button.setIcon(QIcon("assets/icons/rename_duotone.svg"))
        self.rename_button.setToolTip("Rinomina file selezionati")
        self.rename_button.setFixedSize(32, 32)
        self.rename_button.setStyleSheet(Styles.ICON_BUTTON_STYLE)
        
        self.move_up_button = QPushButton()
        self.move_up_button.setIcon(QIcon("assets/icons/arrow_up_duotone.svg"))
        self.move_up_button.setToolTip("Sposta su")
        self.move_up_button.setFixedSize(32, 32)
        self.move_up_button.setStyleSheet(Styles.ICON_BUTTON_STYLE)
        
        self.move_down_button = QPushButton()
        self.move_down_button.setIcon(QIcon("assets/icons/arrow_down_duotone.svg"))
        self.move_down_button.setToolTip("Sposta giù")
        self.move_down_button.setFixedSize(32, 32)
        self.move_down_button.setStyleSheet(Styles.ICON_BUTTON_STYLE)
        
        self.delete_button = QPushButton()
        self.delete_button.setIcon(QIcon("assets/icons/trash_duotone.svg"))
        self.delete_button.setToolTip("Elimina selezionati")
        self.delete_button.setFixedSize(32, 32)
        self.delete_button.setStyleSheet(Styles.ICON_BUTTON_STYLE)

        control_layout.addWidget(self.rename_button)
        control_layout.addStretch()
        control_layout.addWidget(self.move_up_button)
        control_layout.addWidget(self.move_down_button)
        control_layout.addStretch()

        control_layout.addWidget(self.delete_button)
        
        # Controlli per la rinomina
        rename_controls = QWidget()
        rename_controls.setStyleSheet("background-color: transparent;")
        rename_layout = QVBoxLayout(rename_controls)
        rename_layout.setContentsMargins(0, 10, 0, 10)
        
        # Prima riga: prefisso e numero iniziale
        prefix_layout = QHBoxLayout()
        prefix_label = QLabel("Prefisso:")
        prefix_label.setFixedWidth(80)
        prefix_label.setStyleSheet("color: #FFFFFF; font-weight: bold; background-color: #444444; padding: 3px 8px; border-radius: 3px;")
        self.prefix_edit = QLineEdit("img_")
        self.prefix_edit.setStyleSheet("background-color: #2A2A2A; color: #FFFFFF;")
        
        number_label = QLabel("Inizio da:")
        number_label.setFixedWidth(80)
        number_label.setStyleSheet("color: #FFFFFF; font-weight: bold; background-color: #444444; padding: 3px; border-radius: 3px;")
        self.number_edit = QLineEdit("1")
        self.number_edit.setValidator(QIntValidator(1, 9999))
        self.number_edit.setFixedWidth(60)
        self.number_edit.setStyleSheet("background-color: #2A2A2A; color: #FFFFFF;")
        
        prefix_layout.addWidget(prefix_label)
        prefix_layout.addWidget(self.prefix_edit, 1)
        prefix_layout.addWidget(number_label)
        prefix_layout.addWidget(self.number_edit)
        
        rename_layout.addLayout(prefix_layout)
        
        # Seconda riga: barra di progresso
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        self.progress_bar.setVisible(False)
        
        rename_layout.addWidget(self.progress_bar)
        
        # Aggiungi tutti i layout al layout principale
        main_layout.addWidget(self.photo_list_widget, 1)  # 1 = fattore di espansione
        main_layout.addLayout(thumb_layout)
        main_layout.addLayout(sort_layout)
        main_layout.addLayout(control_layout)
        main_layout.addWidget(rename_controls)
    
    def _setup_list_widget(self, list_widget):
        """Configura il widget della lista delle immagini."""
        # Parametri base
        icon_size = 100
        cell_width = icon_size + 20  # Larghezza cella = dimensione icona + margini
        cell_height = icon_size + 40  # Altezza cella = dimensione icona + spazio per testo

        # Modalità griglia con miniature
        list_widget.setViewMode(QListWidget.ViewMode.IconMode)  # Visualizza come griglia
        list_widget.setMovement(QListWidget.Movement.Snap)      # Snap: le miniature si allineano alla griglia e il drag&drop funziona tra tutte le posizioni
        list_widget.setDefaultDropAction(Qt.DropAction.MoveAction)  # Assicura il comportamento di spostamento
        list_widget.setResizeMode(QListWidget.ResizeMode.Adjust)  # Adatta la griglia alle dimensioni
        list_widget.setGridSize(QSize(cell_width, cell_height))
        list_widget.setIconSize(QSize(icon_size, icon_size))
        list_widget.setSpacing(10)  # Spazio tra le celle
        list_widget.setUniformItemSizes(True)

        # Drag&Drop robusto
        list_widget.setDragDropMode(QListWidget.DragDropMode.InternalMove)
        list_widget.setAcceptDrops(True)
        list_widget.setDragEnabled(True)
        list_widget.setDropIndicatorShown(True)
        list_widget.setEditTriggers(QListWidget.EditTrigger.NoEditTriggers)
        list_widget.setSelectionMode(QListWidget.SelectionMode.ExtendedSelection)

        # Aggiungi gestione per lo scorrimento (lazy loading)
        list_widget.verticalScrollBar().valueChanged.connect(self._on_scroll)

        list_widget.currentItemChanged.connect(self._on_current_item_changed)
        logger.debug(f"[LP.__init__] Connesso segnale currentItemChanged a _on_current_item_changed") # DEBUG

    def _connect_signals(self):
        """Connette i segnali agli slot."""
        self.folder_button.clicked.connect(self._select_folder)
        self.thumbnail_slider.valueChanged.connect(self._update_thumbnail_size)
        self.photo_list_widget.itemClicked.connect(self._on_item_clicked)
        self.photo_list_widget.itemDoubleClicked.connect(self._on_item_double_clicked)
        self.move_up_button.clicked.connect(self._move_item_up)
        self.move_down_button.clicked.connect(self._move_item_down)
        self.delete_button.clicked.connect(self._delete_selected_photos)
        self.rename_button.clicked.connect(self._rename_files)


        self.sort_combo.currentIndexChanged.connect(self._apply_sort_filter)
    
    def _select_folder(self):
        """Apre il dialogo di selezione cartella e carica le immagini."""
        folder = QFileDialog.getExistingDirectory(self, "Seleziona Cartella Immagini")
        if folder:
            self.current_folder = folder
            self.folder_label.setText(folder)
            
            # Emette il segnale di cambio cartella
            self.folder_changed.emit(folder)
            
            # Avvia il caricamento asincrono delle immagini
            self._load_images_async(folder)
            
            # Aggiorna il footer
            self._update_footer()
    
    def _load_images_async(self, folder_path):
        """
        Carica le immagini in modo asincrono.
        
        Args:
            folder_path (str): Percorso della cartella da cui caricare le immagini
        """
        # Se c'è già un caricamento in corso, fermalo
        if self.loader and hasattr(self.loader, 'stop'):
            self.loader.stop()
            
        # Pulisci la lista
        self.photo_list_widget.clear()
        self.image_paths = []
        self.pending_images_data = [] # Resetta la lista temporanea
        
        # Crea e avvia il caricatore
        self.loader = ImageLoader(folder_path)
        self.loader.image_loaded.connect(self._on_image_loaded)
        self.loader.finished.connect(self._on_loading_finished)
        self.loader.load()
        
        logger.info(f"Caricamento immagini avviato da: {folder_path}")
    
    def _on_image_loaded(self, image_data):
        """
        Gestisce l'evento di caricamento di una singola immagine.
        Aggiunge i dati dell'immagine a una lista temporanea.
        
        Args:
            image_data (dict): Dati dell'immagine caricata
        """
        self.pending_images_data.append(image_data)
        # Non aggiornare il footer qui, lo faremo in _on_loading_finished
        
    def _on_loading_finished(self, loaded_image_data_list_from_loader):
        """
        Gestisce l'evento di completamento del caricamento delle immagini.
        Ordina le immagini caricate e le aggiunge alla lista.
        
        Args:
            loaded_image_data_list_from_loader (list): Lista dei dati delle immagini caricate da ImageLoader (potrebbe non essere usata direttamente se pending_images_data è la fonte autorevole).
        """
        # self.image_paths è già stato resettato in _load_images_async

        # Ordina la lista di dizionari 'pending_images_data' per la chiave 'name'
        sorted_images_data = sorted(self.pending_images_data, key=lambda x: x['name'])

        for image_data in sorted_images_data:
            self.image_paths.append(image_data['path'])
            
            item = QListWidgetItem()
            item.setText(image_data['name']) # Usa image_data['name'] che è il nome del file
            # Modifica: memorizza un dizionario contenente il percorso e altre info utili
            user_data_dict = {
                'image_path': image_data['path'],
                'name': image_data['name']
                # Aggiungi altri dati se necessario, es. timestamp, dimensioni originali
            }
            item.setData(Qt.ItemDataRole.UserRole, user_data_dict)
            
            if 'thumbnail' in image_data and image_data['thumbnail']:
                thumbnail_pixmap = image_data['thumbnail']
                logger.debug(f"[LP._on_loading_finished] Thumbnail per {image_data['name']}: tipo {type(thumbnail_pixmap)}, isNull: {thumbnail_pixmap.isNull()}, size: {thumbnail_pixmap.size()}")
                icon = QIcon(thumbnail_pixmap)
                item.setIcon(icon)
                logger.debug(f"[LP._on_loading_finished] Icona impostata per {image_data['name']}: icon.isNull: {icon.isNull()}, icon.availableSizes: {icon.availableSizes()}")
            else:
                logger.warning(f"[LP._on_loading_finished] Miniatura mancante o non valida per {image_data['name']}")
            
            self.photo_list_widget.addItem(item)

        # Pulisci la lista temporanea ora che è stata usata
        self.pending_images_data = []

        # Aggiorna la lista immagini anche nel controller (fondamentale per la selezione!)
        self.controller.set_image_list(self.image_paths)

        # Emette il segnale di immagini caricate con la lista ordinata dei percorsi
        self.images_loaded.emit(self.image_paths)
        logger.info(f"Caricamento immagini completato e lista ordinata: {len(self.image_paths)} immagini")
        
        # Aggiorna il footer
        self._update_footer()
    
    def _update_thumbnail_size(self, size):
        """
        Aggiorna la dimensione delle miniature nella lista.
        
        Args:
            size (int): Nuova dimensione delle miniature (in pixel)
        """
        # Assicurati che la dimensione sia ragionevole (permettendo miniature fino a 400 pixel)
        size = max(32, min(400, size))  # Limita tra 32 e 400 pixel
        
        # Registra la dimensione per debug
        logger.info(f"Aggiornamento dimensione miniature a {size}x{size} pixel")
        
        # Calcola le dimensioni della cella in base alla dimensione dell'icona
        cell_width = size + 20  # Larghezza cella = dimensione icona + margini
        cell_height = size + 40  # Altezza cella = dimensione icona + spazio per testo
        
        # Aggiorna le dimensioni della griglia
        self.photo_list_widget.setIconSize(QSize(size, size))
        self.photo_list_widget.setGridSize(QSize(cell_width, cell_height))
        
        # Aggiorna le icone esistenti
        for i in range(self.photo_list_widget.count()):
            item = self.photo_list_widget.item(i)
            user_data = item.data(Qt.ItemDataRole.UserRole)

            # Estrai il percorso dal dizionario UserRole
            if isinstance(user_data, dict):
                path = user_data.get('image_path')
            else:
                path = user_data  # Fallback per compatibilità

            if not path:
                continue

            # Carica direttamente l'immagine senza passare dalla cache
            pixmap = QPixmap(path)
            if not pixmap.isNull():
                # Genera una nuova miniatura della dimensione richiesta
                scaled_pixmap = pixmap.scaled(
                    size, size,
                    Qt.AspectRatioMode.KeepAspectRatio,
                    Qt.TransformationMode.SmoothTransformation
                )
                item.setIcon(QIcon(scaled_pixmap))
                # Forza il ridimensionamento dell'elemento nella lista
                item.setSizeHint(QSize(cell_width, cell_height))
        
        # Aggiornamento non necessario, le modifiche sono già applicate
    
    def _on_scroll(self, value):
        """
        Gestisce l'evento di scorrimento della lista per implementare il lazy loading.

        Args:
            value (int): Valore corrente della barra di scorrimento
        """
        # Parametro non utilizzato ma necessario per la compatibilità del segnale
        _ = value
        # Implementazione del lazy loading qui se necessario
    
    def _on_item_clicked(self, item):
        """
        Gestisce l'evento di clic su un elemento della lista.

        Args:
            item (QListWidgetItem): Elemento cliccato
        """
        user_data = item.data(Qt.ItemDataRole.UserRole)

        # Estrai il percorso dal dizionario UserRole
        if isinstance(user_data, dict):
            image_path = user_data.get('image_path')
        else:
            image_path = user_data  # Fallback per compatibilità

        if image_path:
            index = self.image_paths.index(image_path) if image_path in self.image_paths else -1
            if index != -1:
                self.controller.select_image(index)

            # Aggiorna il footer
            self._update_footer()
            
    def _on_item_double_clicked(self, item):
        """
        Gestisce l'evento di doppio clic su un elemento della lista.
        Quando viene fatto doppio click su una miniatura, seleziona e mostra l'immagine in anteprima.
        Args:
            item (QListWidgetItem): Elemento cliccato due volte
        """
        user_data = item.data(Qt.ItemDataRole.UserRole)

        # Estrai il percorso dal dizionario UserRole
        if isinstance(user_data, dict):
            image_path = user_data.get('image_path')
        else:
            image_path = user_data  # Fallback per compatibilità

        if image_path:
            index = self.image_paths.index(image_path) if image_path in self.image_paths else -1
            if index != -1:
                self.controller.select_image(index)
            self._update_footer()
    
    def _move_item_up(self):
        selected_items = self.photo_list_widget.selectedItems()
        if not selected_items:
            return
        indices = [self.photo_list_widget.row(item) for item in selected_items]
        self.controller.move_up(indices)
    
    def _move_item_down(self):
        selected_items = self.photo_list_widget.selectedItems()
        if not selected_items:
            return
        indices = [self.photo_list_widget.row(item) for item in selected_items]
        self.controller.move_down(indices)
    
    def _delete_selected_photos(self):
        selected_items = self.photo_list_widget.selectedItems()
        if not selected_items:
            return
        indices = [self.photo_list_widget.row(item) for item in selected_items]
        self.controller.delete_selected(indices)
    
    def _rename_files(self):
        selected_items = self.photo_list_widget.selectedItems()
        if not selected_items:
            return
        prefix = self.prefix_edit.text()
        try:
            start_number = int(self.number_edit.text())
        except ValueError:
            start_number = 1
        indices = [self.photo_list_widget.row(item) for item in selected_items]
        self.controller.rename_selected(prefix, start_number, indices)
    
    def _apply_sort_filter(self):
        """Applica il filtro di ordinamento selezionato alla lista immagini."""
        idx = self.sort_combo.currentIndex() if hasattr(self, 'sort_combo') else 0
        self.controller.sort_images(idx)
    
    def _update_footer(self):
        """Aggiorna il testo del piè di pagina con le informazioni sullo stato corrente."""
        if hasattr(self.main_app, "footer_label"):
            num_images = self.photo_list_widget.count()
            num_selected = len(self.photo_list_widget.selectedItems())
            footer_text = f"{num_selected} selezionate di {num_images} immagini"
            
            # Se c'è una cartella attiva, mostrala
            if self.current_folder:
                footer_text += f" | {self.current_folder}"
                
            footer_label = getattr(self.main_app, 'footer_label', None)
            if footer_label:
                footer_label.setText(footer_text)
    
    def get_all_image_paths(self):
        """Restituisce l'elenco di tutti i percorsi delle immagini nella lista.

        Returns:
            list: Lista dei percorsi completi di tutte le immagini.
        """
        image_paths = []

        # Itera su tutti gli elementi della lista
        for i in range(self.photo_list_widget.count()):
            item = self.photo_list_widget.item(i)
            if item:
                # Recupera il percorso dell'immagine dai dati utente dell'elemento
                user_data = item.data(Qt.ItemDataRole.UserRole)

                # Estrai il percorso dal dizionario UserRole
                if isinstance(user_data, dict):
                    path = user_data.get('image_path')
                else:
                    path = user_data  # Fallback per compatibilità

                if path:
                    image_paths.append(path)

        logger.debug(f"Recuperati {len(image_paths)} percorsi di immagini")
        return image_paths

    def get_selected_files(self):
        """Restituisce l'elenco dei percorsi dei file selezionati.

        Returns:
            list: Lista dei percorsi completi dei file selezionati.
        """
        selected_files = []
        selected_items = self.photo_list_widget.selectedItems()

        for item in selected_items:
            # Recupera il percorso dell'immagine dai dati utente dell'elemento
            user_data = item.data(Qt.ItemDataRole.UserRole)

            # Estrai il percorso dal dizionario UserRole
            if isinstance(user_data, dict):
                path = user_data.get('image_path')
            else:
                path = user_data  # Fallback per compatibilità

            if path:
                selected_files.append(path)

        logger.debug(f"Recuperati {len(selected_files)} file selezionati")
        return selected_files

    def refresh_current_folder(self):
        """Ricarica la cartella corrente aggiornando la lista delle immagini."""
        if self.current_folder and os.path.exists(self.current_folder):
            logger.info(f"Aggiornamento cartella: {self.current_folder}")
            self._load_images_async(self.current_folder)
        else:
            logger.warning("Nessuna cartella corrente da aggiornare")

    def reload_current_folder(self):
        """Alias per refresh_current_folder per compatibilità."""
        self.refresh_current_folder()

    def _create_placeholder_icons(self):
        """Crea icone placeholder per diverse dimensioni."""
        try:
            # Dimensioni comuni per le miniature
            sizes = [64, 100, 128, 150, 200]

            for size in sizes:
                # Crea un pixmap placeholder
                pixmap = QPixmap(size, int(size * 0.6))  # Aspect ratio 5:3
                pixmap.fill(Qt.GlobalColor.lightGray)

                # Aggiungi un'icona semplice al centro
                painter = QPainter(pixmap)
                painter.setPen(Qt.GlobalColor.darkGray)
                painter.setFont(QFont("Arial", max(8, size // 8)))
                painter.drawText(pixmap.rect(), Qt.AlignmentFlag.AlignCenter, "IMG")
                painter.end()

                # Salva nella cache
                self._placeholder_cache[size] = QIcon(pixmap)

            logger.debug(f"Creati {len(sizes)} placeholder icons")

        except Exception as e:
            logger.error(f"Errore nella creazione delle icone placeholder: {e}")

    def get_placeholder_icon(self, size=100):
        """
        Restituisce un'icona placeholder della dimensione richiesta.

        Args:
            size (int): Dimensione dell'icona richiesta

        Returns:
            QIcon: Icona placeholder
        """
        # Trova la dimensione più vicina disponibile
        available_sizes = list(self._placeholder_cache.keys())
        if not available_sizes:
            # Fallback: crea un'icona semplice
            pixmap = QPixmap(size, int(size * 0.6))
            pixmap.fill(Qt.GlobalColor.lightGray)
            return QIcon(pixmap)

        # Trova la dimensione più vicina
        closest_size = min(available_sizes, key=lambda x: abs(x - size))
        return self._placeholder_cache[closest_size]
        
    def get_current_image_info(self):
        """
        Restituisce informazioni sull'immagine corrente.
        
        Returns:
            tuple: (indice_corrente, totale_immagini, percorso_cartella)
        """
        current = 0
        total = self.photo_list_widget.count()
        
        # Trova l'indice dell'elemento selezionato
        selected_items = self.photo_list_widget.selectedItems()
        if selected_items:
            current = self.photo_list_widget.row(selected_items[0]) + 1  # +1 per l'utente (1-based)
        
        return current, total, self.current_folder

    def update_thumbnail_for_path(self, image_path: str):
        logger.info(f"Richiesta di aggiornamento thumbnail per: {image_path}")
        try:
            # Normalizza il percorso ricevuto per il confronto
            # Sostituisce i backslash con slash per coerenza interna
            normalized_target_path = os.path.normpath(image_path).replace("\\", "/")
            logger.debug(f"Percorso normalizzato target per l'aggiornamento: {normalized_target_path}")

            for i in range(self.photo_list_widget.count()):
                item = self.photo_list_widget.item(i)
                if not item:
                    logger.debug(f"Item all'indice {i} è None, skippato.")
                    continue

                user_data = item.data(Qt.ItemDataRole.UserRole)

                # Estrai il percorso dal dizionario UserRole
                if isinstance(user_data, dict):
                    item_data_path = user_data.get('image_path')
                else:
                    item_data_path = user_data  # Fallback per compatibilità

                if not item_data_path:
                    logger.debug(f"Item '{item.text()}' all'indice {i} non ha Qt.UserRole path data, skippato.")
                    continue

                # Normalizza il percorso memorizzato nell'item
                normalized_item_path = os.path.normpath(item_data_path).replace("\\", "/")
                logger.debug(f"Controllo item {i}: '{item.text()}', path normalizzato memorizzato: '{normalized_item_path}'")

                if normalized_item_path == normalized_target_path:
                    logger.info(f"Trovato item corrispondente per l'aggiornamento thumbnail: '{item.text()}' (path: {normalized_item_path})")

                    # Definisci la callback che sarà chiamata dal ThumbnailWorker
                    # Questa callback ha l'item specifico 'catturato' dalla closure di partial.
                    def _on_specific_thumbnail_ready(specific_item: QListWidgetItem,
                                                 path: str,                    # da worker
                                                 q_icon: QIcon,                # da worker
                                                 _worker_item_index: Optional[int], # da worker (non usato qui)
                                                 _worker_total_items: Optional[int]): # da worker (non usato qui)
                        # Parametri non utilizzati ma necessari per la compatibilità del segnale
                        _ = _worker_item_index
                        _ = _worker_total_items
                        # path qui è il percorso restituito dal worker, dovrebbe corrispondere a normalized_target_path
                        if q_icon and not q_icon.isNull():
                            logger.info(f"Thumbnail aggiornato per '{path}' pronto. Applicazione all'item: '{specific_item.text()}'")
                            specific_item.setIcon(q_icon)
                            # Forza un repaint dell'item specifico
                            self.photo_list_widget.viewport().update(self.photo_list_widget.visualItemRect(specific_item))
                            logger.debug(f"Richiesto repaint per l'item '{specific_item.text()}' dopo aggiornamento thumbnail.")
                        else:
                            logger.warning(f"Ricevuto QIcon nullo o non valido per l'aggiornamento di '{path}' per l'item '{specific_item.text()}'")

                    # Crea una versione parzialmente applicata della callback, pre-impostando 'specific_item'
                    update_callback_for_worker = partial(_on_specific_thumbnail_ready, item)
                    
                    logger.info(f"Avvio ThumbnailWorker per aggiornare il thumbnail di: {normalized_item_path}")
                    # Assumiamo che ThumbnailWorker chiami callback(image_path, q_icon) 
                    # se item_index è None (o se il costruttore è diverso)
                    # Il costruttore di ThumbnailWorker è (image_path, icon_size, item_index, callback, idx, total)
                    # Passando None per item_index, il worker dovrebbe chiamare la callback con (path, icon)
                    worker = ThumbnailWorker(
                        normalized_item_path,             # image_path
                        self.get_current_thumbnail_size(),# icon_size
                        None,                             # item_index (l'indice dell'item nella lista, non il 'task index')
                        update_callback_for_worker,       # callback
                        0,                                # idx (task index, per questo singolo aggiornamento)
                        1                                 # total (numero di task, solo 1 per questo aggiornamento)
                    )
                    # Assicurati che self.loader e self.loader.thread_pool esistano e siano inizializzati
                    if hasattr(self, 'loader') and self.loader and hasattr(self.loader, 'thread_pool'):
                        self.loader.thread_pool.start(worker)
                        logger.debug(f"ThumbnailWorker per '{normalized_item_path}' avviato.")
                    else:
                        logger.error("ImageLoader o ThreadPool non disponibili in LeftPanel per avviare ThumbnailWorker.")
                    return  # Trovato e aggiornamento avviato, esci dal loop e dalla funzione
            
            logger.warning(f"Nessun item corrispondente trovato in photo_list_widget per aggiornare il thumbnail di: {normalized_target_path}")

        except Exception as e:
            logger.error(f"Errore in update_thumbnail_for_path per '{image_path}': {e}", exc_info=True)

    # 🔧 NUOVO: Metodo per caricare ultima cartella
    def _load_last_folder_if_enabled(self):
        """Carica l'ultima cartella aperta se abilitato nelle impostazioni."""
        try:
            # Verifica se il parent (MainWindow) ha le impostazioni
            parent_window = self.parent()
            while parent_window and not hasattr(parent_window, 'settings'):
                parent_window = parent_window.parent()

            if parent_window and hasattr(parent_window, 'settings'):
                settings = getattr(parent_window, 'settings', None)

                # Verifica se il caricamento automatico è abilitato
                if settings and settings.get('folders.remember_last_folder', True):
                    last_folder = settings.get('folders.last_opened_folder')

                    if last_folder and os.path.exists(last_folder):
                        logger.info(f"Caricamento ultima cartella: {last_folder}")

                        # Imposta la cartella corrente
                        self.current_folder = last_folder
                        self.folder_label.setText(last_folder)

                        # Emetti il segnale di cambio cartella
                        self.folder_changed.emit(last_folder)

                        # Carica le immagini
                        self._load_images_async(last_folder)

                        # Aggiorna il footer
                        self._update_footer()

                    else:
                        logger.debug("Nessuna ultima cartella valida trovata")
                else:
                    logger.debug("Caricamento ultima cartella disabilitato")
            else:
                logger.debug("Impostazioni non disponibili nel parent window")

        except Exception as e:
            logger.error(f"Errore nel caricamento dell'ultima cartella: {e}", exc_info=True)

    def _update_image_display(self, index):
        """Aggiorna la visualizzazione dell'immagine (placeholder per compatibilità)"""
        _ = index  # Parametro non utilizzato

    def show_prev_image(self):
        """
        Navigazione: richiesta immagine precedente.
        """
        logger.info("[DEBUG-NAV] Chiamata show_prev_image")
        current_index = -1
        selected_items = self.photo_list_widget.selectedItems()
        if selected_items:
            current_index = self.photo_list_widget.row(selected_items[0])
            
        if current_index == -1 and self.photo_list_widget.count() > 0: # Se nulla è selezionato, seleziona il primo
            current_index = 0 
        elif current_index == -1:
            logger.info("[DEBUG-NAV] Nessuna selezione attiva e lista vuota in show_prev_image")
            return
            
        if self.photo_list_widget.count() == 0:
            logger.info("[DEBUG-NAV] Lista immagini vuota in show_prev_image")
            return

        new_index = (current_index - 1 + self.photo_list_widget.count()) % self.photo_list_widget.count()
            
        new_item = self.photo_list_widget.item(new_index)
        if new_item:
            self.photo_list_widget.setCurrentItem(new_item)
            self.photo_list_widget.scrollToItem(new_item, QAbstractItemView.ScrollHint.PositionAtCenter)
            user_data = new_item.data(Qt.ItemDataRole.UserRole)

            # Estrai il percorso dal dizionario UserRole
            if isinstance(user_data, dict):
                path = user_data.get('image_path')
            else:
                path = user_data  # Fallback per compatibilità

            logger.info(f"[DEBUG-NAV] show_prev_image: new_index={new_index}, path={path}")
            if path:
                self.selection_changed.emit(path) # Emesso qui
                # self.image_selected.emit(path) # Rimosso perché image_selected è gestito da MainWindow basandosi su selection_changed
                logger.info(f"Mostrata immagine precedente: {os.path.basename(path)}")

    def show_next_image(self):
        """
        Navigazione: richiesta immagine successiva.
        """
        logger.info("[DEBUG-NAV] Chiamata show_next_image")
        current_index = -1
        selected_items = self.photo_list_widget.selectedItems()
        if selected_items:
            current_index = self.photo_list_widget.row(selected_items[0])

        if current_index == -1 and self.photo_list_widget.count() > 0: # Se nulla è selezionato, seleziona l'ultimo come punto di partenza per "next" che andrà al primo
            current_index = self.photo_list_widget.count() -1
        elif current_index == -1:
            logger.info("[DEBUG-NAV] Nessuna selezione attiva e lista vuota in show_next_image")
            return

        if self.photo_list_widget.count() == 0:
            logger.info("[DEBUG-NAV] Lista immagini vuota in show_next_image")
            return
            
        new_index = (current_index + 1) % self.photo_list_widget.count()
            
        new_item = self.photo_list_widget.item(new_index)
        if new_item:
            self.photo_list_widget.setCurrentItem(new_item)
            self.photo_list_widget.scrollToItem(new_item, QAbstractItemView.ScrollHint.PositionAtCenter)
            user_data = new_item.data(Qt.ItemDataRole.UserRole)

            # Estrai il percorso dal dizionario UserRole
            if isinstance(user_data, dict):
                path = user_data.get('image_path')
            else:
                path = user_data  # Fallback per compatibilità

            logger.info(f"[DEBUG-NAV] show_next_image: new_index={new_index}, path={path}")
            if path:
                self.selection_changed.emit(path) # Emesso qui
                # self.image_selected.emit(path) # Rimosso perché image_selected è gestito da MainWindow basandosi su selection_changed
                logger.info(f"Mostrata immagine successiva: {os.path.basename(path)}")

    def clear_selection(self):
        pass

    def get_current_thumbnail_size(self) -> QSize:
        """Restituisce la dimensione corrente delle miniature basata sullo slider."""
        size = self.thumbnail_slider.value()
        return QSize(size, size)

    def _on_current_item_changed(self, current: QListWidgetItem, previous: QListWidgetItem) -> None:
        logger.debug(f"[LP._on_current_item_changed] Chiamato con current: {current.text() if current else 'None'}, previous: {previous.text() if previous else 'None'}") # DEBUG
        if current:
            user_data = current.data(Qt.ItemDataRole.UserRole)
            if user_data and 'image_path' in user_data:
                image_path = user_data['image_path']
                logger.debug(f"[LP._on_current_item_changed] Emitting selection_changed with path: {image_path}")
                self.selection_changed.emit(image_path)
            else:
                logger.warning("[LP._on_current_item_changed] Elemento corrente selezionato ma senza user_data o image_path validi.")
                self.selection_changed.emit("") # O None, a seconda di come lo gestisce il main_window
        else:
            logger.debug("[LP._on_current_item_changed] Nessun elemento corrente selezionato.")
            # self.image_selected.emit(None, None) # Potrebbe essere necessario se si deseleziona tutto
            self.selection_changed.emit("") # O None

    def update_items_after_rename(self, renamed_files_info: list):
        """
        Aggiorna gli item nella photo_list_widget dopo che i file sono stati rinominati.

        Args:
            renamed_files_info (list): Lista di dizionari, ognuno con 
                                       {'old_path': str, 'new_path': str, 'new_name': str}.
        """
        logger.info(f"[LeftPanel.update_items_after_rename] Ricevuti {len(renamed_files_info)} items da aggiornare.")
        if not self.photo_list_widget:
            logger.error("[LeftPanel.update_items_after_rename] photo_list_widget non è inizializzato.")
            return

        items_updated_count = 0
        for i in range(self.photo_list_widget.count()):
            item = self.photo_list_widget.item(i)
            if not item:
                logger.warning(f"[LeftPanel.update_items_after_rename] Item all'indice {i} è None.")
                continue

            item_data = item.data(Qt.ItemDataRole.UserRole)
            if not isinstance(item_data, dict) or 'image_path' not in item_data:
                logger.warning(f"[LeftPanel.update_items_after_rename] Item {i} (Testo: {item.text()}) non ha dati validi (UserRole) o manca 'image_path'. Dati: {item_data}")
                continue
            
            current_item_path = item_data['image_path']

            for renamed_info in renamed_files_info:
                if current_item_path == renamed_info['old_path']:
                    logger.debug(f"[LeftPanel.update_items_after_rename] Aggiornamento item: {current_item_path} -> {renamed_info['new_path']}")
                    
                    # Aggiorna il testo visualizzato dell'item
                    item.setText(renamed_info['new_name'])
                    
                    # Aggiorna i dati memorizzati nell'item (Qt.UserRole)
                    item_data['image_path'] = renamed_info['new_path']
                    item_data['name'] = renamed_info['new_name']
                    item.setData(Qt.ItemDataRole.UserRole, item_data)
                    
                    items_updated_count += 1
                    # TODO: Considerare se l'icona (miniatura) necessita di essere ricaricata/aggiornata qui.
                    # Se la miniatura è un QPixmap già caricato e l'immagine sottostante è solo rinominata,
                    # la miniatura stessa potrebbe non cambiare. Ma se il QIcon dipende dal path per un caricamento futuro,
                    # l'aggiornamento di 'image_path' in UserRole è cruciale.
                    break # Trovato il file rinominato corrispondente, passa al prossimo item del QListWidget
        
        if items_updated_count > 0:
            logger.info(f"[LeftPanel.update_items_after_rename] {items_updated_count} items aggiornati nel QListWidget.")
            self.photo_list_widget.viewport().update() # Forza un repaint della viewport per riflettere i cambiamenti
        else:
            logger.info("[LeftPanel.update_items_after_rename] Nessun item corrispondente trovato nel QListWidget per l'aggiornamento.")
