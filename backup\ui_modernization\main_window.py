#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Implementazione della finestra principale dell'applicazione.
Coordina i vari pannelli e componenti.
"""

import logging
import os
from typing import Optional

from PySide6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QSplitter,
    QMessageBox, QStatusBar, QLabel, QHBoxLayout, QPushButton,
    QMenu, QInputDialog, QDialog  # Aggiunti QMenu, QInputDialog e QDialog
)
from PySide6.QtCore import Qt, QSize
from PySide6.QtGui import QIcon, QCloseEvent, QShortcut, QKeySequence, QAction  # Aggiunto QAction

from ui.left_panel import LeftPanel
from ui.views.panels.right_panel import RightPanel
from ui.right_sidebar import RightSidebar
from ui.dialogs.settings_dialog import SettingsDialog
from ui.styles import Styles
from core.image_cache import ImageCache
from controllers.main_controller import MainController
from models.settings_model import SettingsModel
from utils.logging_config import get_logger

logger = get_logger("PhotoDrop.MainWindow")

class PhotoDropApp(QMainWindow):
    """
    Finestra principale dell'applicazione PhotoDrop.
    Coordina tutti i componenti dell'interfaccia utente.
    """
    
    def __init__(self):
        """Inizializza la finestra principale."""
        print("[DEBUG] main_window.py: Inizio __init__ di PhotoDropApp") # DEBUG PRINT
        super().__init__()
        print("[DEBUG] main_window.py: super().__init__() chiamato") # DEBUG PRINT
        
        # Configurazione finestra principale
        self.setWindowTitle("PhotoDrop - Gestione Foto")
        print("[DEBUG] main_window.py: setWindowTitle chiamato") # DEBUG PRINT
        # NON chiamare showMaximized() qui
        self.setMinimumSize(800, 600)
        print("[DEBUG] main_window.py: setMinimumSize chiamato") # DEBUG PRINT
        
        # 🔧 NUOVO: Inizializza il sistema di impostazioni
        self.settings = SettingsModel()
        print("[DEBUG] main_window.py: SettingsModel istanziato") # DEBUG PRINT

        # Stato dell'applicazione
        self.original_size = QSize(1000, 700)
        self.current_folder = None
        print("[DEBUG] main_window.py: Stato applicazione inizializzato") # DEBUG PRINT

        # Inizializza i servizi con impostazioni personalizzate
        cache_dir = "cache/images" if self.settings.get('cache.enable_disk_cache', True) else None
        memory_size = self.settings.get('cache.max_memory_items', 1000)
        disk_size_mb = self.settings.get('cache.max_disk_cache_size_mb', 500)
        max_age_days = self.settings.get('cache.max_disk_cache_file_age_days', 30)

        self.image_cache = ImageCache(
            cache_dir=cache_dir,
            memory_size=memory_size,
            disk_size_mb=disk_size_mb,
            max_age_days=max_age_days
        )
        print("[DEBUG] main_window.py: ImageCache istanziato con impostazioni personalizzate") # DEBUG PRINT
        
        # Controller MVC
        self.controller = MainController(self)
        print("[DEBUG] main_window.py: MainController istanziato") # DEBUG PRINT
        
        # Inizializza l'interfaccia
        self._setup_ui()
        print("[DEBUG] main_window.py: _setup_ui() chiamato") # DEBUG PRINT
        self._connect_signals()
        print("[DEBUG] main_window.py: _connect_signals() chiamato") # DEBUG PRINT
        
        # Imposta lo stile globale
        self.setStyleSheet(Styles.MAIN_STYLE) # DEBUG: Applica solo lo stile di base
        print("[DEBUG] main_window.py: setStyleSheet chiamato con Styles.MAIN_STYLE") # DEBUG PRINT
        
        # Footer
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        print("[DEBUG] main_window.py: StatusBar impostata") # DEBUG PRINT
        
        # Etichetta per il percorso corrente (a sinistra)
        self.path_status_label = QLabel()
        self.path_status_label.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)
        self.status_bar.addWidget(self.path_status_label, 1)
        
        # Etichetta per il conteggio delle immagini (al centro)
        self.count_status_label = QLabel()
        self.count_status_label.setAlignment(Qt.AlignCenter)
        self.status_bar.addWidget(self.count_status_label, 1)
        
        # Etichetta per informazioni aggiuntive (a destra)
        self.info_status_label = QLabel()
        self.info_status_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        self.status_bar.addWidget(self.info_status_label, 1)
        
        self._update_footer()
        # Icona moderna per la finestra principale
        self.setWindowIcon(QIcon("assets/icons/folder_duotone.svg"))
        
        # Shortcut per schermo intero/uscita
        self.fullscreen_shortcut = QShortcut(QKeySequence("F11"), self)
        self.fullscreen_shortcut.activated.connect(self._toggle_fullscreen_shortcut)
        
        # Shortcut per la guida
        self.help_shortcut = QShortcut(QKeySequence("F1"), self)
        self.help_shortcut.activated.connect(self._show_help_dialog)
        
        self.is_fullscreen = False
        
        # 🔧 NUOVO: Carica geometria finestra dalle impostazioni
        self._load_window_geometry()

        logger.info("Applicazione inizializzata")
        print("[DEBUG] main_window.py: Fine __init__ di PhotoDropApp") # DEBUG PRINT
    
    def _setup_ui(self):
        """Configura l'interfaccia utente."""
        # Widget principale
        main_widget = QWidget()
        self.setCentralWidget(main_widget)
        main_layout = QVBoxLayout(main_widget)
        main_layout.setContentsMargins(2, 2, 2, 2)  # Margini minimi per l'interfaccia

        # Header Layout
        header_layout = QHBoxLayout()
        header_layout.setContentsMargins(0, 0, 0, 2)  # Margini ridotti
        header_layout.setSpacing(2)  # Riduci lo spazio tra i pulsanti

        # Aggiungi uno stretch per spingere i pulsanti a destra
        header_layout.addStretch()
        
        # Pulsante Schermo Intero
        self.fullscreen_button = QPushButton()
        self.fullscreen_button.setIcon(QIcon("assets/icons/fullscreen_duotone.svg"))
        self.fullscreen_button.setFixedSize(32, 32)
        self.fullscreen_button.setIconSize(QSize(20, 20))
        self.fullscreen_button.setToolTip("Schermo intero (F11)")
        self.fullscreen_button.setStyleSheet(Styles.ICON_BUTTON_STYLE)
        self.fullscreen_button.clicked.connect(self._toggle_fullscreen_shortcut)
        header_layout.addWidget(self.fullscreen_button)
        
        # Pulsante Guida
        self.help_button = QPushButton()
        self.help_button.setIcon(QIcon("assets/icons/help_duotone.svg"))
        self.help_button.setFixedSize(32, 32)
        self.help_button.setIconSize(QSize(20, 20))
        self.help_button.setToolTip("Guida (F1)")
        self.help_button.setStyleSheet(Styles.ICON_BUTTON_STYLE)
        self.help_button.clicked.connect(self._show_help_dialog)
        header_layout.addWidget(self.help_button)
        
        # Pulsante Opzioni
        self.options_button = QPushButton()
        self.options_button.setText("⚙️")  # Testo placeholder per l'icona Opzioni
        self.options_button.setFixedSize(32, 32)
        self.options_button.setToolTip("Opzioni Cache")
        self.options_button.setStyleSheet(Styles.ICON_BUTTON_STYLE)
        self.options_button.clicked.connect(self._show_options_menu)
        header_layout.addWidget(self.options_button)
        self._setup_options_menu()

        # Pulsante exit
        self.close_button = QPushButton()
        self.close_button.setIcon(QIcon("assets/icons/close_modern.svg"))
        self.close_button.setFixedSize(32, 32)
        self.close_button.setToolTip("Chiudi applicazione (Alt+F4)")
        self.close_button.setStyleSheet(Styles.CLOSE_BUTTON_STYLE)
        self.close_button.clicked.connect(self.close)
        header_layout.addWidget(self.close_button)

        main_layout.addLayout(header_layout)  # Aggiunge l'header in cima

        # Crea splitter per pannelli sinistro/destro
        self.splitter = QSplitter(Qt.Horizontal)
        self.splitter.setHandleWidth(1)  # Riduci lo spessore della maniglia
        main_layout.addWidget(self.splitter, 1)  # Diamo peso 1 allo splitter per farlo espandere
        
        # Imposta stile per i pannelli
        self.setStyleSheet("""
            QSplitter::handle {
                background-color: #2d2d2d;
                width: 1px;
                height: 1px;
            }
        """)
        
        # Pannello sinistro
        self.left_panel = LeftPanel(self)
        self.splitter.addWidget(self.left_panel)
        
        # Pannello destro con splitter orizzontale per anteprima e sidebar
        right_splitter = QSplitter(Qt.Horizontal)
        
        # Inizializza il modello di dati per le immagini
        from models.image_model import ImageModel
        self.image_model = ImageModel(self.image_cache) # Passa l'istanza di ImageCache
        
        # Pannello di anteprima con cache delle immagini
        self.right_panel = RightPanel(
            image_model=self.image_model,
            image_cache=self.image_cache,
            parent=self
        )
        
        # Sidebar per i metadati EXIF
        self.right_sidebar = RightSidebar(self)
        
        # Imposta la sidebar anche nel pannello destro per la comunicazione
        self.right_panel.right_sidebar = self.right_sidebar
        
        # Aggiungi i pannelli allo splitter destro
        right_splitter.addWidget(self.right_panel)
        right_splitter.addWidget(self.right_sidebar)
        
        # Aggiungi lo splitter destro allo splitter principale
        self.splitter.addWidget(right_splitter)
        
        # 🔧 NUOVO: Carica dimensioni splitter dalle impostazioni
        splitter_sizes = self.settings.get('ui.splitter_sizes', [300, 700])
        right_splitter_sizes = self.settings.get('ui.right_splitter_sizes', [600, 200])

        self.splitter.setSizes(splitter_sizes)
        right_splitter.setSizes(right_splitter_sizes)

        # Salva le dimensioni quando cambiano
        self.splitter.splitterMoved.connect(self._on_splitter_moved)
        right_splitter.splitterMoved.connect(self._on_right_splitter_moved)
    
    def _connect_signals(self):
        """Connette i segnali agli slot."""
        # Connetti i segnali dal pannello sinistro
        if self.left_panel and hasattr(self.left_panel, 'selection_changed'):
            self.left_panel.selection_changed.connect(self._on_selection_changed)
            logger.info("[MW._connect_signals] Connesso left_panel.selection_changed a _on_selection_changed")
        else:
            logger.error("[MW._connect_signals] Impossibile connettere left_panel.selection_changed: left_panel o segnale non trovato.")
        
        self.left_panel.folder_changed.connect(self._on_folder_changed)
        self.left_panel.images_loaded.connect(self._on_images_loaded)
        
        # Connetti i segnali del pannello destro
        self.right_panel.fullscreen_toggled.connect(self._toggle_fullscreen)
        self.right_panel.zoom_changed.connect(self._on_zoom_changed)
        self.right_panel.rotation_changed.connect(self._on_rotation_changed)
        self.right_panel.image_loaded.connect(self._on_image_loaded)
        self.right_panel.error_occurred.connect(self._on_error_occurred)
        self.right_panel.image_updated.connect(self.left_panel.update_thumbnail_for_path) # NUOVA CONNESSIONE

        # Connessioni per la navigazione richiesta dal RightPanel
        if hasattr(self.right_panel, 'request_prev_image') and hasattr(self.left_panel, 'show_prev_image'):
            self.right_panel.request_prev_image.connect(self.left_panel.show_prev_image)
        if hasattr(self.right_panel, 'request_next_image') and hasattr(self.left_panel, 'show_next_image'):
            self.right_panel.request_next_image.connect(self.left_panel.show_next_image)

        # Connetti i segnali della sidebar
        if hasattr(self, 'right_sidebar') and self.right_sidebar:
            # Connessioni aggiuntive per la sidebar, se necessario
            pass
    
    def _on_folder_changed(self, folder_path: str):
        """
        Gestisce il cambio della cartella corrente.
        
        Args:
            folder_path (str): Percorso della nuova cartella
        """
        self.controller.on_folder_changed(folder_path)

    def _on_images_loaded(self, image_paths: list):
        """Gestisce il caricamento di una nuova lista di immagini."""
        if self.controller:
            self.controller.on_images_loaded(image_paths)

    def _on_selection_changed(self, image_path: str):
        """
        Gestisce il cambio dell'immagine selezionata.
        
        Args:
            image_path (str): Percorso dell'immagine selezionata
        """
        # self.controller.on_selection_changed(image_path) # Rimosso/Commentato
        if hasattr(self, 'right_panel') and self.right_panel and image_path:
            self.right_panel.show_image(image_path)
            logger.info(f"MainWindow: _on_selection_changed ha chiamato right_panel.show_image con {os.path.basename(image_path) if image_path else 'None'}")
        elif not image_path:
            logger.warning("MainWindow: _on_selection_changed ha ricevuto un image_path vuoto.")
        else:
            logger.warning("MainWindow: _on_selection_changed chiamata ma right_panel non disponibile o image_path vuoto.")
    
    def _on_zoom_changed(self, zoom_factor: float):
        """
        Gestisce il cambio dello zoom dell'immagine.
        
        Args:
            zoom_factor (float): Fattore di zoom corrente
        """
        self.controller.on_zoom_changed(zoom_factor)
    
    def _on_rotation_changed(self, angle: int):
        """
        Gestisce la rotazione dell'immagine.
        
        Args:
            angle (int): Angolo di rotazione in gradi
        """
        self.controller.on_rotation_changed(angle)
    
    def _on_image_loaded(self, image_path: str):
        """
        Gestisce il caricamento di una nuova immagine.
        
        Args:
            image_path (str): Percorso dell'immagine caricata
        """
        # Aggiorna eventuali controlli UI che mostrano il percorso dell'immagine
        if hasattr(self, 'path_status_label'):
            self.path_status_label.setText(f"Immagine: {os.path.basename(image_path)}")
    
    def _on_error_occurred(self, title: str, message: str):
        """
        Gestisce gli errori segnalati dal pannello destro.
        
        Args:
            title (str): Titolo dell'errore
            message (str): Messaggio di errore
        """
        self.controller.on_error_occurred(title, message)
    
    def browse_previous_image(self):
        """Naviga all'immagine precedente nella lista del pannello sinistro."""
        if not self.left_panel or not hasattr(self.left_panel, 'image_files') or not self.left_panel.image_files:
            logger.debug("PhotoDropApp: Nessun pannello sinistro o nessuna immagine caricata per navigare indietro.")
            return

        if not hasattr(self.left_panel, 'photo_list_widget') or not self.left_panel.photo_list_widget:
            logger.error("PhotoDropApp: photo_list_widget non trovato in left_panel.")
            return

        num_images = self.left_panel.photo_list_widget.count()
        if num_images == 0:
            logger.debug("PhotoDropApp: Nessuna immagine nella lista per navigare.")
            return

        current_row = self.left_panel.photo_list_widget.currentRow()
        # Se nessuna riga è selezionata (currentRow == -1), inizia dall'ultima
        if current_row == -1 and num_images > 0:
            prev_row = num_images -1
        else:
            prev_row = (current_row - 1 + num_images) % num_images
        
        self.left_panel.photo_list_widget.setCurrentRow(prev_row)
        logger.debug(f"PhotoDropApp: Navigato all'immagine precedente, riga {prev_row}")

    def browse_next_image(self):
        """Naviga all'immagine successiva nella lista del pannello sinistro."""
        if not self.left_panel or not hasattr(self.left_panel, 'image_files') or not self.left_panel.image_files:
            logger.debug("PhotoDropApp: Nessun pannello sinistro o nessuna immagine caricata per navigare avanti.")
            return

        if not hasattr(self.left_panel, 'photo_list_widget') or not self.left_panel.photo_list_widget:
            logger.error("PhotoDropApp: photo_list_widget non trovato in left_panel.")
            return
            
        num_images = self.left_panel.photo_list_widget.count()
        if num_images == 0:
            logger.debug("PhotoDropApp: Nessuna immagine nella lista per navigare.")
            return

        current_row = self.left_panel.photo_list_widget.currentRow()
        # Se nessuna riga è selezionata (currentRow == -1), inizia dalla prima
        if current_row == -1 and num_images > 0:
            next_row = 0
        else:
            next_row = (current_row + 1) % num_images
        
        self.left_panel.photo_list_widget.setCurrentRow(next_row)
        logger.debug(f"PhotoDropApp: Navigato all'immagine successiva, riga {next_row}")

    def _toggle_fullscreen(self, fullscreen: bool):
        """
        Attiva/disattiva la modalità a schermo intero.
        
        Args:
            fullscreen (bool): True per attivare lo schermo intero
        """
        if fullscreen:
            # Salva le dimensioni normali prima di andare a schermo intero
            self.original_size = self.size()
            self.normal_geometry = self.geometry()  # Salva la geometria attuale
            self.showFullScreen()
            self.is_fullscreen = True
        else:
            self.showNormal()
            if hasattr(self, 'normal_geometry'):
                self.setGeometry(self.normal_geometry)
            self.is_fullscreen = False
    
    def _show_help_dialog(self):
        """Mostra la finestra di dialogo della guida."""
        from ui.dialogs.help_dialog import HelpDialog
        help_dialog = HelpDialog(self)
        help_dialog.exec_()
    
    def _toggle_fullscreen_shortcut(self):
        """Gestisce il passaggio a schermo intero tramite scorciatoia da tastiera."""
        self._toggle_fullscreen(not self.is_fullscreen)
    
    def _setup_options_menu(self):
        """Crea il menu a tendina per le opzioni della cache."""
        self.options_menu = QMenu(self)
        self.options_menu.setStyleSheet(Styles.MAIN_STYLE)  # Applica uno stile base al menu

        # 🔧 NUOVO: Aggiungi opzione Impostazioni
        action_settings = QAction("⚙️ Impostazioni...", self)
        action_settings.triggered.connect(self._show_settings_dialog)
        self.options_menu.addAction(action_settings)

        self.options_menu.addSeparator()  # Separatore

        action_change_disk_size = QAction("Dimensione Cache Disco...", self)
        action_change_disk_size.triggered.connect(self._change_max_disk_cache_size)
        self.options_menu.addAction(action_change_disk_size)

        action_change_disk_age = QAction("Età File Cache Disco...", self)
        action_change_disk_age.triggered.connect(self._change_max_disk_cache_age)
        self.options_menu.addAction(action_change_disk_age)

        self.options_menu.addSeparator()  # Aggiunge un separatore

        action_clear_cache_now = QAction("Svuota Cache Ora", self)
        action_clear_cache_now.triggered.connect(self._clear_disk_cache_now)
        self.options_menu.addAction(action_clear_cache_now)

    def _show_options_menu(self):
        """Mostra il menu opzioni sotto il pulsante opzioni."""
        button_global_pos = self.options_button.mapToGlobal(self.options_button.rect().bottomLeft())
        self.options_menu.exec(button_global_pos)

    def _change_max_disk_cache_size(self):
        """Modifica la dimensione massima della cache su disco."""
        current_value = self.image_cache.max_disk_cache_size_mb
        new_size, ok = QInputDialog.getInt(self, "Dimensione Cache Disco", 
                                           f"Dimensione massima cache su disco (MB):\nAttuale: {current_value} MB", 
                                           current_value, 10, 2048, 10)
        if ok and new_size:
            confirm = QMessageBox.question(self, "Conferma Modifica", f"Vuoi davvero impostare la dimensione massima della cache su disco a {new_size} MB?", QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
            if confirm == QMessageBox.Yes:
                self.image_cache.max_disk_cache_size_mb = new_size
                QMessageBox.information(self, "Opzione Cache", f"Dimensione cache impostata a {new_size} MB.")

    def _change_max_disk_cache_age(self):
        """Modifica l'età massima dei file nella cache su disco."""
        current_value = self.image_cache.max_disk_cache_file_age_days
        new_age, ok = QInputDialog.getInt(self, "Età File Cache Disco", 
                                          f"Età massima file cache (giorni):\nAttuale: {current_value} giorni", 
                                          current_value, 1, 365, 1)
        if ok and new_age:
            confirm = QMessageBox.question(self, "Conferma Modifica", f"Vuoi davvero impostare l'età massima dei file cache su disco a {new_age} giorni?", QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
            if confirm == QMessageBox.Yes:
                self.image_cache.max_disk_cache_file_age_days = new_age
                QMessageBox.information(self, "Opzione Cache", f"Età massima file cache impostata a {new_age} giorni.")

    def _clear_disk_cache_now(self):
        """Svuota immediatamente la cache su disco."""
        reply = QMessageBox.question(self, "Svuota Cache", 
                                     "Sei sicuro di voler svuotare tutta la cache su disco ora?\nQuesta operazione non può essere annullata.",
                                     QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
        
        if reply == QMessageBox.Yes:
            if hasattr(self, 'image_cache') and hasattr(self.image_cache, 'clear_disk_cache'):
                logger.info("Richiesta di svuotamento manuale della cache su disco.")
                self.image_cache.clear_disk_cache()
                QMessageBox.information(self, "Cache Svuotata", "La cache su disco è stata svuotata.")
            else:
                logger.error("ImageCache o clear_disk_cache non trovati per lo svuotamento manuale.")
                QMessageBox.warning(self, "Errore", "Impossibile svuotare la cache in questo momento.")

    def _update_footer(self):
        """Aggiorna le informazioni nel footer dell'applicazione."""
        # Aggiorna il percorso corrente
        if hasattr(self, 'left_panel') and hasattr(self.left_panel, 'current_folder') and self.left_panel.current_folder:
            self.path_status_label.setText(f"Cartella: {self.left_panel.current_folder}")
        else:
            self.path_status_label.setText("Nessuna cartella selezionata")
            
        # Aggiorna il conteggio delle immagini
        if hasattr(self, 'left_panel') and hasattr(self.left_panel, 'photo_list_widget'):
            count = self.left_panel.photo_list_widget.count()
            if count > 0:
                self.count_status_label.setText(f"{count} immagini")
            else:
                self.count_status_label.setText("Nessuna immagine")
        else:
            self.count_status_label.setText("")
            
        # Aggiorna le informazioni aggiuntive (copyright e data)
        self.info_status_label.setText("PhotoDrop | 2025 Archiwave.tech")
    
    def keyPressEvent(self, event: QKeySequence):
        """
        Gestisce gli eventi della tastiera a livello di finestra principale.
        
        Args:
            event (QKeySequence): Evento di tastiera
        """
        # Gestisci i tasti freccia per la navigazione tra le immagini
        if event.key() == Qt.Key_Left:
            # Freccia sinistra: immagine precedente
            if hasattr(self, 'left_panel') and self.left_panel:
                self.left_panel.show_prev_image()
                return
        elif event.key() == Qt.Key_Right:
            # Freccia destra: immagine successiva
            if hasattr(self, 'left_panel') and self.left_panel:
                self.left_panel.show_next_image()
                return
            
        # Passa l'evento al gestore predefinito
        super().keyPressEvent(event)

    # 🔧 NUOVO: Metodi per gestione impostazioni
    def _load_window_geometry(self):
        """Carica la geometria della finestra dalle impostazioni."""
        try:
            # Carica stato massimizzato
            if self.settings.get('ui.window_maximized', True):
                self.showMaximized()
            else:
                # Carica geometria specifica se disponibile
                geometry = self.settings.get('ui.window_geometry')
                if geometry:
                    self.setGeometry(geometry[0], geometry[1], geometry[2], geometry[3])
                else:
                    # Usa dimensioni predefinite
                    self.resize(1000, 700)

        except Exception as e:
            logger.error(f"Errore nel caricamento della geometria finestra: {e}")
            self.resize(1000, 700)

    def _save_window_geometry(self):
        """Salva la geometria della finestra nelle impostazioni."""
        try:
            # Salva stato massimizzato
            self.settings.set('ui.window_maximized', self.isMaximized())

            # Salva geometria se non massimizzata
            if not self.isMaximized():
                geometry = self.geometry()
                self.settings.set('ui.window_geometry', [
                    geometry.x(), geometry.y(),
                    geometry.width(), geometry.height()
                ])

        except Exception as e:
            logger.error(f"Errore nel salvataggio della geometria finestra: {e}")

    def _on_splitter_moved(self, pos, index):
        """Gestisce il movimento dello splitter principale."""
        try:
            sizes = self.splitter.sizes()
            self.settings.set('ui.splitter_sizes', sizes)
            logger.debug(f"Splitter principale spostato: {sizes}")
        except Exception as e:
            logger.error(f"Errore nel salvataggio dimensioni splitter: {e}")

    def _on_right_splitter_moved(self, pos, index):
        """Gestisce il movimento dello splitter destro."""
        try:
            # Trova lo splitter destro
            right_splitter = None
            for i in range(self.splitter.count()):
                widget = self.splitter.widget(i)
                if isinstance(widget, QSplitter):
                    right_splitter = widget
                    break

            if right_splitter:
                sizes = right_splitter.sizes()
                self.settings.set('ui.right_splitter_sizes', sizes)
                logger.debug(f"Splitter destro spostato: {sizes}")
        except Exception as e:
            logger.error(f"Errore nel salvataggio dimensioni splitter destro: {e}")

    def closeEvent(self, event):
        """Gestisce la chiusura dell'applicazione."""
        try:
            # Salva impostazioni finestra
            self._save_window_geometry()

            # Salva ultima cartella aperta se abilitato
            if self.settings.get('folders.remember_last_folder', True) and self.current_folder:
                self.settings.set('folders.last_opened_folder', self.current_folder)

            # Salva impostazioni finali
            self.settings.save_settings()

            logger.info("Impostazioni salvate alla chiusura")

        except Exception as e:
            logger.error(f"Errore nel salvataggio impostazioni alla chiusura: {e}")

        # Chiama il gestore predefinito
        super().closeEvent(event)

    # 🔧 NUOVO: Metodo per mostrare dialog impostazioni
    def _show_settings_dialog(self):
        """Mostra il dialog delle impostazioni."""
        try:
            dialog = SettingsDialog(self.settings, self)
            dialog.settings_changed.connect(self._on_settings_changed)

            if dialog.exec() == QDialog.Accepted:
                logger.info("Impostazioni modificate dall'utente")

        except Exception as e:
            logger.error(f"Errore nell'apertura del dialog impostazioni: {e}")
            QMessageBox.critical(self, "Errore", f"Impossibile aprire le impostazioni:\n{str(e)}")

    def _on_settings_changed(self):
        """Gestisce i cambiamenti nelle impostazioni."""
        try:
            # Ricarica le impostazioni della cache se necessario
            cache_settings = {
                'max_memory_items': self.settings.get('cache.max_memory_items', 1000),
                'max_disk_cache_size_mb': self.settings.get('cache.max_disk_cache_size_mb', 500),
                'max_disk_cache_file_age_days': self.settings.get('cache.max_disk_cache_file_age_days', 30)
            }

            # Aggiorna le impostazioni della cache esistente
            if hasattr(self.image_cache, 'update_settings'):
                self.image_cache.update_settings(cache_settings)

            # Aggiorna dimensione miniature se cambiata
            thumbnail_size = self.settings.get('ui.thumbnail_size', 128)
            if hasattr(self.left_panel, 'update_thumbnail_size'):
                self.left_panel.update_thumbnail_size(thumbnail_size)

            logger.info("Impostazioni applicate all'applicazione")

        except Exception as e:
            logger.error(f"Errore nell'applicazione delle nuove impostazioni: {e}")

    def closeEvent(self, event: QCloseEvent):
        """
        Gestisce l'evento di chiusura della finestra.
        
        Args:
            event (QCloseEvent): Evento di chiusura
        """
        # Pulisci le cache prima di uscire
        if hasattr(self, 'image_cache'):
            self.image_cache.clear()  # Il metodo si chiama 'clear', non 'clear_all'
        
        # Accetta l'evento di chiusura
        event.accept()
        
        logger.info("Applicazione terminata")
