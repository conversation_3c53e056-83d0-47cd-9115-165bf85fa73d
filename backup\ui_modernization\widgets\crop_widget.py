#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Widget per la selezione dell'area di ritaglio delle immagini.
ui/widgets/crop_widget.py
"""

import os
from typing import Optional, Tuple
from PySide6.QtWidgets import (
    QWidget, QLabel, QVBoxLayout, QHBoxLayout, QPushButton, 
    QFrame, QSizePolicy, QApplication
)
from PySide6.QtCore import Qt, QRect, QPoint, Signal, QSize, QRectF
from PySide6.QtGui import (
    QPixmap, QPainter, QPen, QColor, QMouseEvent, QCursor, 
    QBrush, QFont, QFontMetrics, QPainterPath
)

from ui.styles import Styles
from utils.logging_config import get_logger

logger = get_logger("PhotoDrop.CropWidget")

class CropSelectionLabel(QLabel):
    """
    Label personalizzata per la selezione dell'area di ritaglio.
    Permette di disegnare e ridimensionare un rettangolo di selezione.
    """
    
    # Segnali
    selection_changed = Signal(QRect)  # Emesso quando cambia la selezione
    selection_finished = Signal(QRect)  # Emesso quando la selezione è completata
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # Stato della selezione
        self.selection_rect = QRect()
        self.is_selecting = False
        self.is_resizing = False
        self.resize_handle = None
        self.start_point = QPoint()
        self.last_point = QPoint()
        self.original_pixmap_size = QSize() # Aggiunto per memorizzare le dimensioni originali
        
        # Dimensioni handle di ridimensionamento
        self.handle_size = 8
        
        # Configurazione widget
        self.setMinimumSize(400, 300)
        self.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.setStyleSheet("border: 1px solid #444; background-color: #1a1a1a;")
        self.setCursor(Qt.CursorShape.CrossCursor)
        
        # Abilita il tracking del mouse
        self.setMouseTracking(True)
        
        logger.info("CropSelectionLabel inizializzato")
    
    def set_image(self, pixmap: QPixmap):
        """Imposta l'immagine da visualizzare e ritagliare."""
        if pixmap and not pixmap.isNull():
            self.original_pixmap_size = pixmap.size() # Memorizza le dimensioni originali
            # Scala il pixmap per adattarlo al widget mantenendo l'aspect ratio
            # self.pixmap() restituirà questo scaled_pixmap
            scaled_pixmap = pixmap.scaled(
                self.size(), 
                Qt.AspectRatioMode.KeepAspectRatio, 
                Qt.TransformationMode.SmoothTransformation
            )
            self.setPixmap(scaled_pixmap)
            
            # Reset della selezione
            self.selection_rect = QRect()
            self.update()
            
            logger.debug(f"Immagine impostata: {scaled_pixmap.size()}")
        else:
            self.clear()
            self.selection_rect = QRect()
    
    def get_selection_rect(self) -> QRect:
        """
        Restituisce il rettangolo di selezione corrente.
        
        Returns:
            QRect: Rettangolo di selezione
        """
        return self.selection_rect
    
    def get_selection_coordinates(self) -> Tuple[int, int, int, int]:
        """
        Restituisce le coordinate di selezione relative all'immagine originale.
        
        Returns:
            Tuple[int, int, int, int]: (x, y, width, height) nell'immagine originale
        """
        if self.selection_rect.isEmpty() or not self.pixmap() or self.original_pixmap_size.isEmpty():
            return (0, 0, 0, 0)
        
        # L'immagine visualizzata nel QLabel (quella scalata)
        displayed_pixmap = self.pixmap()
        widget_rect = self.rect()
        
        # Calcola il rettangolo che l'immagine scalata occupa effettivamente nel widget
        # (potrebbe avere bande laterali/superiori se l'aspect ratio non corrisponde)
        pixmap_display_rect = QRect()
        
        # Determina se l'immagine scalata è limitata dalla larghezza o dall'altezza del widget
        if displayed_pixmap.width() * widget_rect.height() > displayed_pixmap.height() * widget_rect.width():
            # L'immagine è limitata dalla larghezza del widget
            scaled_height = widget_rect.width() * displayed_pixmap.height() // displayed_pixmap.width()
            pixmap_display_rect = QRect(
                0, 
                (widget_rect.height() - scaled_height) // 2,
                widget_rect.width(),
                scaled_height
            )
        else:
            # L'immagine è limitata dall'altezza del widget
            scaled_width = widget_rect.height() * displayed_pixmap.width() // displayed_pixmap.height()
            pixmap_display_rect = QRect(
                (widget_rect.width() - scaled_width) // 2,
                0,
                scaled_width,
                widget_rect.height()
            )
        
        # Calcola le coordinate relative all'immagine visualizzata e i fattori di scala
        if pixmap_display_rect.width() > 0 and pixmap_display_rect.height() > 0:
            # Fattori di scala per convertire dalle coordinate dell'immagine visualizzata
            # alle coordinate dell'immagine originale.
            scale_x = self.original_pixmap_size.width() / pixmap_display_rect.width()
            scale_y = self.original_pixmap_size.height() / pixmap_display_rect.height()
            
            # Coordinate del rettangolo di selezione relative all'angolo in alto a sinistra
            # dell'immagine visualizzata (pixmap_display_rect).
            # Assicurati che rel_x e rel_y non siano negativi se la selezione inizia fuori dall'immagine.
            rel_x = max(0, self.selection_rect.x() - pixmap_display_rect.x())
            rel_y = max(0, self.selection_rect.y() - pixmap_display_rect.y())
            
            # Converte le coordinate e le dimensioni della selezione in coordinate dell'immagine originale
            orig_x = int(rel_x * scale_x)
            orig_y = int(rel_y * scale_y)
            orig_width = int(self.selection_rect.width() * scale_x)
            orig_height = int(self.selection_rect.height() * scale_y)
            
            # Assicurati che le coordinate e le dimensioni siano nei limiti dell'immagine originale
            orig_x = max(0, min(orig_x, self.original_pixmap_size.width() - 1))
            orig_y = max(0, min(orig_y, self.original_pixmap_size.height() - 1))
            orig_width = min(orig_width, self.original_pixmap_size.width() - orig_x)
            orig_height = min(orig_height, self.original_pixmap_size.height() - orig_y)
            
            return (orig_x, orig_y, orig_width, orig_height)
        
        return (0, 0, 0, 0)
    
    def clear_selection(self):
        """Pulisce la selezione corrente."""
        self.selection_rect = QRect()
        self.update()
        logger.debug("Selezione pulita")
    
    def mousePressEvent(self, event: QMouseEvent):
        """Gestisce l'evento di pressione del mouse."""
        if event.button() == Qt.MouseButton.LeftButton:
            self.start_point = event.pos()
            self.last_point = event.pos()
            
            # Controlla se stiamo cliccando su un handle di ridimensionamento
            handle = self._get_resize_handle(event.pos())
            if handle and not self.selection_rect.isEmpty():
                self.is_resizing = True
                self.resize_handle = handle
                self.setCursor(self._get_resize_cursor(handle))
            else:
                # Inizia una nuova selezione
                self.is_selecting = True
                self.selection_rect = QRect(self.start_point, QSize(0, 0))
                self.setCursor(Qt.CursorShape.CrossCursor)
            
            self.update()
    
    def mouseMoveEvent(self, event: QMouseEvent):
        """Gestisce l'evento di movimento del mouse."""
        if self.is_selecting:
            # Aggiorna il rettangolo di selezione
            self.selection_rect = QRect(self.start_point, event.pos()).normalized()
            self.update()
            self.selection_changed.emit(self.selection_rect)
            
        elif self.is_resizing and self.resize_handle:
            # Ridimensiona la selezione
            self._resize_selection(event.pos())
            self.update()
            self.selection_changed.emit(self.selection_rect)
            
        else:
            # Aggiorna il cursore in base alla posizione
            handle = self._get_resize_handle(event.pos())
            if handle and not self.selection_rect.isEmpty():
                self.setCursor(self._get_resize_cursor(handle))
            else:
                self.setCursor(Qt.CursorShape.CrossCursor)
    
    def mouseReleaseEvent(self, event: QMouseEvent):
        """Gestisce l'evento di rilascio del mouse."""
        if event.button() == Qt.MouseButton.LeftButton:
            if self.is_selecting or self.is_resizing:
                self.selection_finished.emit(self.selection_rect)
            
            self.is_selecting = False
            self.is_resizing = False
            self.resize_handle = None
            self.setCursor(Qt.CursorShape.CrossCursor)
    
    def paintEvent(self, event):
        """Disegna il widget con l'immagine e la selezione."""
        super().paintEvent(event)
        
        if not self.selection_rect.isEmpty():
            painter = QPainter(self)
            painter.setRenderHint(QPainter.RenderHint.Antialiasing)
            
            # Disegna l'overlay scuro SOLO FUORI dalla selezione
            overlay_color = QColor(0, 0, 0, 100)
            outer_path = QPainterPath()
            outer_path.addRect(QRectF(self.rect())) # Path per l'intero widget

            inner_path = QPainterPath()
            inner_path.addRect(QRectF(self.selection_rect)) # Path per l'area di selezione

            # Sottrai l'area di selezione dal path dell'intero widget
            overlay_path = outer_path.subtracted(inner_path)

            painter.fillPath(overlay_path, QBrush(overlay_color))
            
            # Disegna il bordo della selezione (sopra l'overlay e l'immagine)
            pen = QPen(QColor(0, 120, 215), 2, Qt.PenStyle.SolidLine)
            painter.setPen(pen)
            painter.drawRect(self.selection_rect)
            
            # Disegna gli handle di ridimensionamento
            self._draw_resize_handles(painter)
            
            # Disegna le informazioni della selezione
            self._draw_selection_info(painter)
    
    def _get_resize_handle(self, pos: QPoint) -> Optional[str]:
        """
        Determina quale handle di ridimensionamento è sotto il punto specificato.
        
        Args:
            pos: Posizione del mouse
            
        Returns:
            str: Nome dell'handle ('tl', 'tr', 'bl', 'br', 't', 'b', 'l', 'r') o None
        """
        if self.selection_rect.isEmpty():
            return None
        
        rect = self.selection_rect
        handle_size = self.handle_size
        
        # Handle agli angoli
        if QRect(rect.topLeft() - QPoint(handle_size//2, handle_size//2), 
                QSize(handle_size, handle_size)).contains(pos):
            return 'tl'  # Top-left
        elif QRect(rect.topRight() - QPoint(handle_size//2, handle_size//2), 
                  QSize(handle_size, handle_size)).contains(pos):
            return 'tr'  # Top-right
        elif QRect(rect.bottomLeft() - QPoint(handle_size//2, handle_size//2), 
                  QSize(handle_size, handle_size)).contains(pos):
            return 'bl'  # Bottom-left
        elif QRect(rect.bottomRight() - QPoint(handle_size//2, handle_size//2), 
                  QSize(handle_size, handle_size)).contains(pos):
            return 'br'  # Bottom-right
        
        # Handle sui lati
        elif QRect(QPoint(rect.center().x() - handle_size//2, rect.top() - handle_size//2),
                  QSize(handle_size, handle_size)).contains(pos):
            return 't'   # Top
        elif QRect(QPoint(rect.center().x() - handle_size//2, rect.bottom() - handle_size//2),
                  QSize(handle_size, handle_size)).contains(pos):
            return 'b'   # Bottom
        elif QRect(QPoint(rect.left() - handle_size//2, rect.center().y() - handle_size//2),
                  QSize(handle_size, handle_size)).contains(pos):
            return 'l'   # Left
        elif QRect(QPoint(rect.right() - handle_size//2, rect.center().y() - handle_size//2),
                  QSize(handle_size, handle_size)).contains(pos):
            return 'r'   # Right
        
        return None
    
    def _get_resize_cursor(self, handle: str) -> Qt.CursorShape:
        """
        Restituisce il cursore appropriato per l'handle specificato.
        
        Args:
            handle: Nome dell'handle
            
        Returns:
            Qt.CursorShape: Forma del cursore
        """
        cursor_map = {
            'tl': Qt.CursorShape.SizeFDiagCursor,
            'tr': Qt.CursorShape.SizeBDiagCursor,
            'bl': Qt.CursorShape.SizeBDiagCursor,
            'br': Qt.CursorShape.SizeFDiagCursor,
            't': Qt.CursorShape.SizeVerCursor,
            'b': Qt.CursorShape.SizeVerCursor,
            'l': Qt.CursorShape.SizeHorCursor,
            'r': Qt.CursorShape.SizeHorCursor
        }
        return cursor_map.get(handle, Qt.CursorShape.CrossCursor)
    
    def _resize_selection(self, pos: QPoint):
        """
        Ridimensiona la selezione in base all'handle e alla posizione.
        
        Args:
            pos: Nuova posizione del mouse
        """
        if not self.resize_handle:
            return
        
        rect = self.selection_rect
        handle = self.resize_handle
        
        if handle == 'tl':
            rect.setTopLeft(pos)
        elif handle == 'tr':
            rect.setTopRight(pos)
        elif handle == 'bl':
            rect.setBottomLeft(pos)
        elif handle == 'br':
            rect.setBottomRight(pos)
        elif handle == 't':
            rect.setTop(pos.y())
        elif handle == 'b':
            rect.setBottom(pos.y())
        elif handle == 'l':
            rect.setLeft(pos.x())
        elif handle == 'r':
            rect.setRight(pos.x())
        
        # Assicurati che il rettangolo sia valido
        self.selection_rect = rect.normalized()
        
        # Limita la selezione ai confini del widget
        widget_rect = self.rect()
        self.selection_rect = self.selection_rect.intersected(widget_rect)
    
    def _draw_resize_handles(self, painter: QPainter):
        """
        Disegna gli handle di ridimensionamento.
        
        Args:
            painter: QPainter per il disegno
        """
        if self.selection_rect.isEmpty():
            return
        
        rect = self.selection_rect
        handle_size = self.handle_size
        
        # Colore e stile degli handle
        painter.setPen(QPen(QColor(0, 120, 215), 1))
        painter.setBrush(QBrush(QColor(255, 255, 255)))
        
        # Handle agli angoli
        handles = [
            rect.topLeft(),
            rect.topRight(),
            rect.bottomLeft(),
            rect.bottomRight(),
            QPoint(rect.center().x(), rect.top()),
            QPoint(rect.center().x(), rect.bottom()),
            QPoint(rect.left(), rect.center().y()),
            QPoint(rect.right(), rect.center().y())
        ]
        
        for handle_pos in handles:
            handle_rect = QRect(
                handle_pos - QPoint(handle_size//2, handle_size//2),
                QSize(handle_size, handle_size)
            )
            painter.drawRect(handle_rect)
    
    def _draw_selection_info(self, painter: QPainter):
        """
        Disegna le informazioni sulla selezione.
        
        Args:
            painter: QPainter per il disegno
        """
        if self.selection_rect.isEmpty():
            return
        
        # Ottieni le coordinate nell'immagine originale
        x, y, width, height = self.get_selection_coordinates()
        
        if width > 0 and height > 0:
            info_text = f"{width} × {height} px"
            
            # Configura il font
            font = QFont("Arial", 10)
            painter.setFont(font)
            
            # Calcola la posizione del testo
            font_metrics = QFontMetrics(font)
            text_rect = font_metrics.boundingRect(info_text)
            
            # Posiziona il testo sopra la selezione
            text_pos = QPoint(
                self.selection_rect.left(),
                self.selection_rect.top() - 5
            )
            
            # Se il testo esce dal widget, spostalo
            if text_pos.y() - text_rect.height() < 0:
                text_pos.setY(self.selection_rect.bottom() + text_rect.height() + 5)
            
            # Disegna lo sfondo del testo
            bg_rect = QRect(text_pos - QPoint(2, text_rect.height()), 
                           text_rect.size() + QSize(4, 2))
            painter.fillRect(bg_rect, QColor(0, 0, 0, 180))
            
            # Disegna il testo
            painter.setPen(QPen(QColor(255, 255, 255)))
            painter.drawText(text_pos, info_text)


class CropWidget(QWidget):
    """
    Widget completo per il ritaglio delle immagini.
    Include l'area di selezione e i controlli.
    """
    
    # Segnali
    crop_requested = Signal(int, int, int, int)  # x, y, width, height
    crop_cancelled = Signal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        self.current_image_path = None
        self.original_pixmap = None
        
        self._setup_ui()
        self._connect_signals()
        
        logger.info("CropWidget inizializzato")
    
    def _setup_ui(self):
        """Configura l'interfaccia utente."""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # Titolo
        title_label = QLabel("Ritaglio Immagine")
        title_label.setStyleSheet(f"""
            QLabel {{
                color: {Styles.TEXT_MAIN};
                font-size: 16px;
                font-weight: bold;
                padding: 5px;
            }}
        """)
        layout.addWidget(title_label)
        
        # Area di selezione
        self.crop_label = CropSelectionLabel()
        self.crop_label.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        layout.addWidget(self.crop_label)
        
        # Informazioni sulla selezione
        self.info_label = QLabel("Seleziona un'area da ritagliare trascinando il mouse")
        self.info_label.setStyleSheet(f"""
            QLabel {{
                color: {Styles.TEXT_SECONDARY};
                font-size: 12px;
                padding: 5px;
            }}
        """)
        self.info_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(self.info_label)
        
        # Pulsanti di controllo
        button_layout = QHBoxLayout()
        button_layout.setSpacing(10)
        
        self.clear_button = QPushButton("Pulisci Selezione")
        self.clear_button.setStyleSheet(Styles.SECONDARY_BUTTON_STYLE)
        self.clear_button.setEnabled(False)
        
        self.cancel_button = QPushButton("Annulla")
        self.cancel_button.setStyleSheet(Styles.SECONDARY_BUTTON_STYLE)
        
        self.crop_button = QPushButton("Ritaglia")
        self.crop_button.setStyleSheet(Styles.ACTION_BUTTON_STYLE)
        self.crop_button.setEnabled(False)
        
        button_layout.addStretch()
        button_layout.addWidget(self.clear_button)
        button_layout.addWidget(self.cancel_button)
        button_layout.addWidget(self.crop_button)
        
        layout.addLayout(button_layout)
        
        # Stile generale
        self.setStyleSheet(f"""
            CropWidget {{
                background-color: {Styles.DARK_2};
                border: 1px solid {Styles.BORDER_COLOR};
                border-radius: 8px;
            }}
        """)
    
    def _connect_signals(self):
        """Connette i segnali agli slot."""
        self.crop_label.selection_changed.connect(self._on_selection_changed)
        self.crop_label.selection_finished.connect(self._on_selection_finished)
        
        self.clear_button.clicked.connect(self._clear_selection)
        self.cancel_button.clicked.connect(self._cancel_crop)
        self.crop_button.clicked.connect(self._apply_crop)
    
    def set_image(self, image_path: str, pixmap: QPixmap):
        """
        Imposta l'immagine da ritagliare.
        
        Args:
            image_path: Percorso del file immagine
            pixmap: QPixmap dell'immagine
        """
        self.current_image_path = image_path
        self.original_pixmap = pixmap
        self.crop_label.set_image(pixmap)
        
        # Reset dei controlli
        self.clear_button.setEnabled(False)
        self.crop_button.setEnabled(False)
        self.info_label.setText("Seleziona un'area da ritagliare trascinando il mouse")
        
        logger.info(f"Immagine impostata per il ritaglio: {os.path.basename(image_path)}")
    
    def _on_selection_changed(self, rect: QRect):
        """Gestisce il cambio della selezione."""
        has_selection = not rect.isEmpty()
        self.clear_button.setEnabled(has_selection)
        
        if has_selection:
            x, y, width, height = self.crop_label.get_selection_coordinates()
            if width > 0 and height > 0:
                self.info_label.setText(f"Selezione: {width} × {height} px")
            else:
                self.info_label.setText("Selezione non valida")
        else:
            self.info_label.setText("Seleziona un'area da ritagliare trascinando il mouse")
    
    def _on_selection_finished(self, rect: QRect):
        """Gestisce il completamento della selezione."""
        has_valid_selection = not rect.isEmpty()
        if has_valid_selection:
            x, y, width, height = self.crop_label.get_selection_coordinates()
            has_valid_selection = width > 10 and height > 10  # Minimo 10x10 pixel
        
        self.crop_button.setEnabled(has_valid_selection)
        
        if has_valid_selection:
            x, y, width, height = self.crop_label.get_selection_coordinates()
            self.info_label.setText(f"Pronto per ritagliare: {width} × {height} px")
        else:
            self.info_label.setText("Selezione troppo piccola (minimo 10×10 px)")
    
    def _clear_selection(self):
        """Pulisce la selezione corrente."""
        self.crop_label.clear_selection()
        self.clear_button.setEnabled(False)
        self.crop_button.setEnabled(False)
        self.info_label.setText("Seleziona un'area da ritagliare trascinando il mouse")
    
    def _cancel_crop(self):
        """Annulla l'operazione di ritaglio."""
        self.crop_cancelled.emit()
    
    def _apply_crop(self):
        """Applica il ritaglio all'immagine."""
        if not self.current_image_path or self.crop_label.selection_rect.isEmpty():
            return
        
        x, y, width, height = self.crop_label.get_selection_coordinates()
        
        if width > 0 and height > 0:
            self.crop_requested.emit(x, y, width, height)
            logger.info(f"Ritaglio richiesto: ({x}, {y}, {width}, {height})")
        else:
            logger.warning("Tentativo di ritaglio con coordinate non valide")
