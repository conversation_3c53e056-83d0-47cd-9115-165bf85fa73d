2025-06-10 00:18:00,555 | INFO     | PhotoDrop | <module>:25 | ================================================================================
2025-06-10 00:18:00,555 | INFO     | PhotoDrop | <module>:26 | Nuova sessione avviata il 2025-06-10 00:18:00
2025-06-10 00:18:00,556 | INFO     | PhotoDrop | <module>:27 | ================================================================================
2025-06-10 00:18:00,556 | INFO     | PhotoRenamer | <module>:34 | =================================================================
2025-06-10 00:18:00,556 | INFO     | PhotoRenamer | <module>:35 | Avvio PhotoDrop Applicazione
2025-06-10 00:18:00,556 | INFO     | PhotoRenamer | <module>:36 | =================================================================
2025-06-10 00:18:00,616 | INFO     | PhotoDrop.SettingsModel | _load_settings:112 | File impostazioni non trovato, uso impostazioni predefinite
2025-06-10 00:18:00,616 | INFO     | PhotoDrop.SettingsModel | __init__:93 | SettingsModel inizializzato con file: C:\xampp\htdocs\progetti\photodrop\config\settings.json
2025-06-10 00:18:00,617 | INFO     | core.image_cache | __init__:338 | Cache su disco inizializzata in cache/images (max 500MB, scadenza 30 giorni)
2025-06-10 00:18:00,617 | INFO     | core.image_cache | __init__:342 | Cache immagini inizializzata (max in memoria: 1000 elementi)
2025-06-10 00:18:00,620 | INFO     | PhotoDrop.ModernTopbar | __init__:70 | ModernTopbar inizializzata
2025-06-10 00:18:00,621 | INFO     | PhotoRenamer.LeftPanel | __init__:378 | LeftPanel inizializzato - test logger
2025-06-10 00:18:00,621 | DEBUG    | PhotoRenamer.LeftPanel | _load_last_folder_if_enabled:1019 | Nessuna ultima cartella valida trovata
2025-06-10 00:18:00,622 | INFO     | PhotoRenamer.LeftPanel | __init__:46 | [DND] DraggableListWidget inizializzato
2025-06-10 00:18:00,622 | INFO     | PhotoRenamer.LeftPanel | __init__:55 | [DND] DraggableListWidget configurato con gestione manuale
2025-06-10 00:18:00,622 | INFO     | PhotoRenamer.LeftPanel | _setup_ui:436 | [DND] LeftPanel: creata istanza DraggableListWidget
2025-06-10 00:18:00,622 | DEBUG    | PhotoRenamer.LeftPanel | _setup_list_widget:585 | [LP.__init__] Connesso segnale currentItemChanged a _on_current_item_changed
2025-06-10 00:18:00,645 | INFO     | PhotoDrop.MainWindow | _connect_signals:207 | [MW._connect_signals] Connesso left_panel.selection_changed a _on_selection_changed
2025-06-10 00:18:00,938 | INFO     | PhotoDrop.MainWindow | __init__:127 | Applicazione inizializzata
2025-06-10 00:18:47,081 | INFO     | PhotoDrop.MainController | on_folder_changed:33 | Cartella corrente cambiata: C:/Users/<USER>/Desktop/foto2
2025-06-10 00:18:47,081 | INFO     | PhotoRenamer.ImageLoader | load:88 | Trovate 6 immagini nella cartella: C:/Users/<USER>/Desktop/foto2
2025-06-10 00:18:47,082 | INFO     | PhotoRenamer.LeftPanel | _load_images_async:639 | Caricamento immagini avviato da: C:/Users/<USER>/Desktop/foto2
2025-06-10 00:18:47,101 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: YCbCrPositioning (531) - type: short (3) - value: b'\x01\x00'
2025-06-10 00:18:47,101 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: YCbCrPositioning (531) - type: short (3) - value: b'\x01\x00'
2025-06-10 00:18:47,101 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: YCbCrPositioning (531) - type: short (3) - value: b'\x01\x00'
2025-06-10 00:18:47,102 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Make (271) - type: string (2) Tag Location: 22 - Data Location: 134 - value: b'samsung\x00'
2025-06-10 00:18:47,102 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: YCbCrPositioning (531) - type: short (3) - value: b'\x01\x00'
2025-06-10 00:18:47,102 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DateTime (306) - type: string (2) Tag Location: 34 - Data Location: 138 - value: b'2025:03:08 16:15:15\x00'
2025-06-10 00:18:47,102 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: YCbCrPositioning (531) - type: short (3) - value: b'\x01\x00'
2025-06-10 00:18:47,102 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DateTime (306) - type: string (2) Tag Location: 34 - Data Location: 138 - value: b'2025:03:08 16:07:42\x00'
2025-06-10 00:18:47,102 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DateTime (306) - type: string (2) Tag Location: 34 - Data Location: 138 - value: b'2025:03:08 16:05:29\x00'
2025-06-10 00:18:47,102 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Model (272) - type: string (2) Tag Location: 34 - Data Location: 142 - value: b'SM-P615\x00'
2025-06-10 00:18:47,102 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DateTime (306) - type: string (2) Tag Location: 34 - Data Location: 138 - value: b'2025:03:08 15:53:05\x00'
2025-06-10 00:18:47,102 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Make (271) - type: string (2) Tag Location: 46 - Data Location: 158 - value: b'samsung\x00'
2025-06-10 00:18:47,102 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DateTime (306) - type: string (2) Tag Location: 34 - Data Location: 138 - value: b'2025:03:08 16:05:44\x00'
2025-06-10 00:18:47,103 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Make (271) - type: string (2) Tag Location: 46 - Data Location: 158 - value: b'samsung\x00'
2025-06-10 00:18:47,103 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Make (271) - type: string (2) Tag Location: 46 - Data Location: 158 - value: b'samsung\x00'
2025-06-10 00:18:47,103 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Orientation (274) - type: short (3) - value: b'\x00\x01'
2025-06-10 00:18:47,103 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Make (271) - type: string (2) Tag Location: 46 - Data Location: 158 - value: b'samsung\x00'
2025-06-10 00:18:47,103 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Model (272) - type: string (2) Tag Location: 58 - Data Location: 166 - value: b'SM-P615\x00'
2025-06-10 00:18:47,103 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Make (271) - type: string (2) Tag Location: 46 - Data Location: 158 - value: b'samsung\x00'
2025-06-10 00:18:47,103 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Model (272) - type: string (2) Tag Location: 58 - Data Location: 166 - value: b'SM-P615\x00'
2025-06-10 00:18:47,103 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Model (272) - type: string (2) Tag Location: 58 - Data Location: 166 - value: b'SM-P615\x00'
2025-06-10 00:18:47,103 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: XResolution (282) - type: rational (5) Tag Location: 58 - Data Location: 150 - value: b'\x00\x00\x00H\x00\x00\x00\x01'
2025-06-10 00:18:47,103 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Model (272) - type: string (2) Tag Location: 58 - Data Location: 166 - value: b'SM-P615\x00'
2025-06-10 00:18:47,103 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Orientation (274) - type: short (3) - value: b'\x01\x00'
2025-06-10 00:18:47,103 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Model (272) - type: string (2) Tag Location: 58 - Data Location: 166 - value: b'SM-P615\x00'
2025-06-10 00:18:47,104 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Orientation (274) - type: short (3) - value: b'\x01\x00'
2025-06-10 00:18:47,104 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Orientation (274) - type: short (3) - value: b'\x01\x00'
2025-06-10 00:18:47,104 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: YResolution (283) - type: rational (5) Tag Location: 70 - Data Location: 158 - value: b'\x00\x00\x00H\x00\x00\x00\x01'
2025-06-10 00:18:47,104 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Orientation (274) - type: short (3) - value: b'\x01\x00'
2025-06-10 00:18:47,104 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: XResolution (282) - type: rational (5) Tag Location: 82 - Data Location: 174 - value: b'H\x00\x00\x00\x01\x00\x00\x00'
2025-06-10 00:18:47,104 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Orientation (274) - type: short (3) - value: b'\x01\x00'
2025-06-10 00:18:47,104 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: XResolution (282) - type: rational (5) Tag Location: 82 - Data Location: 174 - value: b'H\x00\x00\x00\x01\x00\x00\x00'
2025-06-10 00:18:47,105 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: XResolution (282) - type: rational (5) Tag Location: 82 - Data Location: 174 - value: b'H\x00\x00\x00\x01\x00\x00\x00'
2025-06-10 00:18:47,105 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ResolutionUnit (296) - type: short (3) - value: b'\x00\x02'
2025-06-10 00:18:47,105 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: XResolution (282) - type: rational (5) Tag Location: 82 - Data Location: 174 - value: b'H\x00\x00\x00\x01\x00\x00\x00'
2025-06-10 00:18:47,105 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: YResolution (283) - type: rational (5) Tag Location: 94 - Data Location: 182 - value: b'H\x00\x00\x00\x01\x00\x00\x00'
2025-06-10 00:18:47,105 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: XResolution (282) - type: rational (5) Tag Location: 82 - Data Location: 174 - value: b'H\x00\x00\x00\x01\x00\x00\x00'
2025-06-10 00:18:47,105 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: YResolution (283) - type: rational (5) Tag Location: 94 - Data Location: 182 - value: b'H\x00\x00\x00\x01\x00\x00\x00'
2025-06-10 00:18:47,105 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: YResolution (283) - type: rational (5) Tag Location: 94 - Data Location: 182 - value: b'H\x00\x00\x00\x01\x00\x00\x00'
2025-06-10 00:18:47,105 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Software (305) - type: string (2) Tag Location: 94 - Data Location: 166 - value: b'ImageMeter\x00'
2025-06-10 00:18:47,105 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: YResolution (283) - type: rational (5) Tag Location: 94 - Data Location: 182 - value: b'H\x00\x00\x00\x01\x00\x00\x00'
2025-06-10 00:18:47,106 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ResolutionUnit (296) - type: short (3) - value: b'\x02\x00'
2025-06-10 00:18:47,106 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: YResolution (283) - type: rational (5) Tag Location: 94 - Data Location: 182 - value: b'H\x00\x00\x00\x01\x00\x00\x00'
2025-06-10 00:18:47,106 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ResolutionUnit (296) - type: short (3) - value: b'\x02\x00'
2025-06-10 00:18:47,106 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ResolutionUnit (296) - type: short (3) - value: b'\x02\x00'
2025-06-10 00:18:47,106 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DateTime (306) - type: string (2) Tag Location: 106 - Data Location: 177 - value: b'2025:03:08 16:06:23\x00'
2025-06-10 00:18:47,106 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ResolutionUnit (296) - type: short (3) - value: b'\x02\x00'
2025-06-10 00:18:47,106 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Software (305) - type: string (2) Tag Location: 118 - Data Location: 190 - value: b'ImageMeter\x00'
2025-06-10 00:18:47,106 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ResolutionUnit (296) - type: short (3) - value: b'\x02\x00'
2025-06-10 00:18:47,106 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Software (305) - type: string (2) Tag Location: 118 - Data Location: 190 - value: b'ImageMeter\x00'
2025-06-10 00:18:47,106 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Software (305) - type: string (2) Tag Location: 118 - Data Location: 190 - value: b'ImageMeter\x00'
2025-06-10 00:18:47,106 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: YCbCrPositioning (531) - type: short (3) - value: b'\x00\x01'
2025-06-10 00:18:47,107 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Software (305) - type: string (2) Tag Location: 118 - Data Location: 190 - value: b'ImageMeter\x00'
2025-06-10 00:18:47,107 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExifIFD (34665) - type: long (4) - value: b'\xca\x00\x00\x00'
2025-06-10 00:18:47,107 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Software (305) - type: string (2) Tag Location: 118 - Data Location: 190 - value: b'ImageMeter\x00'
2025-06-10 00:18:47,107 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExifIFD (34665) - type: long (4) - value: b'\xca\x00\x00\x00'
2025-06-10 00:18:47,107 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExifIFD (34665) - type: long (4) - value: b'\xca\x00\x00\x00'
2025-06-10 00:18:47,107 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExifIFD (34665) - type: long (4) - value: b'\x00\x00\x00\xc5'
2025-06-10 00:18:47,107 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExifIFD (34665) - type: long (4) - value: b'\xca\x00\x00\x00'
2025-06-10 00:18:47,108 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureTime (33434) - type: rational (5) Tag Location: 216 - Data Location: 548 - value: b'\x01\x00\x00\x00\x19\x00\x00\x00'
2025-06-10 00:18:47,108 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExifIFD (34665) - type: long (4) - value: b'\xca\x00\x00\x00'
2025-06-10 00:18:47,108 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureTime (33434) - type: rational (5) Tag Location: 216 - Data Location: 548 - value: b'\x01\x00\x00\x00\x14\x00\x00\x00'
2025-06-10 00:18:47,108 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureTime (33434) - type: rational (5) Tag Location: 216 - Data Location: 560 - value: b'\x01\x00\x00\x00!\x00\x00\x00'
2025-06-10 00:18:47,109 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureTime (33434) - type: rational (5) Tag Location: 211 - Data Location: 547 - value: b'\x00\x00\x00\x01\x00\x00\x002'
2025-06-10 00:18:47,109 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureTime (33434) - type: rational (5) Tag Location: 216 - Data Location: 560 - value: b'\x01\x00\x00\x00\x19\x00\x00\x00'
2025-06-10 00:18:47,109 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: FNumber (33437) - type: rational (5) Tag Location: 228 - Data Location: 556 - value: b'\xbe\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:18:47,109 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureTime (33434) - type: rational (5) Tag Location: 216 - Data Location: 560 - value: b'\x01\x00\x00\x002\x00\x00\x00'
2025-06-10 00:18:47,109 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: FNumber (33437) - type: rational (5) Tag Location: 228 - Data Location: 556 - value: b'\xbe\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:18:47,109 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: FNumber (33437) - type: rational (5) Tag Location: 228 - Data Location: 568 - value: b'\xbe\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:18:47,109 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: FNumber (33437) - type: rational (5) Tag Location: 223 - Data Location: 555 - value: b'\x00\x00\x00\xbe\x00\x00\x00d'
2025-06-10 00:18:47,110 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: FNumber (33437) - type: rational (5) Tag Location: 228 - Data Location: 568 - value: b'\xbe\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:18:47,110 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureProgram (34850) - type: short (3) - value: b'\x02\x00'
2025-06-10 00:18:47,110 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: FNumber (33437) - type: rational (5) Tag Location: 228 - Data Location: 568 - value: b'\xbe\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:18:47,110 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureProgram (34850) - type: short (3) - value: b'\x02\x00'
2025-06-10 00:18:47,110 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureProgram (34850) - type: short (3) - value: b'\x02\x00'
2025-06-10 00:18:47,111 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureProgram (34850) - type: short (3) - value: b'\x00\x02'
2025-06-10 00:18:47,111 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureProgram (34850) - type: short (3) - value: b'\x02\x00'
2025-06-10 00:18:47,111 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ISOSpeedRatings (34855) - type: short (3) - value: b'\xfa\x00'
2025-06-10 00:18:47,111 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureProgram (34850) - type: short (3) - value: b'\x02\x00'
2025-06-10 00:18:47,111 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ISOSpeedRatings (34855) - type: short (3) - value: b'\x90\x01'
2025-06-10 00:18:47,111 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ISOSpeedRatings (34855) - type: short (3) - value: b'\xa0\x00'
2025-06-10 00:18:47,111 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ISOSpeedRatings (34855) - type: short (3) - value: b'\x00}'
2025-06-10 00:18:47,111 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ISOSpeedRatings (34855) - type: short (3) - value: b'\xa0\x00'
2025-06-10 00:18:47,112 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExifVersion (36864) - type: undefined (7) - value: b'0220'
2025-06-10 00:18:47,112 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ISOSpeedRatings (34855) - type: short (3) - value: b'd\x00'
2025-06-10 00:18:47,112 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExifVersion (36864) - type: undefined (7) - value: b'0220'
2025-06-10 00:18:47,112 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExifVersion (36864) - type: undefined (7) - value: b'0220'
2025-06-10 00:18:47,112 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExifVersion (36864) - type: undefined (7) - value: b'0220'
2025-06-10 00:18:47,112 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExifVersion (36864) - type: undefined (7) - value: b'0220'
2025-06-10 00:18:47,112 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DateTimeOriginal (36867) - type: string (2) Tag Location: 276 - Data Location: 564 - value: b'2025:03:08 15:53:05\x00'
2025-06-10 00:18:47,112 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExifVersion (36864) - type: undefined (7) - value: b'0220'
2025-06-10 00:18:47,112 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DateTimeOriginal (36867) - type: string (2) Tag Location: 276 - Data Location: 564 - value: b'2025:03:08 16:07:42\x00'
2025-06-10 00:18:47,113 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DateTimeOriginal (36867) - type: string (2) Tag Location: 276 - Data Location: 576 - value: b'2025:03:08 16:05:29\x00'
2025-06-10 00:18:47,113 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DateTimeOriginal (36867) - type: string (2) Tag Location: 271 - Data Location: 563 - value: b'2025:03:08 16:06:23\x00'
2025-06-10 00:18:47,113 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DateTimeOriginal (36867) - type: string (2) Tag Location: 276 - Data Location: 576 - value: b'2025:03:08 16:15:15\x00'
2025-06-10 00:18:47,114 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DateTimeDigitized (36868) - type: string (2) Tag Location: 288 - Data Location: 584 - value: b'2025:03:08 15:53:05\x00'
2025-06-10 00:18:47,114 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DateTimeOriginal (36867) - type: string (2) Tag Location: 276 - Data Location: 576 - value: b'2025:03:08 16:05:44\x00'
2025-06-10 00:18:47,114 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DateTimeDigitized (36868) - type: string (2) Tag Location: 288 - Data Location: 584 - value: b'2025:03:08 16:07:42\x00'
2025-06-10 00:18:47,114 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DateTimeDigitized (36868) - type: string (2) Tag Location: 288 - Data Location: 596 - value: b'2025:03:08 16:05:29\x00'
2025-06-10 00:18:47,114 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DateTimeDigitized (36868) - type: string (2) Tag Location: 283 - Data Location: 583 - value: b'2025:03:08 16:06:23\x00'
2025-06-10 00:18:47,114 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DateTimeDigitized (36868) - type: string (2) Tag Location: 288 - Data Location: 596 - value: b'2025:03:08 16:15:15\x00'
2025-06-10 00:18:47,114 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: unknown (36880) - type: string (2) Tag Location: 300 - Data Location: 604 - value: b'+01:00\x00'
2025-06-10 00:18:47,114 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DateTimeDigitized (36868) - type: string (2) Tag Location: 288 - Data Location: 596 - value: b'2025:03:08 16:05:44\x00'
2025-06-10 00:18:47,114 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: unknown (36880) - type: string (2) Tag Location: 300 - Data Location: 604 - value: b'+01:00\x00'
2025-06-10 00:18:47,115 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: unknown (36880) - type: string (2) Tag Location: 300 - Data Location: 616 - value: b'+01:00\x00'
2025-06-10 00:18:47,115 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: unknown (36880) - type: string (2) Tag Location: 295 - Data Location: 603 - value: b'+01:00\x00'
2025-06-10 00:18:47,115 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: unknown (36880) - type: string (2) Tag Location: 300 - Data Location: 616 - value: b'+01:00\x00'
2025-06-10 00:18:47,115 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: unknown (36881) - type: string (2) Tag Location: 312 - Data Location: 612 - value: b'+01:00\x00'
2025-06-10 00:18:47,115 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: unknown (36880) - type: string (2) Tag Location: 300 - Data Location: 616 - value: b'+01:00\x00'
2025-06-10 00:18:47,115 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: unknown (36881) - type: string (2) Tag Location: 312 - Data Location: 612 - value: b'+01:00\x00'
2025-06-10 00:18:47,115 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: unknown (36881) - type: string (2) Tag Location: 312 - Data Location: 624 - value: b'+01:00\x00'
2025-06-10 00:18:47,115 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: unknown (36881) - type: string (2) Tag Location: 307 - Data Location: 610 - value: b'+01:00\x00'
2025-06-10 00:18:47,115 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: unknown (36881) - type: string (2) Tag Location: 312 - Data Location: 624 - value: b'+01:00\x00'
2025-06-10 00:18:47,115 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ShutterSpeedValue (37377) - type: rational (5) Tag Location: 324 - Data Location: 620 - value: b'\x01\x00\x00\x00\x19\x00\x00\x00'
2025-06-10 00:18:47,115 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: unknown (36881) - type: string (2) Tag Location: 312 - Data Location: 624 - value: b'+01:00\x00'
2025-06-10 00:18:47,116 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ShutterSpeedValue (37377) - type: rational (5) Tag Location: 324 - Data Location: 620 - value: b'\x01\x00\x00\x00\x14\x00\x00\x00'
2025-06-10 00:18:47,116 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ShutterSpeedValue (37377) - type: rational (5) Tag Location: 324 - Data Location: 632 - value: b'\x01\x00\x00\x00!\x00\x00\x00'
2025-06-10 00:18:47,116 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ShutterSpeedValue (37377) - type: signed rational (10) Tag Location: 319 - Data Location: 617 - value: b'\x00\x00\x00\x01\x00\x00\x002'
2025-06-10 00:18:47,116 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ShutterSpeedValue (37377) - type: rational (5) Tag Location: 324 - Data Location: 632 - value: b'\x01\x00\x00\x00\x19\x00\x00\x00'
2025-06-10 00:18:47,116 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ApertureValue (37378) - type: rational (5) Tag Location: 336 - Data Location: 628 - value: b'\xb9\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:18:47,116 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ShutterSpeedValue (37377) - type: rational (5) Tag Location: 324 - Data Location: 632 - value: b'\x01\x00\x00\x002\x00\x00\x00'
2025-06-10 00:18:47,116 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ApertureValue (37378) - type: rational (5) Tag Location: 336 - Data Location: 628 - value: b'\xb9\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:18:47,116 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ApertureValue (37378) - type: rational (5) Tag Location: 336 - Data Location: 640 - value: b'\xb9\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:18:47,116 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ApertureValue (37378) - type: rational (5) Tag Location: 331 - Data Location: 625 - value: b'\x00\x00\x00\xb9\x00\x00\x00d'
2025-06-10 00:18:47,116 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ApertureValue (37378) - type: rational (5) Tag Location: 336 - Data Location: 640 - value: b'\xb9\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:18:47,116 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureBiasValue (37380) - type: signed rational (10) Tag Location: 348 - Data Location: 636 - value: b'\x00\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:18:47,116 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ApertureValue (37378) - type: rational (5) Tag Location: 336 - Data Location: 640 - value: b'\xb9\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:18:47,117 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: BrightnessValue (37379) - type: signed rational (10) Tag Location: 348 - Data Location: 648 - value: b'\xe7\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:18:47,117 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: BrightnessValue (37379) - type: signed rational (10) Tag Location: 348 - Data Location: 648 - value: b'b\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:18:47,117 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureBiasValue (37380) - type: signed rational (10) Tag Location: 360 - Data Location: 656 - value: b'\x00\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:18:47,117 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: BrightnessValue (37379) - type: signed rational (10) Tag Location: 348 - Data Location: 648 - value: b'.\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:18:47,117 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: MaxApertureValue (37381) - type: rational (5) Tag Location: 360 - Data Location: 644 - value: b'\xb9\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:18:47,117 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: MeteringMode (37383) - type: short (3) - value: b'\x02\x00'
2025-06-10 00:18:47,117 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureBiasValue (37380) - type: signed rational (10) Tag Location: 360 - Data Location: 656 - value: b'\x00\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:18:47,117 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: BrightnessValue (37379) - type: signed rational (10) Tag Location: 343 - Data Location: 633 - value: b'\x00\x00\x00\xb6\x00\x00\x00d'
2025-06-10 00:18:47,118 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: MaxApertureValue (37381) - type: rational (5) Tag Location: 372 - Data Location: 664 - value: b'\xb9\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:18:47,118 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureBiasValue (37380) - type: signed rational (10) Tag Location: 360 - Data Location: 656 - value: b'\x00\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:18:47,118 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureBiasValue (37380) - type: signed rational (10) Tag Location: 348 - Data Location: 636 - value: b'\x00\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:18:47,118 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Flash (37385) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:18:47,118 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: MaxApertureValue (37381) - type: rational (5) Tag Location: 372 - Data Location: 664 - value: b'\xb9\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:18:47,118 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureBiasValue (37380) - type: signed rational (10) Tag Location: 355 - Data Location: 641 - value: b'\x00\x00\x00\x00\x00\x00\x00d'
2025-06-10 00:18:47,118 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: MeteringMode (37383) - type: short (3) - value: b'\x02\x00'
2025-06-10 00:18:47,118 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: MaxApertureValue (37381) - type: rational (5) Tag Location: 372 - Data Location: 664 - value: b'\xb9\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:18:47,118 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: MaxApertureValue (37381) - type: rational (5) Tag Location: 360 - Data Location: 644 - value: b'\xb9\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:18:47,119 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: FocalLength (37386) - type: rational (5) Tag Location: 396 - Data Location: 652 - value: b'#\x01\x00\x00d\x00\x00\x00'
2025-06-10 00:18:47,119 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: MeteringMode (37383) - type: short (3) - value: b'\x02\x00'
2025-06-10 00:18:47,119 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: MaxApertureValue (37381) - type: rational (5) Tag Location: 367 - Data Location: 649 - value: b'\x00\x00\x00\xb9\x00\x00\x00d'
2025-06-10 00:18:47,119 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Flash (37385) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:18:47,119 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: MeteringMode (37383) - type: short (3) - value: b'\x02\x00'
2025-06-10 00:18:47,119 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: MeteringMode (37383) - type: short (3) - value: b'\x02\x00'
2025-06-10 00:18:47,119 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SubSec (37520) - type: string (2) - value: b'593\x00'
2025-06-10 00:18:47,119 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Flash (37385) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:18:47,119 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: MeteringMode (37383) - type: short (3) - value: b'\x00\x02'
2025-06-10 00:18:47,119 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: FocalLength (37386) - type: rational (5) Tag Location: 408 - Data Location: 672 - value: b'#\x01\x00\x00d\x00\x00\x00'
2025-06-10 00:18:47,119 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Flash (37385) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:18:47,119 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Flash (37385) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:18:47,120 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SubSecTimeOriginal (37521) - type: string (2) - value: b'593\x00'
2025-06-10 00:18:47,120 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: FocalLength (37386) - type: rational (5) Tag Location: 408 - Data Location: 672 - value: b'#\x01\x00\x00d\x00\x00\x00'
2025-06-10 00:18:47,120 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Flash (37385) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:18:47,120 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SubSec (37520) - type: string (2) - value: b'970\x00'
2025-06-10 00:18:47,120 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: FocalLength (37386) - type: rational (5) Tag Location: 408 - Data Location: 672 - value: b'#\x01\x00\x00d\x00\x00\x00'
2025-06-10 00:18:47,120 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SubSec (37520) - type: string (2) - value: b'502\x00'
2025-06-10 00:18:47,120 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SubsecTimeDigitized (37522) - type: string (2) - value: b'593\x00'
2025-06-10 00:18:47,120 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SubSec (37520) - type: string (2) - value: b'323\x00'
2025-06-10 00:18:47,120 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: FocalLength (37386) - type: rational (5) Tag Location: 403 - Data Location: 657 - value: b'\x00\x00\x01#\x00\x00\x00d'
2025-06-10 00:18:47,120 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SubSecTimeOriginal (37521) - type: string (2) - value: b'970\x00'
2025-06-10 00:18:47,120 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: FocalLength (37386) - type: rational (5) Tag Location: 396 - Data Location: 652 - value: b'#\x01\x00\x00d\x00\x00\x00'
2025-06-10 00:18:47,120 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SubSecTimeOriginal (37521) - type: string (2) - value: b'502\x00'
2025-06-10 00:18:47,121 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ColorSpace (40961) - type: short (3) - value: b'\x01\x00'
2025-06-10 00:18:47,121 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SubSecTimeOriginal (37521) - type: string (2) - value: b'323\x00'
2025-06-10 00:18:47,121 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SubSec (37520) - type: string (2) - value: b'800\x00'
2025-06-10 00:18:47,121 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SubsecTimeDigitized (37522) - type: string (2) - value: b'970\x00'
2025-06-10 00:18:47,121 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SubSec (37520) - type: string (2) - value: b'598\x00'
2025-06-10 00:18:47,121 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SubsecTimeDigitized (37522) - type: string (2) - value: b'502\x00'
2025-06-10 00:18:47,122 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: PixelXDimension (40962) - type: long (4) - value: b'\xc0\x0c\x00\x00'
2025-06-10 00:18:47,122 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SubsecTimeDigitized (37522) - type: string (2) - value: b'323\x00'
2025-06-10 00:18:47,122 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SubSecTimeOriginal (37521) - type: string (2) - value: b'800\x00'
2025-06-10 00:18:47,122 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ColorSpace (40961) - type: short (3) - value: b'\x01\x00'
2025-06-10 00:18:47,122 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SubSecTimeOriginal (37521) - type: string (2) - value: b'598\x00'
2025-06-10 00:18:47,123 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ColorSpace (40961) - type: short (3) - value: b'\x01\x00'
2025-06-10 00:18:47,123 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: PixelYDimension (40963) - type: long (4) - value: b'\xa4\x07\x00\x00'
2025-06-10 00:18:47,123 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ColorSpace (40961) - type: short (3) - value: b'\x01\x00'
2025-06-10 00:18:47,123 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SubsecTimeDigitized (37522) - type: string (2) - value: b'800\x00'
2025-06-10 00:18:47,123 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: PixelXDimension (40962) - type: long (4) - value: b'\xc0\x0c\x00\x00'
2025-06-10 00:18:47,124 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SubsecTimeDigitized (37522) - type: string (2) - value: b'598\x00'
2025-06-10 00:18:47,124 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: PixelXDimension (40962) - type: long (4) - value: b'\xc0\x0c\x00\x00'
2025-06-10 00:18:47,124 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureMode (41986) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:18:47,124 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: PixelXDimension (40962) - type: long (4) - value: b'\xc0\x0c\x00\x00'
2025-06-10 00:18:47,124 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ColorSpace (40961) - type: short (3) - value: b'\x00\x01'
2025-06-10 00:18:47,124 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: PixelYDimension (40963) - type: long (4) - value: b'\xa4\x07\x00\x00'
2025-06-10 00:18:47,124 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ColorSpace (40961) - type: short (3) - value: b'\x01\x00'
2025-06-10 00:18:47,124 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: PixelYDimension (40963) - type: long (4) - value: b'\xa4\x07\x00\x00'
2025-06-10 00:18:47,124 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: WhiteBalance (41987) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:18:47,124 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: PixelYDimension (40963) - type: long (4) - value: b'\xa4\x07\x00\x00'
2025-06-10 00:18:47,125 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: PixelXDimension (40962) - type: long (4) - value: b'\x00\x00\x0c\xc0'
2025-06-10 00:18:47,125 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureMode (41986) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:18:47,125 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: PixelXDimension (40962) - type: long (4) - value: b'\xc0\x0c\x00\x00'
2025-06-10 00:18:47,125 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureMode (41986) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:18:47,125 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DigitalZoomRatio (41988) - type: rational (5) Tag Location: 504 - Data Location: 660 - value: b'd\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:18:47,125 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: FocalLengthIn35mmFilm (41989) - type: short (3) - value: b'\x1b\x00'
2025-06-10 00:18:47,125 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SceneCaptureType (41990) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:18:47,125 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: WhiteBalance (41987) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:18:47,126 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: PixelYDimension (40963) - type: long (4) - value: b'\xa4\x07\x00\x00'
2025-06-10 00:18:47,126 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: WhiteBalance (41987) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:18:47,126 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureMode (41986) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:18:47,126 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: PixelYDimension (40963) - type: long (4) - value: b'\x00\x00\x07\xa4'
2025-06-10 00:18:47,126 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ImageUniqueID (42016) - type: string (2) Tag Location: 540 - Data Location: 668 - value: b'G08LLMJ00HM\x00'
2025-06-10 00:18:47,126 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DigitalZoomRatio (41988) - type: rational (5) Tag Location: 516 - Data Location: 680 - value: b'd\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:18:47,126 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureMode (41986) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:18:47,127 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DigitalZoomRatio (41988) - type: rational (5) Tag Location: 516 - Data Location: 680 - value: b'd\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:18:47,127 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: WhiteBalance (41987) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:18:47,127 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureMode (41986) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:18:47,127 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: FocalLengthIn35mmFilm (41989) - type: short (3) - value: b'\x1b\x00'
2025-06-10 00:18:47,128 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: WhiteBalance (41987) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:18:47,128 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: FocalLengthIn35mmFilm (41989) - type: short (3) - value: b'\x1b\x00'
2025-06-10 00:18:47,128 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DigitalZoomRatio (41988) - type: rational (5) Tag Location: 516 - Data Location: 680 - value: b'd\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:18:47,128 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: WhiteBalance (41987) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:18:47,128 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SceneCaptureType (41990) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:18:47,128 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DigitalZoomRatio (41988) - type: rational (5) Tag Location: 504 - Data Location: 660 - value: b'd\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:18:47,128 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SceneCaptureType (41990) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:18:47,129 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: FocalLengthIn35mmFilm (41989) - type: short (3) - value: b'\x1b\x00'
2025-06-10 00:18:47,129 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DigitalZoomRatio (41988) - type: rational (5) Tag Location: 511 - Data Location: 665 - value: b'\x00\x00\x00d\x00\x00\x00d'
2025-06-10 00:18:47,129 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ImageUniqueID (42016) - type: string (2) Tag Location: 552 - Data Location: 688 - value: b'G08LLMJ00HM\x00'
2025-06-10 00:18:47,129 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: FocalLengthIn35mmFilm (41989) - type: short (3) - value: b'\x1b\x00'
2025-06-10 00:18:47,129 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ImageUniqueID (42016) - type: string (2) Tag Location: 552 - Data Location: 688 - value: b'G08LLMJ00HM\x00'
2025-06-10 00:18:47,129 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SceneCaptureType (41990) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:18:47,130 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: FocalLengthIn35mmFilm (41989) - type: short (3) - value: b'\x00\x1b'
2025-06-10 00:18:47,133 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SceneCaptureType (41990) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:18:47,133 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ImageUniqueID (42016) - type: string (2) Tag Location: 552 - Data Location: 688 - value: b'G08LLMJ00HM\x00'
2025-06-10 00:18:47,133 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SceneCaptureType (41990) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:18:47,134 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ImageUniqueID (42016) - type: string (2) Tag Location: 540 - Data Location: 668 - value: b'G08LLMJ00HM\x00'
2025-06-10 00:18:47,135 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ImageUniqueID (42016) - type: string (2) Tag Location: 547 - Data Location: 673 - value: b'G08LLMJ00HM\x00'
2025-06-10 00:18:47,141 | INFO     | PhotoRenamer.ImageLoader | on_thumbnail_ready:110 | Caricamento completato: 6 immagini elaborate
2025-06-10 00:18:47,142 | DEBUG    | PhotoRenamer.LeftPanel | _on_loading_finished:680 | [LP._on_loading_finished] Thumbnail per ph_1.jpg: tipo <class 'PySide6.QtGui.QPixmap'>, isNull: False, size: PySide6.QtCore.QSize(100, 60)
2025-06-10 00:18:47,142 | DEBUG    | PhotoRenamer.LeftPanel | _on_loading_finished:683 | [LP._on_loading_finished] Icona impostata per ph_1.jpg: icon.isNull: False, icon.availableSizes: [PySide6.QtCore.QSize(100, 60)]
2025-06-10 00:18:47,143 | DEBUG    | PhotoRenamer.LeftPanel | _on_loading_finished:680 | [LP._on_loading_finished] Thumbnail per ph_2.jpg: tipo <class 'PySide6.QtGui.QPixmap'>, isNull: False, size: PySide6.QtCore.QSize(100, 60)
2025-06-10 00:18:47,143 | DEBUG    | PhotoRenamer.LeftPanel | _on_loading_finished:683 | [LP._on_loading_finished] Icona impostata per ph_2.jpg: icon.isNull: False, icon.availableSizes: [PySide6.QtCore.QSize(100, 60)]
2025-06-10 00:18:47,143 | DEBUG    | PhotoRenamer.LeftPanel | _on_loading_finished:680 | [LP._on_loading_finished] Thumbnail per ph_3.jpg: tipo <class 'PySide6.QtGui.QPixmap'>, isNull: False, size: PySide6.QtCore.QSize(100, 58)
2025-06-10 00:18:47,143 | DEBUG    | PhotoRenamer.LeftPanel | _on_loading_finished:683 | [LP._on_loading_finished] Icona impostata per ph_3.jpg: icon.isNull: False, icon.availableSizes: [PySide6.QtCore.QSize(100, 58)]
2025-06-10 00:18:47,144 | DEBUG    | PhotoRenamer.LeftPanel | _on_loading_finished:680 | [LP._on_loading_finished] Thumbnail per ph_4.jpg: tipo <class 'PySide6.QtGui.QPixmap'>, isNull: False, size: PySide6.QtCore.QSize(100, 60)
2025-06-10 00:18:47,144 | DEBUG    | PhotoRenamer.LeftPanel | _on_loading_finished:683 | [LP._on_loading_finished] Icona impostata per ph_4.jpg: icon.isNull: False, icon.availableSizes: [PySide6.QtCore.QSize(100, 60)]
2025-06-10 00:18:47,144 | DEBUG    | PhotoRenamer.LeftPanel | _on_loading_finished:680 | [LP._on_loading_finished] Thumbnail per ph_5.jpg: tipo <class 'PySide6.QtGui.QPixmap'>, isNull: False, size: PySide6.QtCore.QSize(100, 60)
2025-06-10 00:18:47,144 | DEBUG    | PhotoRenamer.LeftPanel | _on_loading_finished:683 | [LP._on_loading_finished] Icona impostata per ph_5.jpg: icon.isNull: False, icon.availableSizes: [PySide6.QtCore.QSize(100, 60)]
2025-06-10 00:18:47,144 | DEBUG    | PhotoRenamer.LeftPanel | _on_loading_finished:680 | [LP._on_loading_finished] Thumbnail per ph_6.jpg: tipo <class 'PySide6.QtGui.QPixmap'>, isNull: False, size: PySide6.QtCore.QSize(100, 60)
2025-06-10 00:18:47,144 | DEBUG    | PhotoRenamer.LeftPanel | _on_loading_finished:683 | [LP._on_loading_finished] Icona impostata per ph_6.jpg: icon.isNull: False, icon.availableSizes: [PySide6.QtCore.QSize(100, 60)]
2025-06-10 00:18:47,144 | INFO     | PhotoDrop.ImageListController | set_image_list:41 | ImageListController: lista immagini aggiornata (6)
2025-06-10 00:18:47,145 | INFO     | PhotoDrop.RightPanel | set_image_list:291 | RightPanel: impostata lista di 6 immagini
2025-06-10 00:18:47,145 | INFO     | PhotoDrop.MainController | on_images_loaded:51 | Caricate 6 immagini
2025-06-10 00:18:47,145 | INFO     | PhotoRenamer.LeftPanel | _on_loading_finished:697 | Caricamento immagini completato e lista ordinata: 6 immagini
2025-06-10 00:18:48,821 | DEBUG    | PhotoRenamer.LeftPanel | _on_current_item_changed:1121 | [LP._on_current_item_changed] Chiamato con current: ph_2.jpg, previous: None
2025-06-10 00:18:48,821 | DEBUG    | PhotoRenamer.LeftPanel | _on_current_item_changed:1126 | [LP._on_current_item_changed] Emitting selection_changed with path: C:/Users/<USER>/Desktop/foto2\ph_2.jpg
2025-06-10 00:18:48,832 | INFO     | PhotoDrop.MetadataController | load_metadata:35 | Estrazione metadati EXIF per: C:\Users\<USER>\Desktop\foto2\ph_2.jpg
2025-06-10 00:18:48,832 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: YCbCrPositioning (531) - type: short (3) - value: b'\x01\x00'
2025-06-10 00:18:48,833 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DateTime (306) - type: string (2) Tag Location: 34 - Data Location: 138 - value: b'2025:03:08 15:53:05\x00'
2025-06-10 00:18:48,833 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Make (271) - type: string (2) Tag Location: 46 - Data Location: 158 - value: b'samsung\x00'
2025-06-10 00:18:48,833 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Model (272) - type: string (2) Tag Location: 58 - Data Location: 166 - value: b'SM-P615\x00'
2025-06-10 00:18:48,833 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Orientation (274) - type: short (3) - value: b'\x01\x00'
2025-06-10 00:18:48,833 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: XResolution (282) - type: rational (5) Tag Location: 82 - Data Location: 174 - value: b'H\x00\x00\x00\x01\x00\x00\x00'
2025-06-10 00:18:48,833 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: YResolution (283) - type: rational (5) Tag Location: 94 - Data Location: 182 - value: b'H\x00\x00\x00\x01\x00\x00\x00'
2025-06-10 00:18:48,834 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ResolutionUnit (296) - type: short (3) - value: b'\x02\x00'
2025-06-10 00:18:48,834 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Software (305) - type: string (2) Tag Location: 118 - Data Location: 190 - value: b'ImageMeter\x00'
2025-06-10 00:18:48,834 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExifIFD (34665) - type: long (4) - value: b'\xca\x00\x00\x00'
2025-06-10 00:18:48,834 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureTime (33434) - type: rational (5) Tag Location: 216 - Data Location: 548 - value: b'\x01\x00\x00\x00\x19\x00\x00\x00'
2025-06-10 00:18:48,834 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: FNumber (33437) - type: rational (5) Tag Location: 228 - Data Location: 556 - value: b'\xbe\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:18:48,834 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureProgram (34850) - type: short (3) - value: b'\x02\x00'
2025-06-10 00:18:48,835 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ISOSpeedRatings (34855) - type: short (3) - value: b'\xfa\x00'
2025-06-10 00:18:48,835 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExifVersion (36864) - type: undefined (7) - value: b'0220'
2025-06-10 00:18:48,835 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DateTimeOriginal (36867) - type: string (2) Tag Location: 276 - Data Location: 564 - value: b'2025:03:08 15:53:05\x00'
2025-06-10 00:18:48,835 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DateTimeDigitized (36868) - type: string (2) Tag Location: 288 - Data Location: 584 - value: b'2025:03:08 15:53:05\x00'
2025-06-10 00:18:48,835 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: unknown (36880) - type: string (2) Tag Location: 300 - Data Location: 604 - value: b'+01:00\x00'
2025-06-10 00:18:48,835 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: unknown (36881) - type: string (2) Tag Location: 312 - Data Location: 612 - value: b'+01:00\x00'
2025-06-10 00:18:48,835 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ShutterSpeedValue (37377) - type: rational (5) Tag Location: 324 - Data Location: 620 - value: b'\x01\x00\x00\x00\x19\x00\x00\x00'
2025-06-10 00:18:48,835 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ApertureValue (37378) - type: rational (5) Tag Location: 336 - Data Location: 628 - value: b'\xb9\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:18:48,835 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureBiasValue (37380) - type: signed rational (10) Tag Location: 348 - Data Location: 636 - value: b'\x00\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:18:48,835 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: MaxApertureValue (37381) - type: rational (5) Tag Location: 360 - Data Location: 644 - value: b'\xb9\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:18:48,836 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: MeteringMode (37383) - type: short (3) - value: b'\x02\x00'
2025-06-10 00:18:48,836 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Flash (37385) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:18:48,836 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: FocalLength (37386) - type: rational (5) Tag Location: 396 - Data Location: 652 - value: b'#\x01\x00\x00d\x00\x00\x00'
2025-06-10 00:18:48,836 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SubSec (37520) - type: string (2) - value: b'593\x00'
2025-06-10 00:18:48,836 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SubSecTimeOriginal (37521) - type: string (2) - value: b'593\x00'
2025-06-10 00:18:48,836 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SubsecTimeDigitized (37522) - type: string (2) - value: b'593\x00'
2025-06-10 00:18:48,836 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ColorSpace (40961) - type: short (3) - value: b'\x01\x00'
2025-06-10 00:18:48,836 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: PixelXDimension (40962) - type: long (4) - value: b'\xc0\x0c\x00\x00'
2025-06-10 00:18:48,836 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: PixelYDimension (40963) - type: long (4) - value: b'\xa4\x07\x00\x00'
2025-06-10 00:18:48,836 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureMode (41986) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:18:48,836 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: WhiteBalance (41987) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:18:48,836 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DigitalZoomRatio (41988) - type: rational (5) Tag Location: 504 - Data Location: 660 - value: b'd\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:18:48,837 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: FocalLengthIn35mmFilm (41989) - type: short (3) - value: b'\x1b\x00'
2025-06-10 00:18:48,837 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SceneCaptureType (41990) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:18:48,837 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ImageUniqueID (42016) - type: string (2) Tag Location: 540 - Data Location: 668 - value: b'G08LLMJ00HM\x00'
2025-06-10 00:18:48,837 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureTime (33434) - type: rational (5) Tag Location: 216 - Data Location: 548 - value: b'\x01\x00\x00\x00\x19\x00\x00\x00'
2025-06-10 00:18:48,837 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: FNumber (33437) - type: rational (5) Tag Location: 228 - Data Location: 556 - value: b'\xbe\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:18:48,837 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureProgram (34850) - type: short (3) - value: b'\x02\x00'
2025-06-10 00:18:48,837 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ISOSpeedRatings (34855) - type: short (3) - value: b'\xfa\x00'
2025-06-10 00:18:48,837 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExifVersion (36864) - type: undefined (7) - value: b'0220'
2025-06-10 00:18:48,837 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DateTimeOriginal (36867) - type: string (2) Tag Location: 276 - Data Location: 564 - value: b'2025:03:08 15:53:05\x00'
2025-06-10 00:18:48,837 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DateTimeDigitized (36868) - type: string (2) Tag Location: 288 - Data Location: 584 - value: b'2025:03:08 15:53:05\x00'
2025-06-10 00:18:48,838 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: unknown (36880) - type: string (2) Tag Location: 300 - Data Location: 604 - value: b'+01:00\x00'
2025-06-10 00:18:48,838 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: unknown (36881) - type: string (2) Tag Location: 312 - Data Location: 612 - value: b'+01:00\x00'
2025-06-10 00:18:48,838 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ShutterSpeedValue (37377) - type: rational (5) Tag Location: 324 - Data Location: 620 - value: b'\x01\x00\x00\x00\x19\x00\x00\x00'
2025-06-10 00:18:48,838 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ApertureValue (37378) - type: rational (5) Tag Location: 336 - Data Location: 628 - value: b'\xb9\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:18:48,838 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureBiasValue (37380) - type: signed rational (10) Tag Location: 348 - Data Location: 636 - value: b'\x00\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:18:48,838 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: MaxApertureValue (37381) - type: rational (5) Tag Location: 360 - Data Location: 644 - value: b'\xb9\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:18:48,838 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: MeteringMode (37383) - type: short (3) - value: b'\x02\x00'
2025-06-10 00:18:48,838 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Flash (37385) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:18:48,839 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: FocalLength (37386) - type: rational (5) Tag Location: 396 - Data Location: 652 - value: b'#\x01\x00\x00d\x00\x00\x00'
2025-06-10 00:18:48,839 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SubSec (37520) - type: string (2) - value: b'593\x00'
2025-06-10 00:18:48,839 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SubSecTimeOriginal (37521) - type: string (2) - value: b'593\x00'
2025-06-10 00:18:48,839 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SubsecTimeDigitized (37522) - type: string (2) - value: b'593\x00'
2025-06-10 00:18:48,839 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ColorSpace (40961) - type: short (3) - value: b'\x01\x00'
2025-06-10 00:18:48,839 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: PixelXDimension (40962) - type: long (4) - value: b'\xc0\x0c\x00\x00'
2025-06-10 00:18:48,839 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: PixelYDimension (40963) - type: long (4) - value: b'\xa4\x07\x00\x00'
2025-06-10 00:18:48,839 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureMode (41986) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:18:48,839 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: WhiteBalance (41987) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:18:48,839 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DigitalZoomRatio (41988) - type: rational (5) Tag Location: 504 - Data Location: 660 - value: b'd\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:18:48,839 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: FocalLengthIn35mmFilm (41989) - type: short (3) - value: b'\x1b\x00'
2025-06-10 00:18:48,839 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SceneCaptureType (41990) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:18:48,839 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ImageUniqueID (42016) - type: string (2) Tag Location: 540 - Data Location: 668 - value: b'G08LLMJ00HM\x00'
2025-06-10 00:18:48,846 | DEBUG    | PhotoDrop.MetadataController | load_metadata:42 | Metadati EXIF estratti: {'Marca': 'samsung', 'Modello': 'SM-P615', 'Data e ora': '08/03/2025 15:53:05', 'Compensazione esposizione': '0.0', 'Modalità di misurazione': '2', 'Flash': '0', 'Lunghezza focale': '2.91', 'Tempo di esposizione': '0.04 sec', 'Diaframma': 'f/1.9', 'ISO': '250', 'Bilanciamento del bianco': '0', 'Dimensione file': '168.1 KB', 'Nome file': 'ph_2.jpg'}
2025-06-10 00:18:48,846 | INFO     | PhotoDrop.MainWindow | _on_selection_changed:257 | MainWindow: _on_selection_changed ha chiamato right_panel.show_image con ph_2.jpg
2025-06-10 00:18:48,898 | CRITICAL | PhotoRenamer.LeftPanel | startDrag:261 | CUSTOM STARTDRAG CALLED!
2025-06-10 00:18:48,903 | DEBUG    | PhotoRenamer.LeftPanel | startDrag:284 | [DND.startDrag] Pixmap for ph_2.jpg requested with available_sizes[0] (PySide6.QtCore.QSize(100, 60)).isNull: False, Size: PySide6.QtCore.QSize(100, 60)
2025-06-10 00:18:48,904 | DEBUG    | PhotoRenamer.LeftPanel | startDrag:302 | [DND.startDrag] Pixmap for ph_2.jpg successfully created. Size: PySide6.QtCore.QSize(100, 60)
2025-06-10 00:18:48,904 | INFO     | PhotoRenamer.LeftPanel | startDrag:307 | [DND.startDrag] Avvio drag per ph_2.jpg con pixmap size PySide6.QtCore.QSize(100, 60)
2025-06-10 00:18:48,917 | DEBUG    | PhotoRenamer.LeftPanel | dragMoveEvent:87 | [DND] Drag move: posizione indicatore 2
2025-06-10 00:18:48,929 | DEBUG    | PhotoRenamer.LeftPanel | dragMoveEvent:87 | [DND] Drag move: posizione indicatore 2
2025-06-10 00:18:48,936 | DEBUG    | PhotoRenamer.LeftPanel | dragMoveEvent:87 | [DND] Drag move: posizione indicatore 2
2025-06-10 00:18:48,943 | DEBUG    | PhotoRenamer.LeftPanel | dragMoveEvent:87 | [DND] Drag move: posizione indicatore 2
2025-06-10 00:18:48,950 | DEBUG    | PhotoRenamer.LeftPanel | dragMoveEvent:87 | [DND] Drag move: posizione indicatore 2
2025-06-10 00:18:48,958 | DEBUG    | PhotoRenamer.LeftPanel | dragMoveEvent:87 | [DND] Drag move: posizione indicatore 2
2025-06-10 00:18:48,966 | DEBUG    | PhotoRenamer.LeftPanel | dragMoveEvent:87 | [DND] Drag move: posizione indicatore 2
2025-06-10 00:18:48,973 | DEBUG    | PhotoRenamer.LeftPanel | dragMoveEvent:87 | [DND] Drag move: posizione indicatore 2
2025-06-10 00:18:48,981 | DEBUG    | PhotoRenamer.LeftPanel | dragMoveEvent:87 | [DND] Drag move: posizione indicatore 6
2025-06-10 00:18:48,988 | DEBUG    | PhotoRenamer.LeftPanel | dragMoveEvent:87 | [DND] Drag move: posizione indicatore 6
2025-06-10 00:18:48,996 | DEBUG    | PhotoRenamer.LeftPanel | dragMoveEvent:87 | [DND] Drag move: posizione indicatore 6
2025-06-10 00:18:49,003 | DEBUG    | PhotoRenamer.LeftPanel | dragMoveEvent:87 | [DND] Drag move: posizione indicatore 6
2025-06-10 00:18:49,011 | DEBUG    | PhotoRenamer.LeftPanel | dragMoveEvent:87 | [DND] Drag move: posizione indicatore 6
2025-06-10 00:18:49,019 | DEBUG    | PhotoRenamer.LeftPanel | dragMoveEvent:87 | [DND] Drag move: posizione indicatore 6
2025-06-10 00:18:49,026 | DEBUG    | PhotoRenamer.LeftPanel | dragMoveEvent:87 | [DND] Drag move: posizione indicatore 6
2025-06-10 00:18:49,034 | DEBUG    | PhotoRenamer.LeftPanel | dragMoveEvent:87 | [DND] Drag move: posizione indicatore 6
2025-06-10 00:18:49,042 | DEBUG    | PhotoRenamer.LeftPanel | dragMoveEvent:87 | [DND] Drag move: posizione indicatore 6
2025-06-10 00:18:49,048 | DEBUG    | PhotoRenamer.LeftPanel | dragMoveEvent:87 | [DND] Drag move: posizione indicatore 6
2025-06-10 00:18:49,056 | DEBUG    | PhotoRenamer.LeftPanel | dragMoveEvent:87 | [DND] Drag move: posizione indicatore 6
2025-06-10 00:18:49,063 | DEBUG    | PhotoRenamer.LeftPanel | dragMoveEvent:87 | [DND] Drag move: posizione indicatore 6
2025-06-10 00:18:49,071 | DEBUG    | PhotoRenamer.LeftPanel | dragMoveEvent:87 | [DND] Drag move: posizione indicatore 6
2025-06-10 00:18:49,078 | DEBUG    | PhotoRenamer.LeftPanel | dragMoveEvent:87 | [DND] Drag move: posizione indicatore 6
2025-06-10 00:18:49,085 | DEBUG    | PhotoRenamer.LeftPanel | dragMoveEvent:87 | [DND] Drag move: posizione indicatore 6
2025-06-10 00:18:49,093 | DEBUG    | PhotoRenamer.LeftPanel | dragMoveEvent:87 | [DND] Drag move: posizione indicatore 6
2025-06-10 00:18:49,100 | DEBUG    | PhotoRenamer.LeftPanel | dragMoveEvent:87 | [DND] Drag move: posizione indicatore 6
2025-06-10 00:18:49,108 | DEBUG    | PhotoRenamer.LeftPanel | dragMoveEvent:87 | [DND] Drag move: posizione indicatore 6
2025-06-10 00:18:49,115 | DEBUG    | PhotoRenamer.LeftPanel | dragMoveEvent:87 | [DND] Drag move: posizione indicatore 6
2025-06-10 00:18:49,123 | DEBUG    | PhotoRenamer.LeftPanel | dragMoveEvent:87 | [DND] Drag move: posizione indicatore 6
2025-06-10 00:18:49,131 | DEBUG    | PhotoRenamer.LeftPanel | dragMoveEvent:87 | [DND] Drag move: posizione indicatore 6
2025-06-10 00:18:49,138 | DEBUG    | PhotoRenamer.LeftPanel | dragMoveEvent:87 | [DND] Drag move: posizione indicatore 6
2025-06-10 00:18:49,146 | DEBUG    | PhotoRenamer.LeftPanel | dragMoveEvent:87 | [DND] Drag move: posizione indicatore 6
2025-06-10 00:18:49,153 | DEBUG    | PhotoRenamer.LeftPanel | dragMoveEvent:87 | [DND] Drag move: posizione indicatore 6
2025-06-10 00:18:49,169 | DEBUG    | PhotoRenamer.LeftPanel | dragMoveEvent:87 | [DND] Drag move: posizione indicatore 6
2025-06-10 00:18:49,342 | DEBUG    | PhotoRenamer.LeftPanel | dragMoveEvent:87 | [DND] Drag move: posizione indicatore 6
2025-06-10 00:18:49,357 | DEBUG    | PhotoRenamer.LeftPanel | dragMoveEvent:87 | [DND] Drag move: posizione indicatore 4
2025-06-10 00:18:49,364 | DEBUG    | PhotoRenamer.LeftPanel | dragMoveEvent:87 | [DND] Drag move: posizione indicatore 4
2025-06-10 00:18:49,371 | DEBUG    | PhotoRenamer.LeftPanel | dragMoveEvent:87 | [DND] Drag move: posizione indicatore 4
2025-06-10 00:18:49,378 | DEBUG    | PhotoRenamer.LeftPanel | dragMoveEvent:87 | [DND] Drag move: posizione indicatore 4
2025-06-10 00:18:49,394 | DEBUG    | PhotoRenamer.LeftPanel | dragMoveEvent:87 | [DND] Drag move: posizione indicatore 4
2025-06-10 00:18:49,477 | DEBUG    | PhotoRenamer.LeftPanel | dragMoveEvent:87 | [DND] Drag move: posizione indicatore 4
2025-06-10 00:18:49,483 | DEBUG    | PhotoRenamer.LeftPanel | dragMoveEvent:87 | [DND] Drag move: posizione indicatore 4
2025-06-10 00:18:49,491 | DEBUG    | PhotoRenamer.LeftPanel | dragMoveEvent:87 | [DND] Drag move: posizione indicatore 4
2025-06-10 00:18:49,498 | DEBUG    | PhotoRenamer.LeftPanel | dragMoveEvent:87 | [DND] Drag move: posizione indicatore 4
2025-06-10 00:18:49,806 | DEBUG    | PhotoRenamer.LeftPanel | dragMoveEvent:87 | [DND] Drag move: posizione indicatore 4
2025-06-10 00:18:49,814 | DEBUG    | PhotoRenamer.LeftPanel | dragMoveEvent:87 | [DND] Drag move: posizione indicatore 4
2025-06-10 00:18:49,836 | DEBUG    | PhotoRenamer.LeftPanel | dragMoveEvent:87 | [DND] Drag move: posizione indicatore 4
2025-06-10 00:18:49,838 | DEBUG    | PhotoRenamer.LeftPanel | dragMoveEvent:87 | [DND] Drag move: posizione indicatore 4
2025-06-10 00:18:49,843 | DEBUG    | PhotoRenamer.LeftPanel | dragMoveEvent:87 | [DND] Drag move: posizione indicatore 4
2025-06-10 00:18:49,851 | DEBUG    | PhotoRenamer.LeftPanel | dragMoveEvent:87 | [DND] Drag move: posizione indicatore 4
2025-06-10 00:18:49,859 | DEBUG    | PhotoRenamer.LeftPanel | dragMoveEvent:87 | [DND] Drag move: posizione indicatore 4
2025-06-10 00:18:49,866 | DEBUG    | PhotoRenamer.LeftPanel | dragMoveEvent:87 | [DND] Drag move: posizione indicatore 4
2025-06-10 00:18:49,873 | DEBUG    | PhotoRenamer.LeftPanel | dragMoveEvent:87 | [DND] Drag move: posizione indicatore 4
2025-06-10 00:18:49,881 | DEBUG    | PhotoRenamer.LeftPanel | dragMoveEvent:87 | [DND] Drag move: posizione indicatore 4
2025-06-10 00:18:49,888 | DEBUG    | PhotoRenamer.LeftPanel | dragMoveEvent:87 | [DND] Drag move: posizione indicatore 4
2025-06-10 00:18:49,896 | DEBUG    | PhotoRenamer.LeftPanel | dragMoveEvent:87 | [DND] Drag move: posizione indicatore 4
2025-06-10 00:18:49,903 | DEBUG    | PhotoRenamer.LeftPanel | dragMoveEvent:87 | [DND] Drag move: posizione indicatore 4
2025-06-10 00:18:49,911 | DEBUG    | PhotoRenamer.LeftPanel | dragMoveEvent:87 | [DND] Drag move: posizione indicatore 4
2025-06-10 00:18:49,925 | DEBUG    | PhotoRenamer.LeftPanel | dragMoveEvent:87 | [DND] Drag move: posizione indicatore 4
2025-06-10 00:18:49,933 | DEBUG    | PhotoRenamer.LeftPanel | dragMoveEvent:87 | [DND] Drag move: posizione indicatore 4
2025-06-10 00:18:49,940 | DEBUG    | PhotoRenamer.LeftPanel | dragMoveEvent:87 | [DND] Drag move: posizione indicatore 4
2025-06-10 00:18:49,948 | DEBUG    | PhotoRenamer.LeftPanel | dragMoveEvent:87 | [DND] Drag move: posizione indicatore 4
2025-06-10 00:18:49,956 | DEBUG    | PhotoRenamer.LeftPanel | dragMoveEvent:87 | [DND] Drag move: posizione indicatore 4
2025-06-10 00:18:49,964 | DEBUG    | PhotoRenamer.LeftPanel | dragMoveEvent:87 | [DND] Drag move: posizione indicatore 4
2025-06-10 00:18:49,971 | DEBUG    | PhotoRenamer.LeftPanel | dragMoveEvent:87 | [DND] Drag move: posizione indicatore 4
2025-06-10 00:18:49,978 | DEBUG    | PhotoRenamer.LeftPanel | dragMoveEvent:87 | [DND] Drag move: posizione indicatore 4
2025-06-10 00:18:50,009 | DEBUG    | PhotoRenamer.LeftPanel | dragMoveEvent:87 | [DND] Drag move: posizione indicatore 4
2025-06-10 00:18:50,827 | INFO     | PhotoRenamer.LeftPanel | dropEvent:117 | [DND] Drop iniziato: posizione target calcolata 4
2025-06-10 00:18:50,827 | INFO     | PhotoRenamer.LeftPanel | _perform_manual_reorder:133 | [DND] Inizio riordinamento: 1 elementi verso posizione 4
2025-06-10 00:18:50,828 | INFO     | PhotoRenamer.LeftPanel | _perform_manual_reorder:134 | [DND] Numero totale elementi prima: 6
2025-06-10 00:18:50,828 | DEBUG    | PhotoRenamer.LeftPanel | _perform_manual_reorder:140 | [DND._perform_manual_reorder] Icona per ph_2.jpg: icon.isNull: False, icon.availableSizes: [PySide6.QtCore.QSize(100, 60)]
2025-06-10 00:18:50,828 | INFO     | PhotoRenamer.LeftPanel | _perform_manual_reorder:152 | [DND] Elementi da spostare: ['ph_2.jpg']
2025-06-10 00:18:50,828 | INFO     | PhotoRenamer.LeftPanel | _perform_manual_reorder:153 | [DND] Posizioni originali: [1]
2025-06-10 00:18:50,829 | DEBUG    | PhotoRenamer.LeftPanel | _on_current_item_changed:1121 | [LP._on_current_item_changed] Chiamato con current: ph_1.jpg, previous: ph_2.jpg
2025-06-10 00:18:50,829 | DEBUG    | PhotoRenamer.LeftPanel | _on_current_item_changed:1126 | [LP._on_current_item_changed] Emitting selection_changed with path: C:/Users/<USER>/Desktop/foto2\ph_1.jpg
2025-06-10 00:18:50,841 | INFO     | PhotoDrop.MetadataController | load_metadata:35 | Estrazione metadati EXIF per: C:\Users\<USER>\Desktop\foto2\ph_1.jpg
2025-06-10 00:18:50,841 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: YCbCrPositioning (531) - type: short (3) - value: b'\x01\x00'
2025-06-10 00:18:50,842 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DateTime (306) - type: string (2) Tag Location: 34 - Data Location: 138 - value: b'2025:03:08 16:05:44\x00'
2025-06-10 00:18:50,842 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Make (271) - type: string (2) Tag Location: 46 - Data Location: 158 - value: b'samsung\x00'
2025-06-10 00:18:50,842 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Model (272) - type: string (2) Tag Location: 58 - Data Location: 166 - value: b'SM-P615\x00'
2025-06-10 00:18:50,842 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Orientation (274) - type: short (3) - value: b'\x01\x00'
2025-06-10 00:18:50,842 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: XResolution (282) - type: rational (5) Tag Location: 82 - Data Location: 174 - value: b'H\x00\x00\x00\x01\x00\x00\x00'
2025-06-10 00:18:50,842 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: YResolution (283) - type: rational (5) Tag Location: 94 - Data Location: 182 - value: b'H\x00\x00\x00\x01\x00\x00\x00'
2025-06-10 00:18:50,843 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ResolutionUnit (296) - type: short (3) - value: b'\x02\x00'
2025-06-10 00:18:50,843 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Software (305) - type: string (2) Tag Location: 118 - Data Location: 190 - value: b'ImageMeter\x00'
2025-06-10 00:18:50,843 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExifIFD (34665) - type: long (4) - value: b'\xca\x00\x00\x00'
2025-06-10 00:18:50,843 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureTime (33434) - type: rational (5) Tag Location: 216 - Data Location: 560 - value: b'\x01\x00\x00\x002\x00\x00\x00'
2025-06-10 00:18:50,843 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: FNumber (33437) - type: rational (5) Tag Location: 228 - Data Location: 568 - value: b'\xbe\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:18:50,844 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureProgram (34850) - type: short (3) - value: b'\x02\x00'
2025-06-10 00:18:50,844 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ISOSpeedRatings (34855) - type: short (3) - value: b'd\x00'
2025-06-10 00:18:50,844 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExifVersion (36864) - type: undefined (7) - value: b'0220'
2025-06-10 00:18:50,844 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DateTimeOriginal (36867) - type: string (2) Tag Location: 276 - Data Location: 576 - value: b'2025:03:08 16:05:44\x00'
2025-06-10 00:18:50,844 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DateTimeDigitized (36868) - type: string (2) Tag Location: 288 - Data Location: 596 - value: b'2025:03:08 16:05:44\x00'
2025-06-10 00:18:50,844 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: unknown (36880) - type: string (2) Tag Location: 300 - Data Location: 616 - value: b'+01:00\x00'
2025-06-10 00:18:50,845 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: unknown (36881) - type: string (2) Tag Location: 312 - Data Location: 624 - value: b'+01:00\x00'
2025-06-10 00:18:50,845 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ShutterSpeedValue (37377) - type: rational (5) Tag Location: 324 - Data Location: 632 - value: b'\x01\x00\x00\x002\x00\x00\x00'
2025-06-10 00:18:50,845 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ApertureValue (37378) - type: rational (5) Tag Location: 336 - Data Location: 640 - value: b'\xb9\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:18:50,845 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: BrightnessValue (37379) - type: signed rational (10) Tag Location: 348 - Data Location: 648 - value: b'\xe7\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:18:50,845 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureBiasValue (37380) - type: signed rational (10) Tag Location: 360 - Data Location: 656 - value: b'\x00\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:18:50,845 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: MaxApertureValue (37381) - type: rational (5) Tag Location: 372 - Data Location: 664 - value: b'\xb9\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:18:50,846 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: MeteringMode (37383) - type: short (3) - value: b'\x02\x00'
2025-06-10 00:18:50,846 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Flash (37385) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:18:50,846 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: FocalLength (37386) - type: rational (5) Tag Location: 408 - Data Location: 672 - value: b'#\x01\x00\x00d\x00\x00\x00'
2025-06-10 00:18:50,846 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SubSec (37520) - type: string (2) - value: b'323\x00'
2025-06-10 00:18:50,846 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SubSecTimeOriginal (37521) - type: string (2) - value: b'323\x00'
2025-06-10 00:18:50,846 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SubsecTimeDigitized (37522) - type: string (2) - value: b'323\x00'
2025-06-10 00:18:50,847 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ColorSpace (40961) - type: short (3) - value: b'\x01\x00'
2025-06-10 00:18:50,847 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: PixelXDimension (40962) - type: long (4) - value: b'\xc0\x0c\x00\x00'
2025-06-10 00:18:50,847 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: PixelYDimension (40963) - type: long (4) - value: b'\xa4\x07\x00\x00'
2025-06-10 00:18:50,847 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureMode (41986) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:18:50,847 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: WhiteBalance (41987) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:18:50,847 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DigitalZoomRatio (41988) - type: rational (5) Tag Location: 516 - Data Location: 680 - value: b'd\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:18:50,847 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: FocalLengthIn35mmFilm (41989) - type: short (3) - value: b'\x1b\x00'
2025-06-10 00:18:50,847 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SceneCaptureType (41990) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:18:50,847 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ImageUniqueID (42016) - type: string (2) Tag Location: 552 - Data Location: 688 - value: b'G08LLMJ00HM\x00'
2025-06-10 00:18:50,848 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureTime (33434) - type: rational (5) Tag Location: 216 - Data Location: 560 - value: b'\x01\x00\x00\x002\x00\x00\x00'
2025-06-10 00:18:50,848 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: FNumber (33437) - type: rational (5) Tag Location: 228 - Data Location: 568 - value: b'\xbe\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:18:50,848 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureProgram (34850) - type: short (3) - value: b'\x02\x00'
2025-06-10 00:18:50,848 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ISOSpeedRatings (34855) - type: short (3) - value: b'd\x00'
2025-06-10 00:18:50,848 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExifVersion (36864) - type: undefined (7) - value: b'0220'
2025-06-10 00:18:50,848 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DateTimeOriginal (36867) - type: string (2) Tag Location: 276 - Data Location: 576 - value: b'2025:03:08 16:05:44\x00'
2025-06-10 00:18:50,849 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DateTimeDigitized (36868) - type: string (2) Tag Location: 288 - Data Location: 596 - value: b'2025:03:08 16:05:44\x00'
2025-06-10 00:18:50,849 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: unknown (36880) - type: string (2) Tag Location: 300 - Data Location: 616 - value: b'+01:00\x00'
2025-06-10 00:18:50,849 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: unknown (36881) - type: string (2) Tag Location: 312 - Data Location: 624 - value: b'+01:00\x00'
2025-06-10 00:18:50,849 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ShutterSpeedValue (37377) - type: rational (5) Tag Location: 324 - Data Location: 632 - value: b'\x01\x00\x00\x002\x00\x00\x00'
2025-06-10 00:18:50,849 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ApertureValue (37378) - type: rational (5) Tag Location: 336 - Data Location: 640 - value: b'\xb9\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:18:50,849 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: BrightnessValue (37379) - type: signed rational (10) Tag Location: 348 - Data Location: 648 - value: b'\xe7\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:18:50,850 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureBiasValue (37380) - type: signed rational (10) Tag Location: 360 - Data Location: 656 - value: b'\x00\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:18:50,850 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: MaxApertureValue (37381) - type: rational (5) Tag Location: 372 - Data Location: 664 - value: b'\xb9\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:18:50,850 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: MeteringMode (37383) - type: short (3) - value: b'\x02\x00'
2025-06-10 00:18:50,850 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Flash (37385) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:18:50,850 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: FocalLength (37386) - type: rational (5) Tag Location: 408 - Data Location: 672 - value: b'#\x01\x00\x00d\x00\x00\x00'
2025-06-10 00:18:50,850 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SubSec (37520) - type: string (2) - value: b'323\x00'
2025-06-10 00:18:50,850 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SubSecTimeOriginal (37521) - type: string (2) - value: b'323\x00'
2025-06-10 00:18:50,850 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SubsecTimeDigitized (37522) - type: string (2) - value: b'323\x00'
2025-06-10 00:18:50,850 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ColorSpace (40961) - type: short (3) - value: b'\x01\x00'
2025-06-10 00:18:50,851 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: PixelXDimension (40962) - type: long (4) - value: b'\xc0\x0c\x00\x00'
2025-06-10 00:18:50,851 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: PixelYDimension (40963) - type: long (4) - value: b'\xa4\x07\x00\x00'
2025-06-10 00:18:50,851 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureMode (41986) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:18:50,851 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: WhiteBalance (41987) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:18:50,851 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DigitalZoomRatio (41988) - type: rational (5) Tag Location: 516 - Data Location: 680 - value: b'd\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:18:50,851 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: FocalLengthIn35mmFilm (41989) - type: short (3) - value: b'\x1b\x00'
2025-06-10 00:18:50,851 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SceneCaptureType (41990) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:18:50,851 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ImageUniqueID (42016) - type: string (2) Tag Location: 552 - Data Location: 688 - value: b'G08LLMJ00HM\x00'
2025-06-10 00:18:50,855 | DEBUG    | PhotoDrop.MetadataController | load_metadata:42 | Metadati EXIF estratti: {'Marca': 'samsung', 'Modello': 'SM-P615', 'Data e ora': '08/03/2025 16:05:44', 'Compensazione esposizione': '0.0', 'Modalità di misurazione': '2', 'Flash': '0', 'Lunghezza focale': '2.91', 'Tempo di esposizione': '0.02 sec', 'Diaframma': 'f/1.9', 'ISO': '100', 'Bilanciamento del bianco': '0', 'Dimensione file': '193.3 KB', 'Nome file': 'ph_1.jpg'}
2025-06-10 00:18:50,856 | INFO     | PhotoDrop.MainWindow | _on_selection_changed:257 | MainWindow: _on_selection_changed ha chiamato right_panel.show_image con ph_1.jpg
2025-06-10 00:18:50,856 | INFO     | PhotoRenamer.LeftPanel | _perform_manual_reorder:160 | [DND] Rimosso elemento "ph_2.jpg" dalla posizione 1
2025-06-10 00:18:50,856 | INFO     | PhotoRenamer.LeftPanel | _perform_manual_reorder:162 | [DND] Numero elementi dopo rimozione: 5
2025-06-10 00:18:50,856 | DEBUG    | PhotoRenamer.LeftPanel | _perform_manual_reorder:186 | [DND] Reimpostata icona originale per ph_2.jpg. isNull: False, availableSizes: [PySide6.QtCore.QSize(100, 60)]
2025-06-10 00:18:50,857 | INFO     | PhotoRenamer.LeftPanel | _perform_manual_reorder:197 | [DND] Reinserito elemento "ph_2.jpg" alla posizione 4
2025-06-10 00:18:50,857 | INFO     | PhotoRenamer.LeftPanel | _perform_manual_reorder:199 | [DND] Numero elementi dopo inserimento: 6
2025-06-10 00:18:50,857 | INFO     | PhotoRenamer.LeftPanel | _perform_manual_reorder:206 | [DND] Selezionato elemento "ph_2.jpg" alla posizione 4
2025-06-10 00:18:50,857 | DEBUG    | PhotoRenamer.LeftPanel | _on_current_item_changed:1121 | [LP._on_current_item_changed] Chiamato con current: ph_2.jpg, previous: ph_1.jpg
2025-06-10 00:18:50,857 | DEBUG    | PhotoRenamer.LeftPanel | _on_current_item_changed:1126 | [LP._on_current_item_changed] Emitting selection_changed with path: C:/Users/<USER>/Desktop/foto2\ph_2.jpg
2025-06-10 00:18:50,865 | INFO     | PhotoDrop.MetadataController | load_metadata:35 | Estrazione metadati EXIF per: C:\Users\<USER>\Desktop\foto2\ph_2.jpg
2025-06-10 00:18:50,866 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: YCbCrPositioning (531) - type: short (3) - value: b'\x01\x00'
2025-06-10 00:18:50,866 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DateTime (306) - type: string (2) Tag Location: 34 - Data Location: 138 - value: b'2025:03:08 15:53:05\x00'
2025-06-10 00:18:50,866 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Make (271) - type: string (2) Tag Location: 46 - Data Location: 158 - value: b'samsung\x00'
2025-06-10 00:18:50,866 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Model (272) - type: string (2) Tag Location: 58 - Data Location: 166 - value: b'SM-P615\x00'
2025-06-10 00:18:50,866 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Orientation (274) - type: short (3) - value: b'\x01\x00'
2025-06-10 00:18:50,866 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: XResolution (282) - type: rational (5) Tag Location: 82 - Data Location: 174 - value: b'H\x00\x00\x00\x01\x00\x00\x00'
2025-06-10 00:18:50,866 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: YResolution (283) - type: rational (5) Tag Location: 94 - Data Location: 182 - value: b'H\x00\x00\x00\x01\x00\x00\x00'
2025-06-10 00:18:50,866 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ResolutionUnit (296) - type: short (3) - value: b'\x02\x00'
2025-06-10 00:18:50,866 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Software (305) - type: string (2) Tag Location: 118 - Data Location: 190 - value: b'ImageMeter\x00'
2025-06-10 00:18:50,866 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExifIFD (34665) - type: long (4) - value: b'\xca\x00\x00\x00'
2025-06-10 00:18:50,867 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureTime (33434) - type: rational (5) Tag Location: 216 - Data Location: 548 - value: b'\x01\x00\x00\x00\x19\x00\x00\x00'
2025-06-10 00:18:50,867 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: FNumber (33437) - type: rational (5) Tag Location: 228 - Data Location: 556 - value: b'\xbe\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:18:50,867 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureProgram (34850) - type: short (3) - value: b'\x02\x00'
2025-06-10 00:18:50,867 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ISOSpeedRatings (34855) - type: short (3) - value: b'\xfa\x00'
2025-06-10 00:18:50,867 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExifVersion (36864) - type: undefined (7) - value: b'0220'
2025-06-10 00:18:50,867 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DateTimeOriginal (36867) - type: string (2) Tag Location: 276 - Data Location: 564 - value: b'2025:03:08 15:53:05\x00'
2025-06-10 00:18:50,867 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DateTimeDigitized (36868) - type: string (2) Tag Location: 288 - Data Location: 584 - value: b'2025:03:08 15:53:05\x00'
2025-06-10 00:18:50,868 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: unknown (36880) - type: string (2) Tag Location: 300 - Data Location: 604 - value: b'+01:00\x00'
2025-06-10 00:18:50,868 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: unknown (36881) - type: string (2) Tag Location: 312 - Data Location: 612 - value: b'+01:00\x00'
2025-06-10 00:18:50,868 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ShutterSpeedValue (37377) - type: rational (5) Tag Location: 324 - Data Location: 620 - value: b'\x01\x00\x00\x00\x19\x00\x00\x00'
2025-06-10 00:18:50,868 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ApertureValue (37378) - type: rational (5) Tag Location: 336 - Data Location: 628 - value: b'\xb9\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:18:50,868 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureBiasValue (37380) - type: signed rational (10) Tag Location: 348 - Data Location: 636 - value: b'\x00\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:18:50,868 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: MaxApertureValue (37381) - type: rational (5) Tag Location: 360 - Data Location: 644 - value: b'\xb9\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:18:50,868 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: MeteringMode (37383) - type: short (3) - value: b'\x02\x00'
2025-06-10 00:18:50,868 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Flash (37385) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:18:50,869 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: FocalLength (37386) - type: rational (5) Tag Location: 396 - Data Location: 652 - value: b'#\x01\x00\x00d\x00\x00\x00'
2025-06-10 00:18:50,869 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SubSec (37520) - type: string (2) - value: b'593\x00'
2025-06-10 00:18:50,869 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SubSecTimeOriginal (37521) - type: string (2) - value: b'593\x00'
2025-06-10 00:18:50,869 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SubsecTimeDigitized (37522) - type: string (2) - value: b'593\x00'
2025-06-10 00:18:50,869 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ColorSpace (40961) - type: short (3) - value: b'\x01\x00'
2025-06-10 00:18:50,869 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: PixelXDimension (40962) - type: long (4) - value: b'\xc0\x0c\x00\x00'
2025-06-10 00:18:50,869 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: PixelYDimension (40963) - type: long (4) - value: b'\xa4\x07\x00\x00'
2025-06-10 00:18:50,869 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureMode (41986) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:18:50,869 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: WhiteBalance (41987) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:18:50,869 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DigitalZoomRatio (41988) - type: rational (5) Tag Location: 504 - Data Location: 660 - value: b'd\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:18:50,869 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: FocalLengthIn35mmFilm (41989) - type: short (3) - value: b'\x1b\x00'
2025-06-10 00:18:50,869 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SceneCaptureType (41990) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:18:50,869 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ImageUniqueID (42016) - type: string (2) Tag Location: 540 - Data Location: 668 - value: b'G08LLMJ00HM\x00'
2025-06-10 00:18:50,870 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureTime (33434) - type: rational (5) Tag Location: 216 - Data Location: 548 - value: b'\x01\x00\x00\x00\x19\x00\x00\x00'
2025-06-10 00:18:50,870 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: FNumber (33437) - type: rational (5) Tag Location: 228 - Data Location: 556 - value: b'\xbe\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:18:50,870 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureProgram (34850) - type: short (3) - value: b'\x02\x00'
2025-06-10 00:18:50,870 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ISOSpeedRatings (34855) - type: short (3) - value: b'\xfa\x00'
2025-06-10 00:18:50,870 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExifVersion (36864) - type: undefined (7) - value: b'0220'
2025-06-10 00:18:50,870 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DateTimeOriginal (36867) - type: string (2) Tag Location: 276 - Data Location: 564 - value: b'2025:03:08 15:53:05\x00'
2025-06-10 00:18:50,870 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DateTimeDigitized (36868) - type: string (2) Tag Location: 288 - Data Location: 584 - value: b'2025:03:08 15:53:05\x00'
2025-06-10 00:18:50,870 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: unknown (36880) - type: string (2) Tag Location: 300 - Data Location: 604 - value: b'+01:00\x00'
2025-06-10 00:18:50,870 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: unknown (36881) - type: string (2) Tag Location: 312 - Data Location: 612 - value: b'+01:00\x00'
2025-06-10 00:18:50,871 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ShutterSpeedValue (37377) - type: rational (5) Tag Location: 324 - Data Location: 620 - value: b'\x01\x00\x00\x00\x19\x00\x00\x00'
2025-06-10 00:18:50,871 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ApertureValue (37378) - type: rational (5) Tag Location: 336 - Data Location: 628 - value: b'\xb9\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:18:50,871 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureBiasValue (37380) - type: signed rational (10) Tag Location: 348 - Data Location: 636 - value: b'\x00\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:18:50,871 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: MaxApertureValue (37381) - type: rational (5) Tag Location: 360 - Data Location: 644 - value: b'\xb9\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:18:50,871 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: MeteringMode (37383) - type: short (3) - value: b'\x02\x00'
2025-06-10 00:18:50,871 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Flash (37385) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:18:50,871 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: FocalLength (37386) - type: rational (5) Tag Location: 396 - Data Location: 652 - value: b'#\x01\x00\x00d\x00\x00\x00'
2025-06-10 00:18:50,871 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SubSec (37520) - type: string (2) - value: b'593\x00'
2025-06-10 00:18:50,871 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SubSecTimeOriginal (37521) - type: string (2) - value: b'593\x00'
2025-06-10 00:18:50,871 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SubsecTimeDigitized (37522) - type: string (2) - value: b'593\x00'
2025-06-10 00:18:50,872 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ColorSpace (40961) - type: short (3) - value: b'\x01\x00'
2025-06-10 00:18:50,872 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: PixelXDimension (40962) - type: long (4) - value: b'\xc0\x0c\x00\x00'
2025-06-10 00:18:50,872 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: PixelYDimension (40963) - type: long (4) - value: b'\xa4\x07\x00\x00'
2025-06-10 00:18:50,872 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureMode (41986) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:18:50,872 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: WhiteBalance (41987) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:18:50,872 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DigitalZoomRatio (41988) - type: rational (5) Tag Location: 504 - Data Location: 660 - value: b'd\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:18:50,872 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: FocalLengthIn35mmFilm (41989) - type: short (3) - value: b'\x1b\x00'
2025-06-10 00:18:50,872 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SceneCaptureType (41990) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:18:50,872 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ImageUniqueID (42016) - type: string (2) Tag Location: 540 - Data Location: 668 - value: b'G08LLMJ00HM\x00'
2025-06-10 00:18:50,876 | DEBUG    | PhotoDrop.MetadataController | load_metadata:42 | Metadati EXIF estratti: {'Marca': 'samsung', 'Modello': 'SM-P615', 'Data e ora': '08/03/2025 15:53:05', 'Compensazione esposizione': '0.0', 'Modalità di misurazione': '2', 'Flash': '0', 'Lunghezza focale': '2.91', 'Tempo di esposizione': '0.04 sec', 'Diaframma': 'f/1.9', 'ISO': '250', 'Bilanciamento del bianco': '0', 'Dimensione file': '168.1 KB', 'Nome file': 'ph_2.jpg'}
2025-06-10 00:18:50,876 | INFO     | PhotoDrop.MainWindow | _on_selection_changed:257 | MainWindow: _on_selection_changed ha chiamato right_panel.show_image con ph_2.jpg
2025-06-10 00:18:50,876 | DEBUG    | PhotoRenamer.LeftPanel | _perform_manual_reorder:215 | [DND] _perform_manual_reorder: Elementi spostati internamente. Nuova lista: ['ph_1.jpg', 'ph_3.jpg', 'ph_4.jpg', 'ph_5.jpg', 'ph_2.jpg', 'ph_6.jpg']
2025-06-10 00:18:50,877 | INFO     | PhotoDrop.ImageListController | set_image_list:41 | ImageListController: lista immagini aggiornata (6)
2025-06-10 00:18:50,877 | INFO     | PhotoDrop.RightPanel | set_image_list:291 | RightPanel: impostata lista di 6 immagini
2025-06-10 00:18:50,877 | INFO     | PhotoDrop.MainController | on_images_loaded:51 | Caricate 6 immagini
2025-06-10 00:18:50,877 | INFO     | PhotoRenamer.LeftPanel | _sync_controller_after_reorder:364 | [DND] Controller sincronizzato dopo riordinamento
2025-06-10 00:18:50,877 | DEBUG    | PhotoRenamer.LeftPanel | _perform_manual_reorder:221 | [DND] _perform_manual_reorder: Chiamato _sync_controller_after_reorder su <ui.left_panel.LeftPanel(0x253e40a49b0) at 0x00000253E4866340>
2025-06-10 00:18:50,877 | INFO     | PhotoRenamer.LeftPanel | _perform_manual_reorder:228 | [DND] Riordinamento manuale completato: 1 elementi spostati alla posizione 4
2025-06-10 00:18:50,877 | INFO     | PhotoRenamer.LeftPanel | dropEvent:129 | [DND] Drop completato alla posizione target 4
2025-06-10 00:18:50,879 | DEBUG    | PhotoRenamer.LeftPanel | startDrag:311 | [DND.startDrag] Drag operation completed with MoveAction for ph_2.jpg
2025-06-10 00:18:56,400 | DEBUG    | PhotoRenamer.LeftPanel | _on_current_item_changed:1121 | [LP._on_current_item_changed] Chiamato con current: ph_3.jpg, previous: ph_2.jpg
2025-06-10 00:18:56,400 | DEBUG    | PhotoRenamer.LeftPanel | _on_current_item_changed:1126 | [LP._on_current_item_changed] Emitting selection_changed with path: C:/Users/<USER>/Desktop/foto2\ph_3.jpg
2025-06-10 00:18:56,412 | INFO     | PhotoDrop.MetadataController | load_metadata:35 | Estrazione metadati EXIF per: C:\Users\<USER>\Desktop\foto2\ph_3.jpg
2025-06-10 00:18:56,412 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: YCbCrPositioning (531) - type: short (3) - value: b'\x01\x00'
2025-06-10 00:18:56,413 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DateTime (306) - type: string (2) Tag Location: 34 - Data Location: 138 - value: b'2025:03:08 16:05:29\x00'
2025-06-10 00:18:56,413 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Make (271) - type: string (2) Tag Location: 46 - Data Location: 158 - value: b'samsung\x00'
2025-06-10 00:18:56,413 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Model (272) - type: string (2) Tag Location: 58 - Data Location: 166 - value: b'SM-P615\x00'
2025-06-10 00:18:56,413 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Orientation (274) - type: short (3) - value: b'\x01\x00'
2025-06-10 00:18:56,413 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: XResolution (282) - type: rational (5) Tag Location: 82 - Data Location: 174 - value: b'H\x00\x00\x00\x01\x00\x00\x00'
2025-06-10 00:18:56,413 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: YResolution (283) - type: rational (5) Tag Location: 94 - Data Location: 182 - value: b'H\x00\x00\x00\x01\x00\x00\x00'
2025-06-10 00:18:56,413 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ResolutionUnit (296) - type: short (3) - value: b'\x02\x00'
2025-06-10 00:18:56,413 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Software (305) - type: string (2) Tag Location: 118 - Data Location: 190 - value: b'ImageMeter\x00'
2025-06-10 00:18:56,413 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExifIFD (34665) - type: long (4) - value: b'\xca\x00\x00\x00'
2025-06-10 00:18:56,413 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureTime (33434) - type: rational (5) Tag Location: 216 - Data Location: 560 - value: b'\x01\x00\x00\x00!\x00\x00\x00'
2025-06-10 00:18:56,414 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: FNumber (33437) - type: rational (5) Tag Location: 228 - Data Location: 568 - value: b'\xbe\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:18:56,414 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureProgram (34850) - type: short (3) - value: b'\x02\x00'
2025-06-10 00:18:56,414 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ISOSpeedRatings (34855) - type: short (3) - value: b'\xa0\x00'
2025-06-10 00:18:56,414 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExifVersion (36864) - type: undefined (7) - value: b'0220'
2025-06-10 00:18:56,414 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DateTimeOriginal (36867) - type: string (2) Tag Location: 276 - Data Location: 576 - value: b'2025:03:08 16:05:29\x00'
2025-06-10 00:18:56,414 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DateTimeDigitized (36868) - type: string (2) Tag Location: 288 - Data Location: 596 - value: b'2025:03:08 16:05:29\x00'
2025-06-10 00:18:56,414 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: unknown (36880) - type: string (2) Tag Location: 300 - Data Location: 616 - value: b'+01:00\x00'
2025-06-10 00:18:56,414 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: unknown (36881) - type: string (2) Tag Location: 312 - Data Location: 624 - value: b'+01:00\x00'
2025-06-10 00:18:56,414 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ShutterSpeedValue (37377) - type: rational (5) Tag Location: 324 - Data Location: 632 - value: b'\x01\x00\x00\x00!\x00\x00\x00'
2025-06-10 00:18:56,414 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ApertureValue (37378) - type: rational (5) Tag Location: 336 - Data Location: 640 - value: b'\xb9\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:18:56,414 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: BrightnessValue (37379) - type: signed rational (10) Tag Location: 348 - Data Location: 648 - value: b'b\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:18:56,414 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureBiasValue (37380) - type: signed rational (10) Tag Location: 360 - Data Location: 656 - value: b'\x00\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:18:56,414 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: MaxApertureValue (37381) - type: rational (5) Tag Location: 372 - Data Location: 664 - value: b'\xb9\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:18:56,415 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: MeteringMode (37383) - type: short (3) - value: b'\x02\x00'
2025-06-10 00:18:56,415 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Flash (37385) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:18:56,415 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: FocalLength (37386) - type: rational (5) Tag Location: 408 - Data Location: 672 - value: b'#\x01\x00\x00d\x00\x00\x00'
2025-06-10 00:18:56,415 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SubSec (37520) - type: string (2) - value: b'970\x00'
2025-06-10 00:18:56,415 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SubSecTimeOriginal (37521) - type: string (2) - value: b'970\x00'
2025-06-10 00:18:56,415 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SubsecTimeDigitized (37522) - type: string (2) - value: b'970\x00'
2025-06-10 00:18:56,415 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ColorSpace (40961) - type: short (3) - value: b'\x01\x00'
2025-06-10 00:18:56,415 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: PixelXDimension (40962) - type: long (4) - value: b'\xc0\x0c\x00\x00'
2025-06-10 00:18:56,415 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: PixelYDimension (40963) - type: long (4) - value: b'\xa4\x07\x00\x00'
2025-06-10 00:18:56,415 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureMode (41986) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:18:56,415 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: WhiteBalance (41987) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:18:56,415 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DigitalZoomRatio (41988) - type: rational (5) Tag Location: 516 - Data Location: 680 - value: b'd\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:18:56,415 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: FocalLengthIn35mmFilm (41989) - type: short (3) - value: b'\x1b\x00'
2025-06-10 00:18:56,415 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SceneCaptureType (41990) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:18:56,415 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ImageUniqueID (42016) - type: string (2) Tag Location: 552 - Data Location: 688 - value: b'G08LLMJ00HM\x00'
2025-06-10 00:18:56,416 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureTime (33434) - type: rational (5) Tag Location: 216 - Data Location: 560 - value: b'\x01\x00\x00\x00!\x00\x00\x00'
2025-06-10 00:18:56,416 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: FNumber (33437) - type: rational (5) Tag Location: 228 - Data Location: 568 - value: b'\xbe\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:18:56,416 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureProgram (34850) - type: short (3) - value: b'\x02\x00'
2025-06-10 00:18:56,416 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ISOSpeedRatings (34855) - type: short (3) - value: b'\xa0\x00'
2025-06-10 00:18:56,416 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExifVersion (36864) - type: undefined (7) - value: b'0220'
2025-06-10 00:18:56,416 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DateTimeOriginal (36867) - type: string (2) Tag Location: 276 - Data Location: 576 - value: b'2025:03:08 16:05:29\x00'
2025-06-10 00:18:56,416 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DateTimeDigitized (36868) - type: string (2) Tag Location: 288 - Data Location: 596 - value: b'2025:03:08 16:05:29\x00'
2025-06-10 00:18:56,416 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: unknown (36880) - type: string (2) Tag Location: 300 - Data Location: 616 - value: b'+01:00\x00'
2025-06-10 00:18:56,416 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: unknown (36881) - type: string (2) Tag Location: 312 - Data Location: 624 - value: b'+01:00\x00'
2025-06-10 00:18:56,416 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ShutterSpeedValue (37377) - type: rational (5) Tag Location: 324 - Data Location: 632 - value: b'\x01\x00\x00\x00!\x00\x00\x00'
2025-06-10 00:18:56,416 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ApertureValue (37378) - type: rational (5) Tag Location: 336 - Data Location: 640 - value: b'\xb9\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:18:56,416 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: BrightnessValue (37379) - type: signed rational (10) Tag Location: 348 - Data Location: 648 - value: b'b\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:18:56,417 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureBiasValue (37380) - type: signed rational (10) Tag Location: 360 - Data Location: 656 - value: b'\x00\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:18:56,417 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: MaxApertureValue (37381) - type: rational (5) Tag Location: 372 - Data Location: 664 - value: b'\xb9\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:18:56,417 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: MeteringMode (37383) - type: short (3) - value: b'\x02\x00'
2025-06-10 00:18:56,417 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Flash (37385) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:18:56,417 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: FocalLength (37386) - type: rational (5) Tag Location: 408 - Data Location: 672 - value: b'#\x01\x00\x00d\x00\x00\x00'
2025-06-10 00:18:56,417 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SubSec (37520) - type: string (2) - value: b'970\x00'
2025-06-10 00:18:56,417 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SubSecTimeOriginal (37521) - type: string (2) - value: b'970\x00'
2025-06-10 00:18:56,417 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SubsecTimeDigitized (37522) - type: string (2) - value: b'970\x00'
2025-06-10 00:18:56,417 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ColorSpace (40961) - type: short (3) - value: b'\x01\x00'
2025-06-10 00:18:56,417 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: PixelXDimension (40962) - type: long (4) - value: b'\xc0\x0c\x00\x00'
2025-06-10 00:18:56,417 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: PixelYDimension (40963) - type: long (4) - value: b'\xa4\x07\x00\x00'
2025-06-10 00:18:56,417 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureMode (41986) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:18:56,418 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: WhiteBalance (41987) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:18:56,419 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DigitalZoomRatio (41988) - type: rational (5) Tag Location: 516 - Data Location: 680 - value: b'd\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:18:56,419 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: FocalLengthIn35mmFilm (41989) - type: short (3) - value: b'\x1b\x00'
2025-06-10 00:18:56,419 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SceneCaptureType (41990) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:18:56,419 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ImageUniqueID (42016) - type: string (2) Tag Location: 552 - Data Location: 688 - value: b'G08LLMJ00HM\x00'
2025-06-10 00:18:56,423 | DEBUG    | PhotoDrop.MetadataController | load_metadata:42 | Metadati EXIF estratti: {'Marca': 'samsung', 'Modello': 'SM-P615', 'Data e ora': '08/03/2025 16:05:29', 'Compensazione esposizione': '0.0', 'Modalità di misurazione': '2', 'Flash': '0', 'Lunghezza focale': '2.91', 'Tempo di esposizione': '0.030303030303030304 sec', 'Diaframma': 'f/1.9', 'ISO': '160', 'Bilanciamento del bianco': '0', 'Dimensione file': '193.3 KB', 'Nome file': 'ph_3.jpg'}
2025-06-10 00:18:56,423 | INFO     | PhotoDrop.MainWindow | _on_selection_changed:257 | MainWindow: _on_selection_changed ha chiamato right_panel.show_image con ph_3.jpg
2025-06-10 00:18:56,447 | CRITICAL | PhotoRenamer.LeftPanel | startDrag:261 | CUSTOM STARTDRAG CALLED!
2025-06-10 00:18:56,448 | DEBUG    | PhotoRenamer.LeftPanel | startDrag:284 | [DND.startDrag] Pixmap for ph_3.jpg requested with available_sizes[0] (PySide6.QtCore.QSize(100, 58)).isNull: False, Size: PySide6.QtCore.QSize(100, 58)
2025-06-10 00:18:56,448 | DEBUG    | PhotoRenamer.LeftPanel | startDrag:302 | [DND.startDrag] Pixmap for ph_3.jpg successfully created. Size: PySide6.QtCore.QSize(100, 58)
2025-06-10 00:18:56,448 | INFO     | PhotoRenamer.LeftPanel | startDrag:307 | [DND.startDrag] Avvio drag per ph_3.jpg con pixmap size PySide6.QtCore.QSize(100, 58)
2025-06-10 00:18:56,458 | DEBUG    | PhotoRenamer.LeftPanel | dragMoveEvent:87 | [DND] Drag move: posizione indicatore 2
2025-06-10 00:18:56,472 | DEBUG    | PhotoRenamer.LeftPanel | dragMoveEvent:87 | [DND] Drag move: posizione indicatore 2
2025-06-10 00:18:56,474 | DEBUG    | PhotoRenamer.LeftPanel | dragMoveEvent:87 | [DND] Drag move: posizione indicatore 2
2025-06-10 00:18:56,481 | DEBUG    | PhotoRenamer.LeftPanel | dragMoveEvent:87 | [DND] Drag move: posizione indicatore 2
2025-06-10 00:18:56,488 | DEBUG    | PhotoRenamer.LeftPanel | dragMoveEvent:87 | [DND] Drag move: posizione indicatore 6
2025-06-10 00:18:56,496 | DEBUG    | PhotoRenamer.LeftPanel | dragMoveEvent:87 | [DND] Drag move: posizione indicatore 6
2025-06-10 00:18:56,503 | DEBUG    | PhotoRenamer.LeftPanel | dragMoveEvent:87 | [DND] Drag move: posizione indicatore 6
2025-06-10 00:18:56,511 | DEBUG    | PhotoRenamer.LeftPanel | dragMoveEvent:87 | [DND] Drag move: posizione indicatore 6
2025-06-10 00:18:56,684 | DEBUG    | PhotoRenamer.LeftPanel | dragMoveEvent:87 | [DND] Drag move: posizione indicatore 6
2025-06-10 00:18:56,691 | DEBUG    | PhotoRenamer.LeftPanel | dragMoveEvent:87 | [DND] Drag move: posizione indicatore 6
2025-06-10 00:18:56,699 | DEBUG    | PhotoRenamer.LeftPanel | dragMoveEvent:87 | [DND] Drag move: posizione indicatore 6
2025-06-10 00:18:56,706 | DEBUG    | PhotoRenamer.LeftPanel | dragMoveEvent:87 | [DND] Drag move: posizione indicatore 6
2025-06-10 00:18:56,713 | DEBUG    | PhotoRenamer.LeftPanel | dragMoveEvent:87 | [DND] Drag move: posizione indicatore 6
2025-06-10 00:18:56,721 | DEBUG    | PhotoRenamer.LeftPanel | dragMoveEvent:87 | [DND] Drag move: posizione indicatore 6
2025-06-10 00:18:56,728 | DEBUG    | PhotoRenamer.LeftPanel | dragMoveEvent:87 | [DND] Drag move: posizione indicatore 6
2025-06-10 00:18:56,736 | DEBUG    | PhotoRenamer.LeftPanel | dragMoveEvent:87 | [DND] Drag move: posizione indicatore 6
2025-06-10 00:18:56,743 | DEBUG    | PhotoRenamer.LeftPanel | dragMoveEvent:87 | [DND] Drag move: posizione indicatore 6
2025-06-10 00:18:56,751 | DEBUG    | PhotoRenamer.LeftPanel | dragMoveEvent:87 | [DND] Drag move: posizione indicatore 6
2025-06-10 00:18:56,759 | DEBUG    | PhotoRenamer.LeftPanel | dragMoveEvent:87 | [DND] Drag move: posizione indicatore 6
2025-06-10 00:18:56,766 | DEBUG    | PhotoRenamer.LeftPanel | dragMoveEvent:87 | [DND] Drag move: posizione indicatore 6
2025-06-10 00:18:56,781 | DEBUG    | PhotoRenamer.LeftPanel | dragMoveEvent:87 | [DND] Drag move: posizione indicatore 6
2025-06-10 00:18:56,788 | DEBUG    | PhotoRenamer.LeftPanel | dragMoveEvent:87 | [DND] Drag move: posizione indicatore 6
2025-06-10 00:18:56,796 | DEBUG    | PhotoRenamer.LeftPanel | dragMoveEvent:87 | [DND] Drag move: posizione indicatore 6
2025-06-10 00:18:56,804 | DEBUG    | PhotoRenamer.LeftPanel | dragMoveEvent:87 | [DND] Drag move: posizione indicatore 6
2025-06-10 00:18:56,811 | DEBUG    | PhotoRenamer.LeftPanel | dragMoveEvent:87 | [DND] Drag move: posizione indicatore 6
2025-06-10 00:18:56,818 | DEBUG    | PhotoRenamer.LeftPanel | dragMoveEvent:87 | [DND] Drag move: posizione indicatore 6
2025-06-10 00:18:56,826 | DEBUG    | PhotoRenamer.LeftPanel | dragMoveEvent:87 | [DND] Drag move: posizione indicatore 6
2025-06-10 00:18:56,833 | DEBUG    | PhotoRenamer.LeftPanel | dragMoveEvent:87 | [DND] Drag move: posizione indicatore 6
2025-06-10 00:18:56,841 | DEBUG    | PhotoRenamer.LeftPanel | dragMoveEvent:87 | [DND] Drag move: posizione indicatore 6
2025-06-10 00:18:56,848 | DEBUG    | PhotoRenamer.LeftPanel | dragMoveEvent:87 | [DND] Drag move: posizione indicatore 6
2025-06-10 00:18:56,864 | DEBUG    | PhotoRenamer.LeftPanel | dragMoveEvent:87 | [DND] Drag move: posizione indicatore 6
2025-06-10 00:18:57,683 | INFO     | PhotoRenamer.LeftPanel | dropEvent:117 | [DND] Drop iniziato: posizione target calcolata 6
2025-06-10 00:18:57,684 | INFO     | PhotoRenamer.LeftPanel | _perform_manual_reorder:133 | [DND] Inizio riordinamento: 1 elementi verso posizione 6
2025-06-10 00:18:57,684 | INFO     | PhotoRenamer.LeftPanel | _perform_manual_reorder:134 | [DND] Numero totale elementi prima: 6
2025-06-10 00:18:57,685 | DEBUG    | PhotoRenamer.LeftPanel | _perform_manual_reorder:140 | [DND._perform_manual_reorder] Icona per ph_3.jpg: icon.isNull: False, icon.availableSizes: [PySide6.QtCore.QSize(100, 58)]
2025-06-10 00:18:57,685 | INFO     | PhotoRenamer.LeftPanel | _perform_manual_reorder:152 | [DND] Elementi da spostare: ['ph_3.jpg']
2025-06-10 00:18:57,685 | INFO     | PhotoRenamer.LeftPanel | _perform_manual_reorder:153 | [DND] Posizioni originali: [1]
2025-06-10 00:18:57,686 | DEBUG    | PhotoRenamer.LeftPanel | _on_current_item_changed:1121 | [LP._on_current_item_changed] Chiamato con current: ph_1.jpg, previous: ph_3.jpg
2025-06-10 00:18:57,687 | DEBUG    | PhotoRenamer.LeftPanel | _on_current_item_changed:1126 | [LP._on_current_item_changed] Emitting selection_changed with path: C:/Users/<USER>/Desktop/foto2\ph_1.jpg
2025-06-10 00:18:57,700 | INFO     | PhotoDrop.MetadataController | load_metadata:35 | Estrazione metadati EXIF per: C:\Users\<USER>\Desktop\foto2\ph_1.jpg
2025-06-10 00:18:57,700 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: YCbCrPositioning (531) - type: short (3) - value: b'\x01\x00'
2025-06-10 00:18:57,701 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DateTime (306) - type: string (2) Tag Location: 34 - Data Location: 138 - value: b'2025:03:08 16:05:44\x00'
2025-06-10 00:18:57,701 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Make (271) - type: string (2) Tag Location: 46 - Data Location: 158 - value: b'samsung\x00'
2025-06-10 00:18:57,701 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Model (272) - type: string (2) Tag Location: 58 - Data Location: 166 - value: b'SM-P615\x00'
2025-06-10 00:18:57,701 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Orientation (274) - type: short (3) - value: b'\x01\x00'
2025-06-10 00:18:57,701 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: XResolution (282) - type: rational (5) Tag Location: 82 - Data Location: 174 - value: b'H\x00\x00\x00\x01\x00\x00\x00'
2025-06-10 00:18:57,701 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: YResolution (283) - type: rational (5) Tag Location: 94 - Data Location: 182 - value: b'H\x00\x00\x00\x01\x00\x00\x00'
2025-06-10 00:18:57,701 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ResolutionUnit (296) - type: short (3) - value: b'\x02\x00'
2025-06-10 00:18:57,701 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Software (305) - type: string (2) Tag Location: 118 - Data Location: 190 - value: b'ImageMeter\x00'
2025-06-10 00:18:57,701 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExifIFD (34665) - type: long (4) - value: b'\xca\x00\x00\x00'
2025-06-10 00:18:57,701 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureTime (33434) - type: rational (5) Tag Location: 216 - Data Location: 560 - value: b'\x01\x00\x00\x002\x00\x00\x00'
2025-06-10 00:18:57,702 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: FNumber (33437) - type: rational (5) Tag Location: 228 - Data Location: 568 - value: b'\xbe\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:18:57,702 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureProgram (34850) - type: short (3) - value: b'\x02\x00'
2025-06-10 00:18:57,702 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ISOSpeedRatings (34855) - type: short (3) - value: b'd\x00'
2025-06-10 00:18:57,702 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExifVersion (36864) - type: undefined (7) - value: b'0220'
2025-06-10 00:18:57,702 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DateTimeOriginal (36867) - type: string (2) Tag Location: 276 - Data Location: 576 - value: b'2025:03:08 16:05:44\x00'
2025-06-10 00:18:57,702 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DateTimeDigitized (36868) - type: string (2) Tag Location: 288 - Data Location: 596 - value: b'2025:03:08 16:05:44\x00'
2025-06-10 00:18:57,702 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: unknown (36880) - type: string (2) Tag Location: 300 - Data Location: 616 - value: b'+01:00\x00'
2025-06-10 00:18:57,702 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: unknown (36881) - type: string (2) Tag Location: 312 - Data Location: 624 - value: b'+01:00\x00'
2025-06-10 00:18:57,702 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ShutterSpeedValue (37377) - type: rational (5) Tag Location: 324 - Data Location: 632 - value: b'\x01\x00\x00\x002\x00\x00\x00'
2025-06-10 00:18:57,703 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ApertureValue (37378) - type: rational (5) Tag Location: 336 - Data Location: 640 - value: b'\xb9\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:18:57,703 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: BrightnessValue (37379) - type: signed rational (10) Tag Location: 348 - Data Location: 648 - value: b'\xe7\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:18:57,703 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureBiasValue (37380) - type: signed rational (10) Tag Location: 360 - Data Location: 656 - value: b'\x00\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:18:57,703 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: MaxApertureValue (37381) - type: rational (5) Tag Location: 372 - Data Location: 664 - value: b'\xb9\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:18:57,703 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: MeteringMode (37383) - type: short (3) - value: b'\x02\x00'
2025-06-10 00:18:57,703 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Flash (37385) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:18:57,703 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: FocalLength (37386) - type: rational (5) Tag Location: 408 - Data Location: 672 - value: b'#\x01\x00\x00d\x00\x00\x00'
2025-06-10 00:18:57,704 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SubSec (37520) - type: string (2) - value: b'323\x00'
2025-06-10 00:18:57,704 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SubSecTimeOriginal (37521) - type: string (2) - value: b'323\x00'
2025-06-10 00:18:57,704 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SubsecTimeDigitized (37522) - type: string (2) - value: b'323\x00'
2025-06-10 00:18:57,704 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ColorSpace (40961) - type: short (3) - value: b'\x01\x00'
2025-06-10 00:18:57,704 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: PixelXDimension (40962) - type: long (4) - value: b'\xc0\x0c\x00\x00'
2025-06-10 00:18:57,704 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: PixelYDimension (40963) - type: long (4) - value: b'\xa4\x07\x00\x00'
2025-06-10 00:18:57,704 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureMode (41986) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:18:57,704 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: WhiteBalance (41987) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:18:57,705 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DigitalZoomRatio (41988) - type: rational (5) Tag Location: 516 - Data Location: 680 - value: b'd\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:18:57,705 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: FocalLengthIn35mmFilm (41989) - type: short (3) - value: b'\x1b\x00'
2025-06-10 00:18:57,705 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SceneCaptureType (41990) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:18:57,705 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ImageUniqueID (42016) - type: string (2) Tag Location: 552 - Data Location: 688 - value: b'G08LLMJ00HM\x00'
2025-06-10 00:18:57,705 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureTime (33434) - type: rational (5) Tag Location: 216 - Data Location: 560 - value: b'\x01\x00\x00\x002\x00\x00\x00'
2025-06-10 00:18:57,705 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: FNumber (33437) - type: rational (5) Tag Location: 228 - Data Location: 568 - value: b'\xbe\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:18:57,705 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureProgram (34850) - type: short (3) - value: b'\x02\x00'
2025-06-10 00:18:57,705 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ISOSpeedRatings (34855) - type: short (3) - value: b'd\x00'
2025-06-10 00:18:57,705 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExifVersion (36864) - type: undefined (7) - value: b'0220'
2025-06-10 00:18:57,705 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DateTimeOriginal (36867) - type: string (2) Tag Location: 276 - Data Location: 576 - value: b'2025:03:08 16:05:44\x00'
2025-06-10 00:18:57,705 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DateTimeDigitized (36868) - type: string (2) Tag Location: 288 - Data Location: 596 - value: b'2025:03:08 16:05:44\x00'
2025-06-10 00:18:57,706 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: unknown (36880) - type: string (2) Tag Location: 300 - Data Location: 616 - value: b'+01:00\x00'
2025-06-10 00:18:57,706 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: unknown (36881) - type: string (2) Tag Location: 312 - Data Location: 624 - value: b'+01:00\x00'
2025-06-10 00:18:57,706 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ShutterSpeedValue (37377) - type: rational (5) Tag Location: 324 - Data Location: 632 - value: b'\x01\x00\x00\x002\x00\x00\x00'
2025-06-10 00:18:57,706 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ApertureValue (37378) - type: rational (5) Tag Location: 336 - Data Location: 640 - value: b'\xb9\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:18:57,706 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: BrightnessValue (37379) - type: signed rational (10) Tag Location: 348 - Data Location: 648 - value: b'\xe7\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:18:57,706 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureBiasValue (37380) - type: signed rational (10) Tag Location: 360 - Data Location: 656 - value: b'\x00\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:18:57,706 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: MaxApertureValue (37381) - type: rational (5) Tag Location: 372 - Data Location: 664 - value: b'\xb9\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:18:57,706 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: MeteringMode (37383) - type: short (3) - value: b'\x02\x00'
2025-06-10 00:18:57,706 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Flash (37385) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:18:57,706 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: FocalLength (37386) - type: rational (5) Tag Location: 408 - Data Location: 672 - value: b'#\x01\x00\x00d\x00\x00\x00'
2025-06-10 00:18:57,706 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SubSec (37520) - type: string (2) - value: b'323\x00'
2025-06-10 00:18:57,707 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SubSecTimeOriginal (37521) - type: string (2) - value: b'323\x00'
2025-06-10 00:18:57,707 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SubsecTimeDigitized (37522) - type: string (2) - value: b'323\x00'
2025-06-10 00:18:57,707 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ColorSpace (40961) - type: short (3) - value: b'\x01\x00'
2025-06-10 00:18:57,707 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: PixelXDimension (40962) - type: long (4) - value: b'\xc0\x0c\x00\x00'
2025-06-10 00:18:57,707 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: PixelYDimension (40963) - type: long (4) - value: b'\xa4\x07\x00\x00'
2025-06-10 00:18:57,707 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureMode (41986) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:18:57,707 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: WhiteBalance (41987) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:18:57,707 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DigitalZoomRatio (41988) - type: rational (5) Tag Location: 516 - Data Location: 680 - value: b'd\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:18:57,707 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: FocalLengthIn35mmFilm (41989) - type: short (3) - value: b'\x1b\x00'
2025-06-10 00:18:57,707 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SceneCaptureType (41990) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:18:57,707 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ImageUniqueID (42016) - type: string (2) Tag Location: 552 - Data Location: 688 - value: b'G08LLMJ00HM\x00'
2025-06-10 00:18:57,712 | DEBUG    | PhotoDrop.MetadataController | load_metadata:42 | Metadati EXIF estratti: {'Marca': 'samsung', 'Modello': 'SM-P615', 'Data e ora': '08/03/2025 16:05:44', 'Compensazione esposizione': '0.0', 'Modalità di misurazione': '2', 'Flash': '0', 'Lunghezza focale': '2.91', 'Tempo di esposizione': '0.02 sec', 'Diaframma': 'f/1.9', 'ISO': '100', 'Bilanciamento del bianco': '0', 'Dimensione file': '193.3 KB', 'Nome file': 'ph_1.jpg'}
2025-06-10 00:18:57,712 | INFO     | PhotoDrop.MainWindow | _on_selection_changed:257 | MainWindow: _on_selection_changed ha chiamato right_panel.show_image con ph_1.jpg
2025-06-10 00:18:57,712 | INFO     | PhotoRenamer.LeftPanel | _perform_manual_reorder:160 | [DND] Rimosso elemento "ph_3.jpg" dalla posizione 1
2025-06-10 00:18:57,712 | INFO     | PhotoRenamer.LeftPanel | _perform_manual_reorder:162 | [DND] Numero elementi dopo rimozione: 5
2025-06-10 00:18:57,712 | DEBUG    | PhotoRenamer.LeftPanel | _perform_manual_reorder:186 | [DND] Reimpostata icona originale per ph_3.jpg. isNull: False, availableSizes: [PySide6.QtCore.QSize(100, 58)]
2025-06-10 00:18:57,712 | INFO     | PhotoRenamer.LeftPanel | _perform_manual_reorder:197 | [DND] Reinserito elemento "ph_3.jpg" alla posizione 5
2025-06-10 00:18:57,713 | INFO     | PhotoRenamer.LeftPanel | _perform_manual_reorder:199 | [DND] Numero elementi dopo inserimento: 6
2025-06-10 00:18:57,713 | INFO     | PhotoRenamer.LeftPanel | _perform_manual_reorder:206 | [DND] Selezionato elemento "ph_3.jpg" alla posizione 5
2025-06-10 00:18:57,713 | DEBUG    | PhotoRenamer.LeftPanel | _on_current_item_changed:1121 | [LP._on_current_item_changed] Chiamato con current: ph_3.jpg, previous: ph_1.jpg
2025-06-10 00:18:57,714 | DEBUG    | PhotoRenamer.LeftPanel | _on_current_item_changed:1126 | [LP._on_current_item_changed] Emitting selection_changed with path: C:/Users/<USER>/Desktop/foto2\ph_3.jpg
2025-06-10 00:18:57,727 | INFO     | PhotoDrop.MetadataController | load_metadata:35 | Estrazione metadati EXIF per: C:\Users\<USER>\Desktop\foto2\ph_3.jpg
2025-06-10 00:18:57,728 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: YCbCrPositioning (531) - type: short (3) - value: b'\x01\x00'
2025-06-10 00:18:57,728 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DateTime (306) - type: string (2) Tag Location: 34 - Data Location: 138 - value: b'2025:03:08 16:05:29\x00'
2025-06-10 00:18:57,728 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Make (271) - type: string (2) Tag Location: 46 - Data Location: 158 - value: b'samsung\x00'
2025-06-10 00:18:57,728 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Model (272) - type: string (2) Tag Location: 58 - Data Location: 166 - value: b'SM-P615\x00'
2025-06-10 00:18:57,728 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Orientation (274) - type: short (3) - value: b'\x01\x00'
2025-06-10 00:18:57,728 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: XResolution (282) - type: rational (5) Tag Location: 82 - Data Location: 174 - value: b'H\x00\x00\x00\x01\x00\x00\x00'
2025-06-10 00:18:57,728 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: YResolution (283) - type: rational (5) Tag Location: 94 - Data Location: 182 - value: b'H\x00\x00\x00\x01\x00\x00\x00'
2025-06-10 00:18:57,729 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ResolutionUnit (296) - type: short (3) - value: b'\x02\x00'
2025-06-10 00:18:57,729 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Software (305) - type: string (2) Tag Location: 118 - Data Location: 190 - value: b'ImageMeter\x00'
2025-06-10 00:18:57,729 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExifIFD (34665) - type: long (4) - value: b'\xca\x00\x00\x00'
2025-06-10 00:18:57,729 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureTime (33434) - type: rational (5) Tag Location: 216 - Data Location: 560 - value: b'\x01\x00\x00\x00!\x00\x00\x00'
2025-06-10 00:18:57,729 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: FNumber (33437) - type: rational (5) Tag Location: 228 - Data Location: 568 - value: b'\xbe\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:18:57,730 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureProgram (34850) - type: short (3) - value: b'\x02\x00'
2025-06-10 00:18:57,730 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ISOSpeedRatings (34855) - type: short (3) - value: b'\xa0\x00'
2025-06-10 00:18:57,730 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExifVersion (36864) - type: undefined (7) - value: b'0220'
2025-06-10 00:18:57,730 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DateTimeOriginal (36867) - type: string (2) Tag Location: 276 - Data Location: 576 - value: b'2025:03:08 16:05:29\x00'
2025-06-10 00:18:57,730 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DateTimeDigitized (36868) - type: string (2) Tag Location: 288 - Data Location: 596 - value: b'2025:03:08 16:05:29\x00'
2025-06-10 00:18:57,730 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: unknown (36880) - type: string (2) Tag Location: 300 - Data Location: 616 - value: b'+01:00\x00'
2025-06-10 00:18:57,730 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: unknown (36881) - type: string (2) Tag Location: 312 - Data Location: 624 - value: b'+01:00\x00'
2025-06-10 00:18:57,730 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ShutterSpeedValue (37377) - type: rational (5) Tag Location: 324 - Data Location: 632 - value: b'\x01\x00\x00\x00!\x00\x00\x00'
2025-06-10 00:18:57,730 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ApertureValue (37378) - type: rational (5) Tag Location: 336 - Data Location: 640 - value: b'\xb9\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:18:57,730 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: BrightnessValue (37379) - type: signed rational (10) Tag Location: 348 - Data Location: 648 - value: b'b\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:18:57,730 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureBiasValue (37380) - type: signed rational (10) Tag Location: 360 - Data Location: 656 - value: b'\x00\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:18:57,730 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: MaxApertureValue (37381) - type: rational (5) Tag Location: 372 - Data Location: 664 - value: b'\xb9\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:18:57,731 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: MeteringMode (37383) - type: short (3) - value: b'\x02\x00'
2025-06-10 00:18:57,731 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Flash (37385) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:18:57,731 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: FocalLength (37386) - type: rational (5) Tag Location: 408 - Data Location: 672 - value: b'#\x01\x00\x00d\x00\x00\x00'
2025-06-10 00:18:57,731 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SubSec (37520) - type: string (2) - value: b'970\x00'
2025-06-10 00:18:57,731 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SubSecTimeOriginal (37521) - type: string (2) - value: b'970\x00'
2025-06-10 00:18:57,731 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SubsecTimeDigitized (37522) - type: string (2) - value: b'970\x00'
2025-06-10 00:18:57,731 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ColorSpace (40961) - type: short (3) - value: b'\x01\x00'
2025-06-10 00:18:57,731 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: PixelXDimension (40962) - type: long (4) - value: b'\xc0\x0c\x00\x00'
2025-06-10 00:18:57,731 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: PixelYDimension (40963) - type: long (4) - value: b'\xa4\x07\x00\x00'
2025-06-10 00:18:57,731 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureMode (41986) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:18:57,731 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: WhiteBalance (41987) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:18:57,731 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DigitalZoomRatio (41988) - type: rational (5) Tag Location: 516 - Data Location: 680 - value: b'd\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:18:57,732 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: FocalLengthIn35mmFilm (41989) - type: short (3) - value: b'\x1b\x00'
2025-06-10 00:18:57,732 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SceneCaptureType (41990) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:18:57,732 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ImageUniqueID (42016) - type: string (2) Tag Location: 552 - Data Location: 688 - value: b'G08LLMJ00HM\x00'
2025-06-10 00:18:57,732 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureTime (33434) - type: rational (5) Tag Location: 216 - Data Location: 560 - value: b'\x01\x00\x00\x00!\x00\x00\x00'
2025-06-10 00:18:57,732 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: FNumber (33437) - type: rational (5) Tag Location: 228 - Data Location: 568 - value: b'\xbe\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:18:57,732 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureProgram (34850) - type: short (3) - value: b'\x02\x00'
2025-06-10 00:18:57,732 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ISOSpeedRatings (34855) - type: short (3) - value: b'\xa0\x00'
2025-06-10 00:18:57,732 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExifVersion (36864) - type: undefined (7) - value: b'0220'
2025-06-10 00:18:57,733 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DateTimeOriginal (36867) - type: string (2) Tag Location: 276 - Data Location: 576 - value: b'2025:03:08 16:05:29\x00'
2025-06-10 00:18:57,733 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DateTimeDigitized (36868) - type: string (2) Tag Location: 288 - Data Location: 596 - value: b'2025:03:08 16:05:29\x00'
2025-06-10 00:18:57,733 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: unknown (36880) - type: string (2) Tag Location: 300 - Data Location: 616 - value: b'+01:00\x00'
2025-06-10 00:18:57,733 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: unknown (36881) - type: string (2) Tag Location: 312 - Data Location: 624 - value: b'+01:00\x00'
2025-06-10 00:18:57,733 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ShutterSpeedValue (37377) - type: rational (5) Tag Location: 324 - Data Location: 632 - value: b'\x01\x00\x00\x00!\x00\x00\x00'
2025-06-10 00:18:57,734 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ApertureValue (37378) - type: rational (5) Tag Location: 336 - Data Location: 640 - value: b'\xb9\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:18:57,734 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: BrightnessValue (37379) - type: signed rational (10) Tag Location: 348 - Data Location: 648 - value: b'b\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:18:57,734 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureBiasValue (37380) - type: signed rational (10) Tag Location: 360 - Data Location: 656 - value: b'\x00\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:18:57,735 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: MaxApertureValue (37381) - type: rational (5) Tag Location: 372 - Data Location: 664 - value: b'\xb9\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:18:57,735 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: MeteringMode (37383) - type: short (3) - value: b'\x02\x00'
2025-06-10 00:18:57,735 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Flash (37385) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:18:57,735 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: FocalLength (37386) - type: rational (5) Tag Location: 408 - Data Location: 672 - value: b'#\x01\x00\x00d\x00\x00\x00'
2025-06-10 00:18:57,735 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SubSec (37520) - type: string (2) - value: b'970\x00'
2025-06-10 00:18:57,735 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SubSecTimeOriginal (37521) - type: string (2) - value: b'970\x00'
2025-06-10 00:18:57,735 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SubsecTimeDigitized (37522) - type: string (2) - value: b'970\x00'
2025-06-10 00:18:57,735 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ColorSpace (40961) - type: short (3) - value: b'\x01\x00'
2025-06-10 00:18:57,735 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: PixelXDimension (40962) - type: long (4) - value: b'\xc0\x0c\x00\x00'
2025-06-10 00:18:57,735 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: PixelYDimension (40963) - type: long (4) - value: b'\xa4\x07\x00\x00'
2025-06-10 00:18:57,736 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureMode (41986) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:18:57,736 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: WhiteBalance (41987) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:18:57,736 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DigitalZoomRatio (41988) - type: rational (5) Tag Location: 516 - Data Location: 680 - value: b'd\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:18:57,736 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: FocalLengthIn35mmFilm (41989) - type: short (3) - value: b'\x1b\x00'
2025-06-10 00:18:57,736 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SceneCaptureType (41990) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:18:57,736 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ImageUniqueID (42016) - type: string (2) Tag Location: 552 - Data Location: 688 - value: b'G08LLMJ00HM\x00'
2025-06-10 00:18:57,741 | DEBUG    | PhotoDrop.MetadataController | load_metadata:42 | Metadati EXIF estratti: {'Marca': 'samsung', 'Modello': 'SM-P615', 'Data e ora': '08/03/2025 16:05:29', 'Compensazione esposizione': '0.0', 'Modalità di misurazione': '2', 'Flash': '0', 'Lunghezza focale': '2.91', 'Tempo di esposizione': '0.030303030303030304 sec', 'Diaframma': 'f/1.9', 'ISO': '160', 'Bilanciamento del bianco': '0', 'Dimensione file': '193.3 KB', 'Nome file': 'ph_3.jpg'}
2025-06-10 00:18:57,741 | INFO     | PhotoDrop.MainWindow | _on_selection_changed:257 | MainWindow: _on_selection_changed ha chiamato right_panel.show_image con ph_3.jpg
2025-06-10 00:18:57,741 | DEBUG    | PhotoRenamer.LeftPanel | _perform_manual_reorder:215 | [DND] _perform_manual_reorder: Elementi spostati internamente. Nuova lista: ['ph_1.jpg', 'ph_4.jpg', 'ph_5.jpg', 'ph_2.jpg', 'ph_6.jpg', 'ph_3.jpg']
2025-06-10 00:18:57,741 | INFO     | PhotoDrop.ImageListController | set_image_list:41 | ImageListController: lista immagini aggiornata (6)
2025-06-10 00:18:57,742 | INFO     | PhotoDrop.RightPanel | set_image_list:291 | RightPanel: impostata lista di 6 immagini
2025-06-10 00:18:57,742 | INFO     | PhotoDrop.MainController | on_images_loaded:51 | Caricate 6 immagini
2025-06-10 00:18:57,742 | INFO     | PhotoRenamer.LeftPanel | _sync_controller_after_reorder:364 | [DND] Controller sincronizzato dopo riordinamento
2025-06-10 00:18:57,742 | DEBUG    | PhotoRenamer.LeftPanel | _perform_manual_reorder:221 | [DND] _perform_manual_reorder: Chiamato _sync_controller_after_reorder su <ui.left_panel.LeftPanel(0x253e40a49b0) at 0x00000253E4866340>
2025-06-10 00:18:57,742 | INFO     | PhotoRenamer.LeftPanel | _perform_manual_reorder:228 | [DND] Riordinamento manuale completato: 1 elementi spostati alla posizione 5
2025-06-10 00:18:57,742 | INFO     | PhotoRenamer.LeftPanel | dropEvent:129 | [DND] Drop completato alla posizione target 6
2025-06-10 00:18:57,743 | DEBUG    | PhotoRenamer.LeftPanel | startDrag:311 | [DND.startDrag] Drag operation completed with MoveAction for ph_3.jpg
2025-06-10 00:19:10,924 | INFO     | core.image_cache | clear:447 | Cache immagini svuotata
2025-06-10 00:19:10,924 | INFO     | PhotoDrop.MainWindow | closeEvent:713 | Applicazione terminata
