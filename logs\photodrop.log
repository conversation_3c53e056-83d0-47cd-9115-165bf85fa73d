2025-06-10 00:31:17,700 | INFO     | PhotoDrop | <module>:25 | ================================================================================
2025-06-10 00:31:17,700 | INFO     | PhotoDrop | <module>:26 | Nuova sessione avviata il 2025-06-10 00:31:17
2025-06-10 00:31:17,700 | INFO     | PhotoDrop | <module>:27 | ================================================================================
2025-06-10 00:31:17,700 | INFO     | PhotoRenamer | <module>:34 | =================================================================
2025-06-10 00:31:17,701 | INFO     | PhotoRenamer | <module>:35 | Avvio PhotoDrop Applicazione
2025-06-10 00:31:17,701 | INFO     | PhotoRenamer | <module>:36 | =================================================================
2025-06-10 00:31:17,772 | INFO     | PhotoDrop.SettingsModel | _load_settings:112 | File impostazioni non trovato, uso impostazioni predefinite
2025-06-10 00:31:17,772 | INFO     | PhotoDrop.SettingsModel | __init__:93 | SettingsModel inizializzato con file: C:\xampp\htdocs\progetti\photodrop\config\settings.json
2025-06-10 00:31:17,773 | INFO     | core.image_cache | __init__:338 | Cache su disco inizializzata in cache/images (max 500MB, scadenza 30 giorni)
2025-06-10 00:31:17,773 | INFO     | core.image_cache | __init__:342 | Cache immagini inizializzata (max in memoria: 1000 elementi)
2025-06-10 00:31:17,776 | INFO     | PhotoDrop.ModernTopbar | __init__:71 | ModernTopbar inizializzata
2025-06-10 00:31:17,776 | INFO     | PhotoRenamer.LeftPanel | __init__:378 | LeftPanel inizializzato - test logger
2025-06-10 00:31:17,776 | DEBUG    | PhotoRenamer.LeftPanel | _load_last_folder_if_enabled:1019 | Nessuna ultima cartella valida trovata
2025-06-10 00:31:17,778 | INFO     | PhotoRenamer.LeftPanel | __init__:46 | [DND] DraggableListWidget inizializzato
2025-06-10 00:31:17,778 | INFO     | PhotoRenamer.LeftPanel | __init__:55 | [DND] DraggableListWidget configurato con gestione manuale
2025-06-10 00:31:17,778 | INFO     | PhotoRenamer.LeftPanel | _setup_ui:436 | [DND] LeftPanel: creata istanza DraggableListWidget
2025-06-10 00:31:17,778 | DEBUG    | PhotoRenamer.LeftPanel | _setup_list_widget:585 | [LP.__init__] Connesso segnale currentItemChanged a _on_current_item_changed
2025-06-10 00:31:17,809 | INFO     | PhotoDrop.MainWindow | _connect_signals:208 | [MW._connect_signals] Connesso left_panel.selection_changed a _on_selection_changed
2025-06-10 00:31:17,976 | DEBUG    | PhotoDrop.ModernTopbar | _update_responsive_mode:275 | Modalità responsive cambiata: compatta
2025-06-10 00:31:18,007 | INFO     | PhotoDrop.MainWindow | __init__:128 | Applicazione inizializzata
2025-06-10 00:31:18,009 | DEBUG    | PhotoDrop.ModernTopbar | _update_responsive_mode:275 | Modalità responsive cambiata: normale
2025-06-10 00:31:33,424 | INFO     | PhotoDrop.MainController | on_folder_changed:33 | Cartella corrente cambiata: C:/Users/<USER>/Desktop/foto2
2025-06-10 00:31:33,425 | INFO     | PhotoRenamer.ImageLoader | load:88 | Trovate 6 immagini nella cartella: C:/Users/<USER>/Desktop/foto2
2025-06-10 00:31:33,425 | INFO     | PhotoRenamer.LeftPanel | _load_images_async:639 | Caricamento immagini avviato da: C:/Users/<USER>/Desktop/foto2
2025-06-10 00:31:33,445 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: YCbCrPositioning (531) - type: short (3) - value: b'\x01\x00'
2025-06-10 00:31:33,445 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: YCbCrPositioning (531) - type: short (3) - value: b'\x01\x00'
2025-06-10 00:31:33,446 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: YCbCrPositioning (531) - type: short (3) - value: b'\x01\x00'
2025-06-10 00:31:33,446 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Make (271) - type: string (2) Tag Location: 22 - Data Location: 134 - value: b'samsung\x00'
2025-06-10 00:31:33,446 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: YCbCrPositioning (531) - type: short (3) - value: b'\x01\x00'
2025-06-10 00:31:33,446 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DateTime (306) - type: string (2) Tag Location: 34 - Data Location: 138 - value: b'2025:03:08 16:15:15\x00'
2025-06-10 00:31:33,446 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: YCbCrPositioning (531) - type: short (3) - value: b'\x01\x00'
2025-06-10 00:31:33,446 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DateTime (306) - type: string (2) Tag Location: 34 - Data Location: 138 - value: b'2025:03:08 16:05:29\x00'
2025-06-10 00:31:33,446 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DateTime (306) - type: string (2) Tag Location: 34 - Data Location: 138 - value: b'2025:03:08 16:07:42\x00'
2025-06-10 00:31:33,446 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Model (272) - type: string (2) Tag Location: 34 - Data Location: 142 - value: b'SM-P615\x00'
2025-06-10 00:31:33,447 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DateTime (306) - type: string (2) Tag Location: 34 - Data Location: 138 - value: b'2025:03:08 15:53:05\x00'
2025-06-10 00:31:33,447 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Make (271) - type: string (2) Tag Location: 46 - Data Location: 158 - value: b'samsung\x00'
2025-06-10 00:31:33,447 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DateTime (306) - type: string (2) Tag Location: 34 - Data Location: 138 - value: b'2025:03:08 16:05:44\x00'
2025-06-10 00:31:33,447 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Make (271) - type: string (2) Tag Location: 46 - Data Location: 158 - value: b'samsung\x00'
2025-06-10 00:31:33,447 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Make (271) - type: string (2) Tag Location: 46 - Data Location: 158 - value: b'samsung\x00'
2025-06-10 00:31:33,447 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Orientation (274) - type: short (3) - value: b'\x00\x01'
2025-06-10 00:31:33,447 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Make (271) - type: string (2) Tag Location: 46 - Data Location: 158 - value: b'samsung\x00'
2025-06-10 00:31:33,447 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Model (272) - type: string (2) Tag Location: 58 - Data Location: 166 - value: b'SM-P615\x00'
2025-06-10 00:31:33,447 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Make (271) - type: string (2) Tag Location: 46 - Data Location: 158 - value: b'samsung\x00'
2025-06-10 00:31:33,447 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Model (272) - type: string (2) Tag Location: 58 - Data Location: 166 - value: b'SM-P615\x00'
2025-06-10 00:31:33,447 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Model (272) - type: string (2) Tag Location: 58 - Data Location: 166 - value: b'SM-P615\x00'
2025-06-10 00:31:33,448 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: XResolution (282) - type: rational (5) Tag Location: 58 - Data Location: 150 - value: b'\x00\x00\x00H\x00\x00\x00\x01'
2025-06-10 00:31:33,448 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Model (272) - type: string (2) Tag Location: 58 - Data Location: 166 - value: b'SM-P615\x00'
2025-06-10 00:31:33,448 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Orientation (274) - type: short (3) - value: b'\x01\x00'
2025-06-10 00:31:33,448 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Model (272) - type: string (2) Tag Location: 58 - Data Location: 166 - value: b'SM-P615\x00'
2025-06-10 00:31:33,448 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Orientation (274) - type: short (3) - value: b'\x01\x00'
2025-06-10 00:31:33,448 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Orientation (274) - type: short (3) - value: b'\x01\x00'
2025-06-10 00:31:33,448 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: YResolution (283) - type: rational (5) Tag Location: 70 - Data Location: 158 - value: b'\x00\x00\x00H\x00\x00\x00\x01'
2025-06-10 00:31:33,448 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Orientation (274) - type: short (3) - value: b'\x01\x00'
2025-06-10 00:31:33,448 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: XResolution (282) - type: rational (5) Tag Location: 82 - Data Location: 174 - value: b'H\x00\x00\x00\x01\x00\x00\x00'
2025-06-10 00:31:33,449 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Orientation (274) - type: short (3) - value: b'\x01\x00'
2025-06-10 00:31:33,449 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: XResolution (282) - type: rational (5) Tag Location: 82 - Data Location: 174 - value: b'H\x00\x00\x00\x01\x00\x00\x00'
2025-06-10 00:31:33,449 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: XResolution (282) - type: rational (5) Tag Location: 82 - Data Location: 174 - value: b'H\x00\x00\x00\x01\x00\x00\x00'
2025-06-10 00:31:33,449 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ResolutionUnit (296) - type: short (3) - value: b'\x00\x02'
2025-06-10 00:31:33,449 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: XResolution (282) - type: rational (5) Tag Location: 82 - Data Location: 174 - value: b'H\x00\x00\x00\x01\x00\x00\x00'
2025-06-10 00:31:33,449 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: YResolution (283) - type: rational (5) Tag Location: 94 - Data Location: 182 - value: b'H\x00\x00\x00\x01\x00\x00\x00'
2025-06-10 00:31:33,449 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: XResolution (282) - type: rational (5) Tag Location: 82 - Data Location: 174 - value: b'H\x00\x00\x00\x01\x00\x00\x00'
2025-06-10 00:31:33,449 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: YResolution (283) - type: rational (5) Tag Location: 94 - Data Location: 182 - value: b'H\x00\x00\x00\x01\x00\x00\x00'
2025-06-10 00:31:33,449 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: YResolution (283) - type: rational (5) Tag Location: 94 - Data Location: 182 - value: b'H\x00\x00\x00\x01\x00\x00\x00'
2025-06-10 00:31:33,449 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Software (305) - type: string (2) Tag Location: 94 - Data Location: 166 - value: b'ImageMeter\x00'
2025-06-10 00:31:33,450 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: YResolution (283) - type: rational (5) Tag Location: 94 - Data Location: 182 - value: b'H\x00\x00\x00\x01\x00\x00\x00'
2025-06-10 00:31:33,450 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ResolutionUnit (296) - type: short (3) - value: b'\x02\x00'
2025-06-10 00:31:33,450 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: YResolution (283) - type: rational (5) Tag Location: 94 - Data Location: 182 - value: b'H\x00\x00\x00\x01\x00\x00\x00'
2025-06-10 00:31:33,450 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ResolutionUnit (296) - type: short (3) - value: b'\x02\x00'
2025-06-10 00:31:33,450 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ResolutionUnit (296) - type: short (3) - value: b'\x02\x00'
2025-06-10 00:31:33,450 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DateTime (306) - type: string (2) Tag Location: 106 - Data Location: 177 - value: b'2025:03:08 16:06:23\x00'
2025-06-10 00:31:33,450 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ResolutionUnit (296) - type: short (3) - value: b'\x02\x00'
2025-06-10 00:31:33,451 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Software (305) - type: string (2) Tag Location: 118 - Data Location: 190 - value: b'ImageMeter\x00'
2025-06-10 00:31:33,451 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ResolutionUnit (296) - type: short (3) - value: b'\x02\x00'
2025-06-10 00:31:33,451 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Software (305) - type: string (2) Tag Location: 118 - Data Location: 190 - value: b'ImageMeter\x00'
2025-06-10 00:31:33,451 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Software (305) - type: string (2) Tag Location: 118 - Data Location: 190 - value: b'ImageMeter\x00'
2025-06-10 00:31:33,451 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: YCbCrPositioning (531) - type: short (3) - value: b'\x00\x01'
2025-06-10 00:31:33,451 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Software (305) - type: string (2) Tag Location: 118 - Data Location: 190 - value: b'ImageMeter\x00'
2025-06-10 00:31:33,451 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExifIFD (34665) - type: long (4) - value: b'\xca\x00\x00\x00'
2025-06-10 00:31:33,452 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Software (305) - type: string (2) Tag Location: 118 - Data Location: 190 - value: b'ImageMeter\x00'
2025-06-10 00:31:33,452 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExifIFD (34665) - type: long (4) - value: b'\xca\x00\x00\x00'
2025-06-10 00:31:33,452 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExifIFD (34665) - type: long (4) - value: b'\xca\x00\x00\x00'
2025-06-10 00:31:33,452 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExifIFD (34665) - type: long (4) - value: b'\x00\x00\x00\xc5'
2025-06-10 00:31:33,452 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExifIFD (34665) - type: long (4) - value: b'\xca\x00\x00\x00'
2025-06-10 00:31:33,453 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureTime (33434) - type: rational (5) Tag Location: 216 - Data Location: 560 - value: b'\x01\x00\x00\x00\x19\x00\x00\x00'
2025-06-10 00:31:33,453 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExifIFD (34665) - type: long (4) - value: b'\xca\x00\x00\x00'
2025-06-10 00:31:33,453 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureTime (33434) - type: rational (5) Tag Location: 216 - Data Location: 560 - value: b'\x01\x00\x00\x00!\x00\x00\x00'
2025-06-10 00:31:33,454 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureTime (33434) - type: rational (5) Tag Location: 216 - Data Location: 548 - value: b'\x01\x00\x00\x00\x14\x00\x00\x00'
2025-06-10 00:31:33,454 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureTime (33434) - type: rational (5) Tag Location: 211 - Data Location: 547 - value: b'\x00\x00\x00\x01\x00\x00\x002'
2025-06-10 00:31:33,454 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureTime (33434) - type: rational (5) Tag Location: 216 - Data Location: 548 - value: b'\x01\x00\x00\x00\x19\x00\x00\x00'
2025-06-10 00:31:33,454 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: FNumber (33437) - type: rational (5) Tag Location: 228 - Data Location: 568 - value: b'\xbe\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:31:33,455 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureTime (33434) - type: rational (5) Tag Location: 216 - Data Location: 560 - value: b'\x01\x00\x00\x002\x00\x00\x00'
2025-06-10 00:31:33,455 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: FNumber (33437) - type: rational (5) Tag Location: 228 - Data Location: 568 - value: b'\xbe\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:31:33,455 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: FNumber (33437) - type: rational (5) Tag Location: 228 - Data Location: 556 - value: b'\xbe\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:31:33,455 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: FNumber (33437) - type: rational (5) Tag Location: 223 - Data Location: 555 - value: b'\x00\x00\x00\xbe\x00\x00\x00d'
2025-06-10 00:31:33,455 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: FNumber (33437) - type: rational (5) Tag Location: 228 - Data Location: 556 - value: b'\xbe\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:31:33,456 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureProgram (34850) - type: short (3) - value: b'\x02\x00'
2025-06-10 00:31:33,456 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: FNumber (33437) - type: rational (5) Tag Location: 228 - Data Location: 568 - value: b'\xbe\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:31:33,456 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureProgram (34850) - type: short (3) - value: b'\x02\x00'
2025-06-10 00:31:33,456 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureProgram (34850) - type: short (3) - value: b'\x02\x00'
2025-06-10 00:31:33,456 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureProgram (34850) - type: short (3) - value: b'\x00\x02'
2025-06-10 00:31:33,456 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureProgram (34850) - type: short (3) - value: b'\x02\x00'
2025-06-10 00:31:33,457 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ISOSpeedRatings (34855) - type: short (3) - value: b'\xa0\x00'
2025-06-10 00:31:33,457 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureProgram (34850) - type: short (3) - value: b'\x02\x00'
2025-06-10 00:31:33,457 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ISOSpeedRatings (34855) - type: short (3) - value: b'\xa0\x00'
2025-06-10 00:31:33,457 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ISOSpeedRatings (34855) - type: short (3) - value: b'\x90\x01'
2025-06-10 00:31:33,457 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ISOSpeedRatings (34855) - type: short (3) - value: b'\x00}'
2025-06-10 00:31:33,457 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ISOSpeedRatings (34855) - type: short (3) - value: b'\xfa\x00'
2025-06-10 00:31:33,457 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExifVersion (36864) - type: undefined (7) - value: b'0220'
2025-06-10 00:31:33,458 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ISOSpeedRatings (34855) - type: short (3) - value: b'd\x00'
2025-06-10 00:31:33,458 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExifVersion (36864) - type: undefined (7) - value: b'0220'
2025-06-10 00:31:33,458 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExifVersion (36864) - type: undefined (7) - value: b'0220'
2025-06-10 00:31:33,458 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExifVersion (36864) - type: undefined (7) - value: b'0220'
2025-06-10 00:31:33,458 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExifVersion (36864) - type: undefined (7) - value: b'0220'
2025-06-10 00:31:33,458 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DateTimeOriginal (36867) - type: string (2) Tag Location: 276 - Data Location: 576 - value: b'2025:03:08 16:15:15\x00'
2025-06-10 00:31:33,459 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExifVersion (36864) - type: undefined (7) - value: b'0220'
2025-06-10 00:31:33,459 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DateTimeOriginal (36867) - type: string (2) Tag Location: 276 - Data Location: 576 - value: b'2025:03:08 16:05:29\x00'
2025-06-10 00:31:33,459 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DateTimeOriginal (36867) - type: string (2) Tag Location: 276 - Data Location: 564 - value: b'2025:03:08 16:07:42\x00'
2025-06-10 00:31:33,459 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DateTimeOriginal (36867) - type: string (2) Tag Location: 271 - Data Location: 563 - value: b'2025:03:08 16:06:23\x00'
2025-06-10 00:31:33,459 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DateTimeOriginal (36867) - type: string (2) Tag Location: 276 - Data Location: 564 - value: b'2025:03:08 15:53:05\x00'
2025-06-10 00:31:33,459 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DateTimeDigitized (36868) - type: string (2) Tag Location: 288 - Data Location: 596 - value: b'2025:03:08 16:15:15\x00'
2025-06-10 00:31:33,460 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DateTimeOriginal (36867) - type: string (2) Tag Location: 276 - Data Location: 576 - value: b'2025:03:08 16:05:44\x00'
2025-06-10 00:31:33,460 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DateTimeDigitized (36868) - type: string (2) Tag Location: 288 - Data Location: 596 - value: b'2025:03:08 16:05:29\x00'
2025-06-10 00:31:33,460 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DateTimeDigitized (36868) - type: string (2) Tag Location: 288 - Data Location: 584 - value: b'2025:03:08 16:07:42\x00'
2025-06-10 00:31:33,460 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DateTimeDigitized (36868) - type: string (2) Tag Location: 283 - Data Location: 583 - value: b'2025:03:08 16:06:23\x00'
2025-06-10 00:31:33,460 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DateTimeDigitized (36868) - type: string (2) Tag Location: 288 - Data Location: 584 - value: b'2025:03:08 15:53:05\x00'
2025-06-10 00:31:33,461 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: unknown (36880) - type: string (2) Tag Location: 300 - Data Location: 604 - value: b'+01:00\x00'
2025-06-10 00:31:33,461 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DateTimeDigitized (36868) - type: string (2) Tag Location: 288 - Data Location: 596 - value: b'2025:03:08 16:05:44\x00'
2025-06-10 00:31:33,461 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: unknown (36880) - type: string (2) Tag Location: 300 - Data Location: 616 - value: b'+01:00\x00'
2025-06-10 00:31:33,461 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: unknown (36880) - type: string (2) Tag Location: 300 - Data Location: 604 - value: b'+01:00\x00'
2025-06-10 00:31:33,461 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: unknown (36880) - type: string (2) Tag Location: 295 - Data Location: 603 - value: b'+01:00\x00'
2025-06-10 00:31:33,461 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: unknown (36880) - type: string (2) Tag Location: 300 - Data Location: 616 - value: b'+01:00\x00'
2025-06-10 00:31:33,461 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: unknown (36881) - type: string (2) Tag Location: 312 - Data Location: 612 - value: b'+01:00\x00'
2025-06-10 00:31:33,461 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: unknown (36880) - type: string (2) Tag Location: 300 - Data Location: 616 - value: b'+01:00\x00'
2025-06-10 00:31:33,462 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: unknown (36881) - type: string (2) Tag Location: 312 - Data Location: 624 - value: b'+01:00\x00'
2025-06-10 00:31:33,462 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: unknown (36881) - type: string (2) Tag Location: 312 - Data Location: 612 - value: b'+01:00\x00'
2025-06-10 00:31:33,462 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: unknown (36881) - type: string (2) Tag Location: 307 - Data Location: 610 - value: b'+01:00\x00'
2025-06-10 00:31:33,462 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: unknown (36881) - type: string (2) Tag Location: 312 - Data Location: 624 - value: b'+01:00\x00'
2025-06-10 00:31:33,462 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ShutterSpeedValue (37377) - type: rational (5) Tag Location: 324 - Data Location: 620 - value: b'\x01\x00\x00\x00\x19\x00\x00\x00'
2025-06-10 00:31:33,463 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: unknown (36881) - type: string (2) Tag Location: 312 - Data Location: 624 - value: b'+01:00\x00'
2025-06-10 00:31:33,463 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ShutterSpeedValue (37377) - type: rational (5) Tag Location: 324 - Data Location: 632 - value: b'\x01\x00\x00\x00!\x00\x00\x00'
2025-06-10 00:31:33,463 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ShutterSpeedValue (37377) - type: rational (5) Tag Location: 324 - Data Location: 620 - value: b'\x01\x00\x00\x00\x14\x00\x00\x00'
2025-06-10 00:31:33,463 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ShutterSpeedValue (37377) - type: signed rational (10) Tag Location: 319 - Data Location: 617 - value: b'\x00\x00\x00\x01\x00\x00\x002'
2025-06-10 00:31:33,463 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ShutterSpeedValue (37377) - type: rational (5) Tag Location: 324 - Data Location: 632 - value: b'\x01\x00\x00\x00\x19\x00\x00\x00'
2025-06-10 00:31:33,463 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ApertureValue (37378) - type: rational (5) Tag Location: 336 - Data Location: 628 - value: b'\xb9\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:31:33,463 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ShutterSpeedValue (37377) - type: rational (5) Tag Location: 324 - Data Location: 632 - value: b'\x01\x00\x00\x002\x00\x00\x00'
2025-06-10 00:31:33,464 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ApertureValue (37378) - type: rational (5) Tag Location: 336 - Data Location: 640 - value: b'\xb9\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:31:33,464 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ApertureValue (37378) - type: rational (5) Tag Location: 336 - Data Location: 628 - value: b'\xb9\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:31:33,464 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ApertureValue (37378) - type: rational (5) Tag Location: 331 - Data Location: 625 - value: b'\x00\x00\x00\xb9\x00\x00\x00d'
2025-06-10 00:31:33,464 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ApertureValue (37378) - type: rational (5) Tag Location: 336 - Data Location: 640 - value: b'\xb9\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:31:33,464 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureBiasValue (37380) - type: signed rational (10) Tag Location: 348 - Data Location: 636 - value: b'\x00\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:31:33,464 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ApertureValue (37378) - type: rational (5) Tag Location: 336 - Data Location: 640 - value: b'\xb9\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:31:33,464 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: BrightnessValue (37379) - type: signed rational (10) Tag Location: 348 - Data Location: 648 - value: b'b\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:31:33,465 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureBiasValue (37380) - type: signed rational (10) Tag Location: 360 - Data Location: 656 - value: b'\x00\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:31:33,465 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: MaxApertureValue (37381) - type: rational (5) Tag Location: 372 - Data Location: 664 - value: b'\xb9\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:31:33,465 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: MeteringMode (37383) - type: short (3) - value: b'\x02\x00'
2025-06-10 00:31:33,465 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: MaxApertureValue (37381) - type: rational (5) Tag Location: 360 - Data Location: 644 - value: b'\xb9\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:31:33,465 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: BrightnessValue (37379) - type: signed rational (10) Tag Location: 348 - Data Location: 648 - value: b'\xe7\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:31:33,465 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureBiasValue (37380) - type: signed rational (10) Tag Location: 348 - Data Location: 636 - value: b'\x00\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:31:33,466 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: BrightnessValue (37379) - type: signed rational (10) Tag Location: 343 - Data Location: 633 - value: b'\x00\x00\x00\xb6\x00\x00\x00d'
2025-06-10 00:31:33,466 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: BrightnessValue (37379) - type: signed rational (10) Tag Location: 348 - Data Location: 648 - value: b'.\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:31:33,466 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Flash (37385) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:31:33,466 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: MeteringMode (37383) - type: short (3) - value: b'\x02\x00'
2025-06-10 00:31:33,466 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureBiasValue (37380) - type: signed rational (10) Tag Location: 360 - Data Location: 656 - value: b'\x00\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:31:33,466 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: MaxApertureValue (37381) - type: rational (5) Tag Location: 360 - Data Location: 644 - value: b'\xb9\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:31:33,466 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureBiasValue (37380) - type: signed rational (10) Tag Location: 355 - Data Location: 641 - value: b'\x00\x00\x00\x00\x00\x00\x00d'
2025-06-10 00:31:33,467 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureBiasValue (37380) - type: signed rational (10) Tag Location: 360 - Data Location: 656 - value: b'\x00\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:31:33,467 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: FocalLength (37386) - type: rational (5) Tag Location: 408 - Data Location: 672 - value: b'#\x01\x00\x00d\x00\x00\x00'
2025-06-10 00:31:33,467 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Flash (37385) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:31:33,467 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: MaxApertureValue (37381) - type: rational (5) Tag Location: 372 - Data Location: 664 - value: b'\xb9\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:31:33,467 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: MeteringMode (37383) - type: short (3) - value: b'\x02\x00'
2025-06-10 00:31:33,467 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: MaxApertureValue (37381) - type: rational (5) Tag Location: 367 - Data Location: 649 - value: b'\x00\x00\x00\xb9\x00\x00\x00d'
2025-06-10 00:31:33,467 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: MaxApertureValue (37381) - type: rational (5) Tag Location: 372 - Data Location: 664 - value: b'\xb9\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:31:33,467 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SubSec (37520) - type: string (2) - value: b'970\x00'
2025-06-10 00:31:33,467 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: FocalLength (37386) - type: rational (5) Tag Location: 396 - Data Location: 652 - value: b'#\x01\x00\x00d\x00\x00\x00'
2025-06-10 00:31:33,468 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: MeteringMode (37383) - type: short (3) - value: b'\x02\x00'
2025-06-10 00:31:33,468 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Flash (37385) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:31:33,468 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: MeteringMode (37383) - type: short (3) - value: b'\x00\x02'
2025-06-10 00:31:33,468 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: MeteringMode (37383) - type: short (3) - value: b'\x02\x00'
2025-06-10 00:31:33,468 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SubSecTimeOriginal (37521) - type: string (2) - value: b'970\x00'
2025-06-10 00:31:33,468 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SubSec (37520) - type: string (2) - value: b'593\x00'
2025-06-10 00:31:33,468 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Flash (37385) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:31:33,469 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: FocalLength (37386) - type: rational (5) Tag Location: 396 - Data Location: 652 - value: b'#\x01\x00\x00d\x00\x00\x00'
2025-06-10 00:31:33,469 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Flash (37385) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:31:33,469 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Flash (37385) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:31:33,469 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SubsecTimeDigitized (37522) - type: string (2) - value: b'970\x00'
2025-06-10 00:31:33,469 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SubSecTimeOriginal (37521) - type: string (2) - value: b'593\x00'
2025-06-10 00:31:33,469 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: FocalLength (37386) - type: rational (5) Tag Location: 408 - Data Location: 672 - value: b'#\x01\x00\x00d\x00\x00\x00'
2025-06-10 00:31:33,469 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SubSec (37520) - type: string (2) - value: b'598\x00'
2025-06-10 00:31:33,470 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: FocalLength (37386) - type: rational (5) Tag Location: 403 - Data Location: 657 - value: b'\x00\x00\x01#\x00\x00\x00d'
2025-06-10 00:31:33,470 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: FocalLength (37386) - type: rational (5) Tag Location: 408 - Data Location: 672 - value: b'#\x01\x00\x00d\x00\x00\x00'
2025-06-10 00:31:33,470 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ColorSpace (40961) - type: short (3) - value: b'\x01\x00'
2025-06-10 00:31:33,470 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SubsecTimeDigitized (37522) - type: string (2) - value: b'593\x00'
2025-06-10 00:31:33,470 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SubSec (37520) - type: string (2) - value: b'323\x00'
2025-06-10 00:31:33,470 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SubSecTimeOriginal (37521) - type: string (2) - value: b'598\x00'
2025-06-10 00:31:33,470 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SubSec (37520) - type: string (2) - value: b'800\x00'
2025-06-10 00:31:33,470 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SubSec (37520) - type: string (2) - value: b'502\x00'
2025-06-10 00:31:33,471 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: PixelXDimension (40962) - type: long (4) - value: b'\xc0\x0c\x00\x00'
2025-06-10 00:31:33,471 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ColorSpace (40961) - type: short (3) - value: b'\x01\x00'
2025-06-10 00:31:33,471 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SubSecTimeOriginal (37521) - type: string (2) - value: b'323\x00'
2025-06-10 00:31:33,471 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SubsecTimeDigitized (37522) - type: string (2) - value: b'598\x00'
2025-06-10 00:31:33,471 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SubSecTimeOriginal (37521) - type: string (2) - value: b'800\x00'
2025-06-10 00:31:33,471 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SubSecTimeOriginal (37521) - type: string (2) - value: b'502\x00'
2025-06-10 00:31:33,471 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: PixelYDimension (40963) - type: long (4) - value: b'\xa4\x07\x00\x00'
2025-06-10 00:31:33,471 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: PixelXDimension (40962) - type: long (4) - value: b'\xc0\x0c\x00\x00'
2025-06-10 00:31:33,471 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SubsecTimeDigitized (37522) - type: string (2) - value: b'323\x00'
2025-06-10 00:31:33,472 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ColorSpace (40961) - type: short (3) - value: b'\x01\x00'
2025-06-10 00:31:33,472 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SubsecTimeDigitized (37522) - type: string (2) - value: b'800\x00'
2025-06-10 00:31:33,472 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SubsecTimeDigitized (37522) - type: string (2) - value: b'502\x00'
2025-06-10 00:31:33,472 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureMode (41986) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:31:33,472 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: PixelYDimension (40963) - type: long (4) - value: b'\xa4\x07\x00\x00'
2025-06-10 00:31:33,472 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ColorSpace (40961) - type: short (3) - value: b'\x01\x00'
2025-06-10 00:31:33,472 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: PixelXDimension (40962) - type: long (4) - value: b'\xc0\x0c\x00\x00'
2025-06-10 00:31:33,472 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ColorSpace (40961) - type: short (3) - value: b'\x00\x01'
2025-06-10 00:31:33,472 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ColorSpace (40961) - type: short (3) - value: b'\x01\x00'
2025-06-10 00:31:33,473 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: WhiteBalance (41987) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:31:33,473 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureMode (41986) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:31:33,473 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: PixelXDimension (40962) - type: long (4) - value: b'\xc0\x0c\x00\x00'
2025-06-10 00:31:33,473 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: PixelYDimension (40963) - type: long (4) - value: b'\xa4\x07\x00\x00'
2025-06-10 00:31:33,473 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: PixelXDimension (40962) - type: long (4) - value: b'\x00\x00\x0c\xc0'
2025-06-10 00:31:33,473 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: PixelXDimension (40962) - type: long (4) - value: b'\xc0\x0c\x00\x00'
2025-06-10 00:31:33,473 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DigitalZoomRatio (41988) - type: rational (5) Tag Location: 516 - Data Location: 680 - value: b'd\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:31:33,473 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: WhiteBalance (41987) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:31:33,474 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: PixelYDimension (40963) - type: long (4) - value: b'\xa4\x07\x00\x00'
2025-06-10 00:31:33,474 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureMode (41986) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:31:33,474 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: PixelYDimension (40963) - type: long (4) - value: b'\x00\x00\x07\xa4'
2025-06-10 00:31:33,474 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: PixelYDimension (40963) - type: long (4) - value: b'\xa4\x07\x00\x00'
2025-06-10 00:31:33,474 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: FocalLengthIn35mmFilm (41989) - type: short (3) - value: b'\x1b\x00'
2025-06-10 00:31:33,474 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DigitalZoomRatio (41988) - type: rational (5) Tag Location: 504 - Data Location: 660 - value: b'd\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:31:33,474 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureMode (41986) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:31:33,474 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: WhiteBalance (41987) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:31:33,474 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureMode (41986) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:31:33,475 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureMode (41986) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:31:33,475 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SceneCaptureType (41990) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:31:33,475 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: FocalLengthIn35mmFilm (41989) - type: short (3) - value: b'\x1b\x00'
2025-06-10 00:31:33,475 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: WhiteBalance (41987) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:31:33,475 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DigitalZoomRatio (41988) - type: rational (5) Tag Location: 504 - Data Location: 660 - value: b'd\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:31:33,475 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: WhiteBalance (41987) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:31:33,476 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: WhiteBalance (41987) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:31:33,476 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ImageUniqueID (42016) - type: string (2) Tag Location: 552 - Data Location: 688 - value: b'G08LLMJ00HM\x00'
2025-06-10 00:31:33,476 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SceneCaptureType (41990) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:31:33,476 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DigitalZoomRatio (41988) - type: rational (5) Tag Location: 516 - Data Location: 680 - value: b'd\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:31:33,476 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: FocalLengthIn35mmFilm (41989) - type: short (3) - value: b'\x1b\x00'
2025-06-10 00:31:33,476 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DigitalZoomRatio (41988) - type: rational (5) Tag Location: 511 - Data Location: 665 - value: b'\x00\x00\x00d\x00\x00\x00d'
2025-06-10 00:31:33,477 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DigitalZoomRatio (41988) - type: rational (5) Tag Location: 516 - Data Location: 680 - value: b'd\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:31:33,477 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ImageUniqueID (42016) - type: string (2) Tag Location: 540 - Data Location: 668 - value: b'G08LLMJ00HM\x00'
2025-06-10 00:31:33,478 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: FocalLengthIn35mmFilm (41989) - type: short (3) - value: b'\x1b\x00'
2025-06-10 00:31:33,478 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SceneCaptureType (41990) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:31:33,478 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: FocalLengthIn35mmFilm (41989) - type: short (3) - value: b'\x00\x1b'
2025-06-10 00:31:33,478 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: FocalLengthIn35mmFilm (41989) - type: short (3) - value: b'\x1b\x00'
2025-06-10 00:31:33,479 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SceneCaptureType (41990) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:31:33,479 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ImageUniqueID (42016) - type: string (2) Tag Location: 540 - Data Location: 668 - value: b'G08LLMJ00HM\x00'
2025-06-10 00:31:33,479 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SceneCaptureType (41990) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:31:33,479 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SceneCaptureType (41990) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:31:33,479 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ImageUniqueID (42016) - type: string (2) Tag Location: 552 - Data Location: 688 - value: b'G08LLMJ00HM\x00'
2025-06-10 00:31:33,480 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ImageUniqueID (42016) - type: string (2) Tag Location: 547 - Data Location: 673 - value: b'G08LLMJ00HM\x00'
2025-06-10 00:31:33,480 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ImageUniqueID (42016) - type: string (2) Tag Location: 552 - Data Location: 688 - value: b'G08LLMJ00HM\x00'
2025-06-10 00:31:33,489 | INFO     | PhotoRenamer.ImageLoader | on_thumbnail_ready:110 | Caricamento completato: 6 immagini elaborate
2025-06-10 00:31:33,489 | DEBUG    | PhotoRenamer.LeftPanel | _on_loading_finished:680 | [LP._on_loading_finished] Thumbnail per ph_1.jpg: tipo <class 'PySide6.QtGui.QPixmap'>, isNull: False, size: PySide6.QtCore.QSize(100, 60)
2025-06-10 00:31:33,489 | DEBUG    | PhotoRenamer.LeftPanel | _on_loading_finished:683 | [LP._on_loading_finished] Icona impostata per ph_1.jpg: icon.isNull: False, icon.availableSizes: [PySide6.QtCore.QSize(100, 60)]
2025-06-10 00:31:33,490 | DEBUG    | PhotoRenamer.LeftPanel | _on_loading_finished:680 | [LP._on_loading_finished] Thumbnail per ph_2.jpg: tipo <class 'PySide6.QtGui.QPixmap'>, isNull: False, size: PySide6.QtCore.QSize(100, 60)
2025-06-10 00:31:33,490 | DEBUG    | PhotoRenamer.LeftPanel | _on_loading_finished:683 | [LP._on_loading_finished] Icona impostata per ph_2.jpg: icon.isNull: False, icon.availableSizes: [PySide6.QtCore.QSize(100, 60)]
2025-06-10 00:31:33,490 | DEBUG    | PhotoRenamer.LeftPanel | _on_loading_finished:680 | [LP._on_loading_finished] Thumbnail per ph_3.jpg: tipo <class 'PySide6.QtGui.QPixmap'>, isNull: False, size: PySide6.QtCore.QSize(100, 58)
2025-06-10 00:31:33,490 | DEBUG    | PhotoRenamer.LeftPanel | _on_loading_finished:683 | [LP._on_loading_finished] Icona impostata per ph_3.jpg: icon.isNull: False, icon.availableSizes: [PySide6.QtCore.QSize(100, 58)]
2025-06-10 00:31:33,490 | DEBUG    | PhotoRenamer.LeftPanel | _on_loading_finished:680 | [LP._on_loading_finished] Thumbnail per ph_4.jpg: tipo <class 'PySide6.QtGui.QPixmap'>, isNull: False, size: PySide6.QtCore.QSize(100, 60)
2025-06-10 00:31:33,491 | DEBUG    | PhotoRenamer.LeftPanel | _on_loading_finished:683 | [LP._on_loading_finished] Icona impostata per ph_4.jpg: icon.isNull: False, icon.availableSizes: [PySide6.QtCore.QSize(100, 60)]
2025-06-10 00:31:33,491 | DEBUG    | PhotoRenamer.LeftPanel | _on_loading_finished:680 | [LP._on_loading_finished] Thumbnail per ph_5.jpg: tipo <class 'PySide6.QtGui.QPixmap'>, isNull: False, size: PySide6.QtCore.QSize(100, 60)
2025-06-10 00:31:33,491 | DEBUG    | PhotoRenamer.LeftPanel | _on_loading_finished:683 | [LP._on_loading_finished] Icona impostata per ph_5.jpg: icon.isNull: False, icon.availableSizes: [PySide6.QtCore.QSize(100, 60)]
2025-06-10 00:31:33,491 | DEBUG    | PhotoRenamer.LeftPanel | _on_loading_finished:680 | [LP._on_loading_finished] Thumbnail per ph_6.jpg: tipo <class 'PySide6.QtGui.QPixmap'>, isNull: False, size: PySide6.QtCore.QSize(100, 60)
2025-06-10 00:31:33,491 | DEBUG    | PhotoRenamer.LeftPanel | _on_loading_finished:683 | [LP._on_loading_finished] Icona impostata per ph_6.jpg: icon.isNull: False, icon.availableSizes: [PySide6.QtCore.QSize(100, 60)]
2025-06-10 00:31:33,491 | INFO     | PhotoDrop.ImageListController | set_image_list:41 | ImageListController: lista immagini aggiornata (6)
2025-06-10 00:31:33,492 | INFO     | PhotoDrop.RightPanel | set_image_list:291 | RightPanel: impostata lista di 6 immagini
2025-06-10 00:31:33,492 | INFO     | PhotoDrop.MainController | on_images_loaded:51 | Caricate 6 immagini
2025-06-10 00:31:33,492 | INFO     | PhotoRenamer.LeftPanel | _on_loading_finished:697 | Caricamento immagini completato e lista ordinata: 6 immagini
2025-06-10 00:31:37,541 | DEBUG    | PhotoRenamer.LeftPanel | _on_current_item_changed:1121 | [LP._on_current_item_changed] Chiamato con current: ph_3.jpg, previous: None
2025-06-10 00:31:37,542 | DEBUG    | PhotoRenamer.LeftPanel | _on_current_item_changed:1126 | [LP._on_current_item_changed] Emitting selection_changed with path: C:/Users/<USER>/Desktop/foto2\ph_3.jpg
2025-06-10 00:31:37,555 | INFO     | PhotoDrop.MetadataController | load_metadata:35 | Estrazione metadati EXIF per: C:\Users\<USER>\Desktop\foto2\ph_3.jpg
2025-06-10 00:31:37,555 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: YCbCrPositioning (531) - type: short (3) - value: b'\x01\x00'
2025-06-10 00:31:37,556 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DateTime (306) - type: string (2) Tag Location: 34 - Data Location: 138 - value: b'2025:03:08 16:05:29\x00'
2025-06-10 00:31:37,556 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Make (271) - type: string (2) Tag Location: 46 - Data Location: 158 - value: b'samsung\x00'
2025-06-10 00:31:37,556 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Model (272) - type: string (2) Tag Location: 58 - Data Location: 166 - value: b'SM-P615\x00'
2025-06-10 00:31:37,556 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Orientation (274) - type: short (3) - value: b'\x01\x00'
2025-06-10 00:31:37,557 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: XResolution (282) - type: rational (5) Tag Location: 82 - Data Location: 174 - value: b'H\x00\x00\x00\x01\x00\x00\x00'
2025-06-10 00:31:37,557 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: YResolution (283) - type: rational (5) Tag Location: 94 - Data Location: 182 - value: b'H\x00\x00\x00\x01\x00\x00\x00'
2025-06-10 00:31:37,557 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ResolutionUnit (296) - type: short (3) - value: b'\x02\x00'
2025-06-10 00:31:37,557 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Software (305) - type: string (2) Tag Location: 118 - Data Location: 190 - value: b'ImageMeter\x00'
2025-06-10 00:31:37,558 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExifIFD (34665) - type: long (4) - value: b'\xca\x00\x00\x00'
2025-06-10 00:31:37,558 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureTime (33434) - type: rational (5) Tag Location: 216 - Data Location: 560 - value: b'\x01\x00\x00\x00!\x00\x00\x00'
2025-06-10 00:31:37,558 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: FNumber (33437) - type: rational (5) Tag Location: 228 - Data Location: 568 - value: b'\xbe\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:31:37,559 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureProgram (34850) - type: short (3) - value: b'\x02\x00'
2025-06-10 00:31:37,559 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ISOSpeedRatings (34855) - type: short (3) - value: b'\xa0\x00'
2025-06-10 00:31:37,559 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExifVersion (36864) - type: undefined (7) - value: b'0220'
2025-06-10 00:31:37,559 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DateTimeOriginal (36867) - type: string (2) Tag Location: 276 - Data Location: 576 - value: b'2025:03:08 16:05:29\x00'
2025-06-10 00:31:37,560 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DateTimeDigitized (36868) - type: string (2) Tag Location: 288 - Data Location: 596 - value: b'2025:03:08 16:05:29\x00'
2025-06-10 00:31:37,560 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: unknown (36880) - type: string (2) Tag Location: 300 - Data Location: 616 - value: b'+01:00\x00'
2025-06-10 00:31:37,560 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: unknown (36881) - type: string (2) Tag Location: 312 - Data Location: 624 - value: b'+01:00\x00'
2025-06-10 00:31:37,560 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ShutterSpeedValue (37377) - type: rational (5) Tag Location: 324 - Data Location: 632 - value: b'\x01\x00\x00\x00!\x00\x00\x00'
2025-06-10 00:31:37,560 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ApertureValue (37378) - type: rational (5) Tag Location: 336 - Data Location: 640 - value: b'\xb9\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:31:37,560 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: BrightnessValue (37379) - type: signed rational (10) Tag Location: 348 - Data Location: 648 - value: b'b\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:31:37,560 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureBiasValue (37380) - type: signed rational (10) Tag Location: 360 - Data Location: 656 - value: b'\x00\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:31:37,561 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: MaxApertureValue (37381) - type: rational (5) Tag Location: 372 - Data Location: 664 - value: b'\xb9\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:31:37,561 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: MeteringMode (37383) - type: short (3) - value: b'\x02\x00'
2025-06-10 00:31:37,561 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Flash (37385) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:31:37,561 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: FocalLength (37386) - type: rational (5) Tag Location: 408 - Data Location: 672 - value: b'#\x01\x00\x00d\x00\x00\x00'
2025-06-10 00:31:37,561 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SubSec (37520) - type: string (2) - value: b'970\x00'
2025-06-10 00:31:37,561 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SubSecTimeOriginal (37521) - type: string (2) - value: b'970\x00'
2025-06-10 00:31:37,561 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SubsecTimeDigitized (37522) - type: string (2) - value: b'970\x00'
2025-06-10 00:31:37,562 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ColorSpace (40961) - type: short (3) - value: b'\x01\x00'
2025-06-10 00:31:37,562 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: PixelXDimension (40962) - type: long (4) - value: b'\xc0\x0c\x00\x00'
2025-06-10 00:31:37,562 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: PixelYDimension (40963) - type: long (4) - value: b'\xa4\x07\x00\x00'
2025-06-10 00:31:37,562 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureMode (41986) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:31:37,562 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: WhiteBalance (41987) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:31:37,562 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DigitalZoomRatio (41988) - type: rational (5) Tag Location: 516 - Data Location: 680 - value: b'd\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:31:37,562 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: FocalLengthIn35mmFilm (41989) - type: short (3) - value: b'\x1b\x00'
2025-06-10 00:31:37,562 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SceneCaptureType (41990) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:31:37,562 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ImageUniqueID (42016) - type: string (2) Tag Location: 552 - Data Location: 688 - value: b'G08LLMJ00HM\x00'
2025-06-10 00:31:37,563 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureTime (33434) - type: rational (5) Tag Location: 216 - Data Location: 560 - value: b'\x01\x00\x00\x00!\x00\x00\x00'
2025-06-10 00:31:37,563 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: FNumber (33437) - type: rational (5) Tag Location: 228 - Data Location: 568 - value: b'\xbe\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:31:37,563 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureProgram (34850) - type: short (3) - value: b'\x02\x00'
2025-06-10 00:31:37,563 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ISOSpeedRatings (34855) - type: short (3) - value: b'\xa0\x00'
2025-06-10 00:31:37,563 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExifVersion (36864) - type: undefined (7) - value: b'0220'
2025-06-10 00:31:37,563 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DateTimeOriginal (36867) - type: string (2) Tag Location: 276 - Data Location: 576 - value: b'2025:03:08 16:05:29\x00'
2025-06-10 00:31:37,563 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DateTimeDigitized (36868) - type: string (2) Tag Location: 288 - Data Location: 596 - value: b'2025:03:08 16:05:29\x00'
2025-06-10 00:31:37,564 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: unknown (36880) - type: string (2) Tag Location: 300 - Data Location: 616 - value: b'+01:00\x00'
2025-06-10 00:31:37,564 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: unknown (36881) - type: string (2) Tag Location: 312 - Data Location: 624 - value: b'+01:00\x00'
2025-06-10 00:31:37,564 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ShutterSpeedValue (37377) - type: rational (5) Tag Location: 324 - Data Location: 632 - value: b'\x01\x00\x00\x00!\x00\x00\x00'
2025-06-10 00:31:37,564 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ApertureValue (37378) - type: rational (5) Tag Location: 336 - Data Location: 640 - value: b'\xb9\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:31:37,564 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: BrightnessValue (37379) - type: signed rational (10) Tag Location: 348 - Data Location: 648 - value: b'b\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:31:37,564 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureBiasValue (37380) - type: signed rational (10) Tag Location: 360 - Data Location: 656 - value: b'\x00\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:31:37,564 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: MaxApertureValue (37381) - type: rational (5) Tag Location: 372 - Data Location: 664 - value: b'\xb9\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:31:37,564 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: MeteringMode (37383) - type: short (3) - value: b'\x02\x00'
2025-06-10 00:31:37,564 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Flash (37385) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:31:37,564 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: FocalLength (37386) - type: rational (5) Tag Location: 408 - Data Location: 672 - value: b'#\x01\x00\x00d\x00\x00\x00'
2025-06-10 00:31:37,565 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SubSec (37520) - type: string (2) - value: b'970\x00'
2025-06-10 00:31:37,565 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SubSecTimeOriginal (37521) - type: string (2) - value: b'970\x00'
2025-06-10 00:31:37,565 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SubsecTimeDigitized (37522) - type: string (2) - value: b'970\x00'
2025-06-10 00:31:37,565 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ColorSpace (40961) - type: short (3) - value: b'\x01\x00'
2025-06-10 00:31:37,565 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: PixelXDimension (40962) - type: long (4) - value: b'\xc0\x0c\x00\x00'
2025-06-10 00:31:37,565 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: PixelYDimension (40963) - type: long (4) - value: b'\xa4\x07\x00\x00'
2025-06-10 00:31:37,565 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureMode (41986) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:31:37,565 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: WhiteBalance (41987) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:31:37,565 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DigitalZoomRatio (41988) - type: rational (5) Tag Location: 516 - Data Location: 680 - value: b'd\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:31:37,565 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: FocalLengthIn35mmFilm (41989) - type: short (3) - value: b'\x1b\x00'
2025-06-10 00:31:37,565 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SceneCaptureType (41990) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:31:37,565 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ImageUniqueID (42016) - type: string (2) Tag Location: 552 - Data Location: 688 - value: b'G08LLMJ00HM\x00'
2025-06-10 00:31:37,572 | DEBUG    | PhotoDrop.MetadataController | load_metadata:42 | Metadati EXIF estratti: {'Marca': 'samsung', 'Modello': 'SM-P615', 'Data e ora': '08/03/2025 16:05:29', 'Compensazione esposizione': '0.0', 'Modalità di misurazione': '2', 'Flash': '0', 'Lunghezza focale': '2.91', 'Tempo di esposizione': '0.030303030303030304 sec', 'Diaframma': 'f/1.9', 'ISO': '160', 'Bilanciamento del bianco': '0', 'Dimensione file': '193.3 KB', 'Nome file': 'ph_3.jpg'}
2025-06-10 00:31:37,572 | INFO     | PhotoDrop.MainWindow | _on_selection_changed:258 | MainWindow: _on_selection_changed ha chiamato right_panel.show_image con ph_3.jpg
2025-06-10 00:31:37,693 | INFO     | PhotoDrop.MetadataController | load_metadata:35 | Estrazione metadati EXIF per: C:\Users\<USER>\Desktop\foto2\ph_3.jpg
2025-06-10 00:31:37,693 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: YCbCrPositioning (531) - type: short (3) - value: b'\x01\x00'
2025-06-10 00:31:37,693 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DateTime (306) - type: string (2) Tag Location: 34 - Data Location: 138 - value: b'2025:03:08 16:05:29\x00'
2025-06-10 00:31:37,694 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Make (271) - type: string (2) Tag Location: 46 - Data Location: 158 - value: b'samsung\x00'
2025-06-10 00:31:37,694 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Model (272) - type: string (2) Tag Location: 58 - Data Location: 166 - value: b'SM-P615\x00'
2025-06-10 00:31:37,694 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Orientation (274) - type: short (3) - value: b'\x01\x00'
2025-06-10 00:31:37,694 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: XResolution (282) - type: rational (5) Tag Location: 82 - Data Location: 174 - value: b'H\x00\x00\x00\x01\x00\x00\x00'
2025-06-10 00:31:37,695 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: YResolution (283) - type: rational (5) Tag Location: 94 - Data Location: 182 - value: b'H\x00\x00\x00\x01\x00\x00\x00'
2025-06-10 00:31:37,695 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ResolutionUnit (296) - type: short (3) - value: b'\x02\x00'
2025-06-10 00:31:37,695 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Software (305) - type: string (2) Tag Location: 118 - Data Location: 190 - value: b'ImageMeter\x00'
2025-06-10 00:31:37,695 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExifIFD (34665) - type: long (4) - value: b'\xca\x00\x00\x00'
2025-06-10 00:31:37,695 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureTime (33434) - type: rational (5) Tag Location: 216 - Data Location: 560 - value: b'\x01\x00\x00\x00!\x00\x00\x00'
2025-06-10 00:31:37,696 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: FNumber (33437) - type: rational (5) Tag Location: 228 - Data Location: 568 - value: b'\xbe\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:31:37,696 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureProgram (34850) - type: short (3) - value: b'\x02\x00'
2025-06-10 00:31:37,696 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ISOSpeedRatings (34855) - type: short (3) - value: b'\xa0\x00'
2025-06-10 00:31:37,696 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExifVersion (36864) - type: undefined (7) - value: b'0220'
2025-06-10 00:31:37,697 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DateTimeOriginal (36867) - type: string (2) Tag Location: 276 - Data Location: 576 - value: b'2025:03:08 16:05:29\x00'
2025-06-10 00:31:37,697 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DateTimeDigitized (36868) - type: string (2) Tag Location: 288 - Data Location: 596 - value: b'2025:03:08 16:05:29\x00'
2025-06-10 00:31:37,697 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: unknown (36880) - type: string (2) Tag Location: 300 - Data Location: 616 - value: b'+01:00\x00'
2025-06-10 00:31:37,697 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: unknown (36881) - type: string (2) Tag Location: 312 - Data Location: 624 - value: b'+01:00\x00'
2025-06-10 00:31:37,697 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ShutterSpeedValue (37377) - type: rational (5) Tag Location: 324 - Data Location: 632 - value: b'\x01\x00\x00\x00!\x00\x00\x00'
2025-06-10 00:31:37,697 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ApertureValue (37378) - type: rational (5) Tag Location: 336 - Data Location: 640 - value: b'\xb9\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:31:37,697 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: BrightnessValue (37379) - type: signed rational (10) Tag Location: 348 - Data Location: 648 - value: b'b\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:31:37,697 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureBiasValue (37380) - type: signed rational (10) Tag Location: 360 - Data Location: 656 - value: b'\x00\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:31:37,697 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: MaxApertureValue (37381) - type: rational (5) Tag Location: 372 - Data Location: 664 - value: b'\xb9\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:31:37,698 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: MeteringMode (37383) - type: short (3) - value: b'\x02\x00'
2025-06-10 00:31:37,698 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Flash (37385) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:31:37,698 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: FocalLength (37386) - type: rational (5) Tag Location: 408 - Data Location: 672 - value: b'#\x01\x00\x00d\x00\x00\x00'
2025-06-10 00:31:37,698 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SubSec (37520) - type: string (2) - value: b'970\x00'
2025-06-10 00:31:37,698 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SubSecTimeOriginal (37521) - type: string (2) - value: b'970\x00'
2025-06-10 00:31:37,698 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SubsecTimeDigitized (37522) - type: string (2) - value: b'970\x00'
2025-06-10 00:31:37,698 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ColorSpace (40961) - type: short (3) - value: b'\x01\x00'
2025-06-10 00:31:37,698 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: PixelXDimension (40962) - type: long (4) - value: b'\xc0\x0c\x00\x00'
2025-06-10 00:31:37,698 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: PixelYDimension (40963) - type: long (4) - value: b'\xa4\x07\x00\x00'
2025-06-10 00:31:37,698 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureMode (41986) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:31:37,698 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: WhiteBalance (41987) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:31:37,698 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DigitalZoomRatio (41988) - type: rational (5) Tag Location: 516 - Data Location: 680 - value: b'd\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:31:37,699 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: FocalLengthIn35mmFilm (41989) - type: short (3) - value: b'\x1b\x00'
2025-06-10 00:31:37,699 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SceneCaptureType (41990) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:31:37,699 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ImageUniqueID (42016) - type: string (2) Tag Location: 552 - Data Location: 688 - value: b'G08LLMJ00HM\x00'
2025-06-10 00:31:37,699 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureTime (33434) - type: rational (5) Tag Location: 216 - Data Location: 560 - value: b'\x01\x00\x00\x00!\x00\x00\x00'
2025-06-10 00:31:37,699 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: FNumber (33437) - type: rational (5) Tag Location: 228 - Data Location: 568 - value: b'\xbe\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:31:37,699 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureProgram (34850) - type: short (3) - value: b'\x02\x00'
2025-06-10 00:31:37,699 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ISOSpeedRatings (34855) - type: short (3) - value: b'\xa0\x00'
2025-06-10 00:31:37,700 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExifVersion (36864) - type: undefined (7) - value: b'0220'
2025-06-10 00:31:37,700 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DateTimeOriginal (36867) - type: string (2) Tag Location: 276 - Data Location: 576 - value: b'2025:03:08 16:05:29\x00'
2025-06-10 00:31:37,700 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DateTimeDigitized (36868) - type: string (2) Tag Location: 288 - Data Location: 596 - value: b'2025:03:08 16:05:29\x00'
2025-06-10 00:31:37,700 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: unknown (36880) - type: string (2) Tag Location: 300 - Data Location: 616 - value: b'+01:00\x00'
2025-06-10 00:31:37,700 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: unknown (36881) - type: string (2) Tag Location: 312 - Data Location: 624 - value: b'+01:00\x00'
2025-06-10 00:31:37,700 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ShutterSpeedValue (37377) - type: rational (5) Tag Location: 324 - Data Location: 632 - value: b'\x01\x00\x00\x00!\x00\x00\x00'
2025-06-10 00:31:37,700 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ApertureValue (37378) - type: rational (5) Tag Location: 336 - Data Location: 640 - value: b'\xb9\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:31:37,700 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: BrightnessValue (37379) - type: signed rational (10) Tag Location: 348 - Data Location: 648 - value: b'b\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:31:37,701 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureBiasValue (37380) - type: signed rational (10) Tag Location: 360 - Data Location: 656 - value: b'\x00\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:31:37,701 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: MaxApertureValue (37381) - type: rational (5) Tag Location: 372 - Data Location: 664 - value: b'\xb9\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:31:37,701 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: MeteringMode (37383) - type: short (3) - value: b'\x02\x00'
2025-06-10 00:31:37,701 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Flash (37385) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:31:37,702 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: FocalLength (37386) - type: rational (5) Tag Location: 408 - Data Location: 672 - value: b'#\x01\x00\x00d\x00\x00\x00'
2025-06-10 00:31:37,702 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SubSec (37520) - type: string (2) - value: b'970\x00'
2025-06-10 00:31:37,702 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SubSecTimeOriginal (37521) - type: string (2) - value: b'970\x00'
2025-06-10 00:31:37,702 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SubsecTimeDigitized (37522) - type: string (2) - value: b'970\x00'
2025-06-10 00:31:37,703 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ColorSpace (40961) - type: short (3) - value: b'\x01\x00'
2025-06-10 00:31:37,703 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: PixelXDimension (40962) - type: long (4) - value: b'\xc0\x0c\x00\x00'
2025-06-10 00:31:37,703 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: PixelYDimension (40963) - type: long (4) - value: b'\xa4\x07\x00\x00'
2025-06-10 00:31:37,703 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureMode (41986) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:31:37,703 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: WhiteBalance (41987) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:31:37,704 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DigitalZoomRatio (41988) - type: rational (5) Tag Location: 516 - Data Location: 680 - value: b'd\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:31:37,704 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: FocalLengthIn35mmFilm (41989) - type: short (3) - value: b'\x1b\x00'
2025-06-10 00:31:37,704 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SceneCaptureType (41990) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:31:37,704 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ImageUniqueID (42016) - type: string (2) Tag Location: 552 - Data Location: 688 - value: b'G08LLMJ00HM\x00'
2025-06-10 00:31:37,707 | DEBUG    | PhotoDrop.MetadataController | load_metadata:42 | Metadati EXIF estratti: {'Marca': 'samsung', 'Modello': 'SM-P615', 'Data e ora': '08/03/2025 16:05:29', 'Compensazione esposizione': '0.0', 'Modalità di misurazione': '2', 'Flash': '0', 'Lunghezza focale': '2.91', 'Tempo di esposizione': '0.030303030303030304 sec', 'Diaframma': 'f/1.9', 'ISO': '160', 'Bilanciamento del bianco': '0', 'Dimensione file': '193.3 KB', 'Nome file': 'ph_3.jpg'}
2025-06-10 00:31:37,708 | INFO     | PhotoDrop.MainWindow | _on_selection_changed:258 | MainWindow: _on_selection_changed ha chiamato right_panel.show_image con ph_3.jpg
2025-06-10 00:31:37,708 | INFO     | PhotoDrop.ImageListController | select_image:51 | ImageListController: selezionata immagine 3/6
2025-06-10 00:31:40,316 | INFO     | PhotoDrop.MainWindow | _handle_navigation_change:419 | Navigazione cambiata: edit
2025-06-10 00:31:40,317 | INFO     | PhotoDrop.MainWindow | _show_edit_mode:495 | Modalità modifica attivata
2025-06-10 00:31:40,317 | DEBUG    | PhotoDrop.ModernTopbar | set_active_section:331 | Sezione attiva cambiata: edit
2025-06-10 00:31:44,829 | INFO     | PhotoDrop.MainWindow | _handle_topbar_action:385 | Azione topbar ricevuta: crop
2025-06-10 00:31:44,833 | INFO     | PhotoDrop.CropWidget | __init__:60 | CropSelectionLabel inizializzato
2025-06-10 00:31:44,834 | INFO     | PhotoDrop.CropWidget | __init__:455 | CropWidget inizializzato
2025-06-10 00:31:44,836 | DEBUG    | PhotoDrop.CropWidget | set_image:79 | Immagine impostata: PySide6.QtCore.QSize(640, 373)
2025-06-10 00:31:44,837 | INFO     | PhotoDrop.CropWidget | set_image:549 | Immagine impostata per il ritaglio: ph_3.jpg
2025-06-10 00:31:44,837 | INFO     | PhotoDrop.CropDialog | __init__:90 | CropDialog inizializzato per: ph_3.jpg
2025-06-10 00:31:50,904 | INFO     | PhotoDrop.CropDialog | _start_crop_operation:214 | Operazione di ritaglio avviata: (337, 218, 1283, 704)
2025-06-10 00:31:50,904 | INFO     | PhotoDrop.CropWidget | _apply_crop:600 | Ritaglio richiesto: (337, 218, 1283, 704)
2025-06-10 00:31:50,904 | INFO     | PhotoDrop.CropDialog | run:43 | Inizio ritaglio: ph_3.jpg (337, 218, 1283, 704)
2025-06-10 00:31:50,917 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: YCbCrPositioning (531) - type: short (3) - value: b'\x01\x00'
2025-06-10 00:31:50,917 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DateTime (306) - type: string (2) Tag Location: 34 - Data Location: 138 - value: b'2025:03:08 16:05:29\x00'
2025-06-10 00:31:50,917 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Make (271) - type: string (2) Tag Location: 46 - Data Location: 158 - value: b'samsung\x00'
2025-06-10 00:31:50,918 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Model (272) - type: string (2) Tag Location: 58 - Data Location: 166 - value: b'SM-P615\x00'
2025-06-10 00:31:50,918 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Orientation (274) - type: short (3) - value: b'\x01\x00'
2025-06-10 00:31:50,918 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: XResolution (282) - type: rational (5) Tag Location: 82 - Data Location: 174 - value: b'H\x00\x00\x00\x01\x00\x00\x00'
2025-06-10 00:31:50,918 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: YResolution (283) - type: rational (5) Tag Location: 94 - Data Location: 182 - value: b'H\x00\x00\x00\x01\x00\x00\x00'
2025-06-10 00:31:50,918 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ResolutionUnit (296) - type: short (3) - value: b'\x02\x00'
2025-06-10 00:31:50,919 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Software (305) - type: string (2) Tag Location: 118 - Data Location: 190 - value: b'ImageMeter\x00'
2025-06-10 00:31:50,919 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExifIFD (34665) - type: long (4) - value: b'\xca\x00\x00\x00'
2025-06-10 00:31:50,933 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: YCbCrPositioning (531) - type: short (3) - value: b'\x01\x00'
2025-06-10 00:31:50,933 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DateTime (306) - type: string (2) Tag Location: 34 - Data Location: 138 - value: b'2025:03:08 16:05:29\x00'
2025-06-10 00:31:50,934 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Make (271) - type: string (2) Tag Location: 46 - Data Location: 158 - value: b'samsung\x00'
2025-06-10 00:31:50,934 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Model (272) - type: string (2) Tag Location: 58 - Data Location: 166 - value: b'SM-P615\x00'
2025-06-10 00:31:50,934 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Orientation (274) - type: short (3) - value: b'\x01\x00'
2025-06-10 00:31:50,934 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: XResolution (282) - type: rational (5) Tag Location: 82 - Data Location: 174 - value: b'H\x00\x00\x00\x01\x00\x00\x00'
2025-06-10 00:31:50,934 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: YResolution (283) - type: rational (5) Tag Location: 94 - Data Location: 182 - value: b'H\x00\x00\x00\x01\x00\x00\x00'
2025-06-10 00:31:50,935 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ResolutionUnit (296) - type: short (3) - value: b'\x02\x00'
2025-06-10 00:31:50,935 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Software (305) - type: string (2) Tag Location: 118 - Data Location: 190 - value: b'ImageMeter\x00'
2025-06-10 00:31:50,935 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExifIFD (34665) - type: long (4) - value: b'\xca\x00\x00\x00'
2025-06-10 00:31:50,936 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: YCbCrPositioning (531) - type: short (3) - value: b'\x01\x00'
2025-06-10 00:31:50,936 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DateTime (306) - type: string (2) Tag Location: 34 - Data Location: 138 - value: b'2025:03:08 16:05:29\x00'
2025-06-10 00:31:50,936 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Make (271) - type: string (2) Tag Location: 46 - Data Location: 158 - value: b'samsung\x00'
2025-06-10 00:31:50,937 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Model (272) - type: string (2) Tag Location: 58 - Data Location: 166 - value: b'SM-P615\x00'
2025-06-10 00:31:50,937 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Orientation (274) - type: short (3) - value: b'\x01\x00'
2025-06-10 00:31:50,937 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: XResolution (282) - type: rational (5) Tag Location: 82 - Data Location: 174 - value: b'H\x00\x00\x00\x01\x00\x00\x00'
2025-06-10 00:31:50,937 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: YResolution (283) - type: rational (5) Tag Location: 94 - Data Location: 182 - value: b'H\x00\x00\x00\x01\x00\x00\x00'
2025-06-10 00:31:50,937 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ResolutionUnit (296) - type: short (3) - value: b'\x02\x00'
2025-06-10 00:31:50,937 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Software (305) - type: string (2) Tag Location: 118 - Data Location: 190 - value: b'ImageMeter\x00'
2025-06-10 00:31:50,937 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExifIFD (34665) - type: long (4) - value: b'\xca\x00\x00\x00'
2025-06-10 00:31:50,938 | INFO     | PhotoDrop.CropDialog | run:56 | Ritaglio completato: ph_3.jpg
2025-06-10 00:31:50,947 | INFO     | PhotoDrop.MetadataController | load_metadata:35 | Estrazione metadati EXIF per: C:\Users\<USER>\Desktop\foto2\ph_3.jpg
2025-06-10 00:31:50,947 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: YCbCrPositioning (531) - type: short (3) - value: b'\x01\x00'
2025-06-10 00:31:50,948 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DateTime (306) - type: string (2) Tag Location: 34 - Data Location: 138 - value: b'2025:03:08 16:05:29\x00'
2025-06-10 00:31:50,948 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Make (271) - type: string (2) Tag Location: 46 - Data Location: 158 - value: b'samsung\x00'
2025-06-10 00:31:50,949 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Model (272) - type: string (2) Tag Location: 58 - Data Location: 166 - value: b'SM-P615\x00'
2025-06-10 00:31:50,949 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Orientation (274) - type: short (3) - value: b'\x01\x00'
2025-06-10 00:31:50,950 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: XResolution (282) - type: rational (5) Tag Location: 82 - Data Location: 174 - value: b'H\x00\x00\x00\x01\x00\x00\x00'
2025-06-10 00:31:50,950 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: YResolution (283) - type: rational (5) Tag Location: 94 - Data Location: 182 - value: b'H\x00\x00\x00\x01\x00\x00\x00'
2025-06-10 00:31:50,951 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ResolutionUnit (296) - type: short (3) - value: b'\x02\x00'
2025-06-10 00:31:50,951 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Software (305) - type: string (2) Tag Location: 118 - Data Location: 190 - value: b'ImageMeter\x00'
2025-06-10 00:31:50,951 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExifIFD (34665) - type: long (4) - value: b'\xca\x00\x00\x00'
2025-06-10 00:31:50,952 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureTime (33434) - type: rational (5) Tag Location: 216 - Data Location: 560 - value: b'\x01\x00\x00\x00!\x00\x00\x00'
2025-06-10 00:31:50,952 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: FNumber (33437) - type: rational (5) Tag Location: 228 - Data Location: 568 - value: b'\xbe\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:31:50,952 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureProgram (34850) - type: short (3) - value: b'\x02\x00'
2025-06-10 00:31:50,952 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ISOSpeedRatings (34855) - type: short (3) - value: b'\xa0\x00'
2025-06-10 00:31:50,953 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExifVersion (36864) - type: undefined (7) - value: b'0220'
2025-06-10 00:31:50,953 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DateTimeOriginal (36867) - type: string (2) Tag Location: 276 - Data Location: 576 - value: b'2025:03:08 16:05:29\x00'
2025-06-10 00:31:50,953 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DateTimeDigitized (36868) - type: string (2) Tag Location: 288 - Data Location: 596 - value: b'2025:03:08 16:05:29\x00'
2025-06-10 00:31:50,953 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: unknown (36880) - type: string (2) Tag Location: 300 - Data Location: 616 - value: b'+01:00\x00'
2025-06-10 00:31:50,953 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: unknown (36881) - type: string (2) Tag Location: 312 - Data Location: 624 - value: b'+01:00\x00'
2025-06-10 00:31:50,953 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ShutterSpeedValue (37377) - type: rational (5) Tag Location: 324 - Data Location: 632 - value: b'\x01\x00\x00\x00!\x00\x00\x00'
2025-06-10 00:31:50,953 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ApertureValue (37378) - type: rational (5) Tag Location: 336 - Data Location: 640 - value: b'\xb9\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:31:50,954 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: BrightnessValue (37379) - type: signed rational (10) Tag Location: 348 - Data Location: 648 - value: b'b\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:31:50,954 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureBiasValue (37380) - type: signed rational (10) Tag Location: 360 - Data Location: 656 - value: b'\x00\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:31:50,954 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: MaxApertureValue (37381) - type: rational (5) Tag Location: 372 - Data Location: 664 - value: b'\xb9\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:31:50,954 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: MeteringMode (37383) - type: short (3) - value: b'\x02\x00'
2025-06-10 00:31:50,954 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Flash (37385) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:31:50,955 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: FocalLength (37386) - type: rational (5) Tag Location: 408 - Data Location: 672 - value: b'#\x01\x00\x00d\x00\x00\x00'
2025-06-10 00:31:50,955 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SubSec (37520) - type: string (2) - value: b'970\x00'
2025-06-10 00:31:50,955 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SubSecTimeOriginal (37521) - type: string (2) - value: b'970\x00'
2025-06-10 00:31:50,955 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SubsecTimeDigitized (37522) - type: string (2) - value: b'970\x00'
2025-06-10 00:31:50,956 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ColorSpace (40961) - type: short (3) - value: b'\x01\x00'
2025-06-10 00:31:50,956 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: PixelXDimension (40962) - type: long (4) - value: b'\xc0\x0c\x00\x00'
2025-06-10 00:31:50,956 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: PixelYDimension (40963) - type: long (4) - value: b'\xa4\x07\x00\x00'
2025-06-10 00:31:50,956 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureMode (41986) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:31:50,956 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: WhiteBalance (41987) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:31:50,956 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DigitalZoomRatio (41988) - type: rational (5) Tag Location: 516 - Data Location: 680 - value: b'd\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:31:50,956 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: FocalLengthIn35mmFilm (41989) - type: short (3) - value: b'\x1b\x00'
2025-06-10 00:31:50,957 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SceneCaptureType (41990) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:31:50,957 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ImageUniqueID (42016) - type: string (2) Tag Location: 552 - Data Location: 688 - value: b'G08LLMJ00HM\x00'
2025-06-10 00:31:50,957 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureTime (33434) - type: rational (5) Tag Location: 216 - Data Location: 560 - value: b'\x01\x00\x00\x00!\x00\x00\x00'
2025-06-10 00:31:50,957 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: FNumber (33437) - type: rational (5) Tag Location: 228 - Data Location: 568 - value: b'\xbe\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:31:50,958 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureProgram (34850) - type: short (3) - value: b'\x02\x00'
2025-06-10 00:31:50,958 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ISOSpeedRatings (34855) - type: short (3) - value: b'\xa0\x00'
2025-06-10 00:31:50,958 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExifVersion (36864) - type: undefined (7) - value: b'0220'
2025-06-10 00:31:50,958 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DateTimeOriginal (36867) - type: string (2) Tag Location: 276 - Data Location: 576 - value: b'2025:03:08 16:05:29\x00'
2025-06-10 00:31:50,959 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DateTimeDigitized (36868) - type: string (2) Tag Location: 288 - Data Location: 596 - value: b'2025:03:08 16:05:29\x00'
2025-06-10 00:31:50,959 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: unknown (36880) - type: string (2) Tag Location: 300 - Data Location: 616 - value: b'+01:00\x00'
2025-06-10 00:31:50,959 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: unknown (36881) - type: string (2) Tag Location: 312 - Data Location: 624 - value: b'+01:00\x00'
2025-06-10 00:31:50,959 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ShutterSpeedValue (37377) - type: rational (5) Tag Location: 324 - Data Location: 632 - value: b'\x01\x00\x00\x00!\x00\x00\x00'
2025-06-10 00:31:50,960 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ApertureValue (37378) - type: rational (5) Tag Location: 336 - Data Location: 640 - value: b'\xb9\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:31:50,960 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: BrightnessValue (37379) - type: signed rational (10) Tag Location: 348 - Data Location: 648 - value: b'b\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:31:50,960 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureBiasValue (37380) - type: signed rational (10) Tag Location: 360 - Data Location: 656 - value: b'\x00\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:31:50,960 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: MaxApertureValue (37381) - type: rational (5) Tag Location: 372 - Data Location: 664 - value: b'\xb9\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:31:50,960 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: MeteringMode (37383) - type: short (3) - value: b'\x02\x00'
2025-06-10 00:31:50,960 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Flash (37385) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:31:50,960 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: FocalLength (37386) - type: rational (5) Tag Location: 408 - Data Location: 672 - value: b'#\x01\x00\x00d\x00\x00\x00'
2025-06-10 00:31:50,961 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SubSec (37520) - type: string (2) - value: b'970\x00'
2025-06-10 00:31:50,961 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SubSecTimeOriginal (37521) - type: string (2) - value: b'970\x00'
2025-06-10 00:31:50,961 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SubsecTimeDigitized (37522) - type: string (2) - value: b'970\x00'
2025-06-10 00:31:50,961 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ColorSpace (40961) - type: short (3) - value: b'\x01\x00'
2025-06-10 00:31:50,961 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: PixelXDimension (40962) - type: long (4) - value: b'\xc0\x0c\x00\x00'
2025-06-10 00:31:50,961 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: PixelYDimension (40963) - type: long (4) - value: b'\xa4\x07\x00\x00'
2025-06-10 00:31:50,963 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureMode (41986) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:31:50,963 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: WhiteBalance (41987) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:31:50,963 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DigitalZoomRatio (41988) - type: rational (5) Tag Location: 516 - Data Location: 680 - value: b'd\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:31:50,964 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: FocalLengthIn35mmFilm (41989) - type: short (3) - value: b'\x1b\x00'
2025-06-10 00:31:50,964 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SceneCaptureType (41990) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:31:50,964 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ImageUniqueID (42016) - type: string (2) Tag Location: 552 - Data Location: 688 - value: b'G08LLMJ00HM\x00'
2025-06-10 00:31:50,970 | DEBUG    | PhotoDrop.MetadataController | load_metadata:42 | Metadati EXIF estratti: {'Marca': 'samsung', 'Modello': 'SM-P615', 'Data e ora': '08/03/2025 16:05:29', 'Compensazione esposizione': '0.0', 'Modalità di misurazione': '2', 'Flash': '0', 'Lunghezza focale': '2.91', 'Tempo di esposizione': '0.030303030303030304 sec', 'Diaframma': 'f/1.9', 'ISO': '160', 'Bilanciamento del bianco': '0', 'Dimensione file': '159.7 KB', 'Nome file': 'ph_3.jpg'}
2025-06-10 00:31:50,971 | INFO     | PhotoRenamer.LeftPanel | update_thumbnail_for_path:905 | Richiesta di aggiornamento thumbnail per: C:/Users/<USER>/Desktop/foto2\ph_3.jpg
2025-06-10 00:31:50,971 | DEBUG    | PhotoRenamer.LeftPanel | update_thumbnail_for_path:910 | Percorso normalizzato target per l'aggiornamento: C:/Users/<USER>/Desktop/foto2/ph_3.jpg
2025-06-10 00:31:50,972 | DEBUG    | PhotoRenamer.LeftPanel | update_thumbnail_for_path:932 | Controllo item 0: 'ph_1.jpg', path normalizzato memorizzato: 'C:/Users/<USER>/Desktop/foto2/ph_1.jpg'
2025-06-10 00:31:50,972 | DEBUG    | PhotoRenamer.LeftPanel | update_thumbnail_for_path:932 | Controllo item 1: 'ph_2.jpg', path normalizzato memorizzato: 'C:/Users/<USER>/Desktop/foto2/ph_2.jpg'
2025-06-10 00:31:50,972 | DEBUG    | PhotoRenamer.LeftPanel | update_thumbnail_for_path:932 | Controllo item 2: 'ph_3.jpg', path normalizzato memorizzato: 'C:/Users/<USER>/Desktop/foto2/ph_3.jpg'
2025-06-10 00:31:50,973 | INFO     | PhotoRenamer.LeftPanel | update_thumbnail_for_path:935 | Trovato item corrispondente per l'aggiornamento thumbnail: 'ph_3.jpg' (path: C:/Users/<USER>/Desktop/foto2/ph_3.jpg)
2025-06-10 00:31:50,973 | INFO     | PhotoRenamer.LeftPanel | update_thumbnail_for_path:960 | Avvio ThumbnailWorker per aggiornare il thumbnail di: C:/Users/<USER>/Desktop/foto2/ph_3.jpg
2025-06-10 00:31:50,973 | DEBUG    | PhotoRenamer.LeftPanel | update_thumbnail_for_path:976 | ThumbnailWorker per 'C:/Users/<USER>/Desktop/foto2/ph_3.jpg' avviato.
2025-06-10 00:31:50,973 | INFO     | PhotoDrop.RightPanel | _on_image_cropped:277 | Immagine ritagliata e segnale image_updated emesso per: C:/Users/<USER>/Desktop/foto2\ph_3.jpg
2025-06-10 00:31:50,974 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: YCbCrPositioning (531) - type: short (3) - value: b'\x01\x00'
2025-06-10 00:31:50,974 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DateTime (306) - type: string (2) Tag Location: 34 - Data Location: 138 - value: b'2025:03:08 16:05:29\x00'
2025-06-10 00:31:50,975 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Make (271) - type: string (2) Tag Location: 46 - Data Location: 158 - value: b'samsung\x00'
2025-06-10 00:31:50,975 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Model (272) - type: string (2) Tag Location: 58 - Data Location: 166 - value: b'SM-P615\x00'
2025-06-10 00:31:50,975 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Orientation (274) - type: short (3) - value: b'\x01\x00'
2025-06-10 00:31:50,976 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: XResolution (282) - type: rational (5) Tag Location: 82 - Data Location: 174 - value: b'H\x00\x00\x00\x01\x00\x00\x00'
2025-06-10 00:31:50,976 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: YResolution (283) - type: rational (5) Tag Location: 94 - Data Location: 182 - value: b'H\x00\x00\x00\x01\x00\x00\x00'
2025-06-10 00:31:50,976 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ResolutionUnit (296) - type: short (3) - value: b'\x02\x00'
2025-06-10 00:31:50,976 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Software (305) - type: string (2) Tag Location: 118 - Data Location: 190 - value: b'ImageMeter\x00'
2025-06-10 00:31:50,977 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExifIFD (34665) - type: long (4) - value: b'\xca\x00\x00\x00'
2025-06-10 00:31:50,977 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureTime (33434) - type: rational (5) Tag Location: 216 - Data Location: 560 - value: b'\x01\x00\x00\x00!\x00\x00\x00'
2025-06-10 00:31:50,977 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: FNumber (33437) - type: rational (5) Tag Location: 228 - Data Location: 568 - value: b'\xbe\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:31:50,978 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureProgram (34850) - type: short (3) - value: b'\x02\x00'
2025-06-10 00:31:50,978 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ISOSpeedRatings (34855) - type: short (3) - value: b'\xa0\x00'
2025-06-10 00:31:50,978 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExifVersion (36864) - type: undefined (7) - value: b'0220'
2025-06-10 00:31:50,979 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DateTimeOriginal (36867) - type: string (2) Tag Location: 276 - Data Location: 576 - value: b'2025:03:08 16:05:29\x00'
2025-06-10 00:31:50,979 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DateTimeDigitized (36868) - type: string (2) Tag Location: 288 - Data Location: 596 - value: b'2025:03:08 16:05:29\x00'
2025-06-10 00:31:50,979 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: unknown (36880) - type: string (2) Tag Location: 300 - Data Location: 616 - value: b'+01:00\x00'
2025-06-10 00:31:50,979 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: unknown (36881) - type: string (2) Tag Location: 312 - Data Location: 624 - value: b'+01:00\x00'
2025-06-10 00:31:50,980 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ShutterSpeedValue (37377) - type: rational (5) Tag Location: 324 - Data Location: 632 - value: b'\x01\x00\x00\x00!\x00\x00\x00'
2025-06-10 00:31:50,980 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ApertureValue (37378) - type: rational (5) Tag Location: 336 - Data Location: 640 - value: b'\xb9\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:31:50,980 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: BrightnessValue (37379) - type: signed rational (10) Tag Location: 348 - Data Location: 648 - value: b'b\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:31:50,980 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureBiasValue (37380) - type: signed rational (10) Tag Location: 360 - Data Location: 656 - value: b'\x00\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:31:50,981 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: MaxApertureValue (37381) - type: rational (5) Tag Location: 372 - Data Location: 664 - value: b'\xb9\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:31:50,981 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: MeteringMode (37383) - type: short (3) - value: b'\x02\x00'
2025-06-10 00:31:50,981 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: Flash (37385) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:31:50,982 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: FocalLength (37386) - type: rational (5) Tag Location: 408 - Data Location: 672 - value: b'#\x01\x00\x00d\x00\x00\x00'
2025-06-10 00:31:50,982 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SubSec (37520) - type: string (2) - value: b'970\x00'
2025-06-10 00:31:50,982 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SubSecTimeOriginal (37521) - type: string (2) - value: b'970\x00'
2025-06-10 00:31:50,983 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SubsecTimeDigitized (37522) - type: string (2) - value: b'970\x00'
2025-06-10 00:31:50,983 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ColorSpace (40961) - type: short (3) - value: b'\x01\x00'
2025-06-10 00:31:50,984 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: PixelXDimension (40962) - type: long (4) - value: b'\xc0\x0c\x00\x00'
2025-06-10 00:31:50,984 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: PixelYDimension (40963) - type: long (4) - value: b'\xa4\x07\x00\x00'
2025-06-10 00:31:50,984 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ExposureMode (41986) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:31:50,985 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: WhiteBalance (41987) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:31:50,985 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: DigitalZoomRatio (41988) - type: rational (5) Tag Location: 516 - Data Location: 680 - value: b'd\x00\x00\x00d\x00\x00\x00'
2025-06-10 00:31:50,985 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: FocalLengthIn35mmFilm (41989) - type: short (3) - value: b'\x1b\x00'
2025-06-10 00:31:50,985 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: SceneCaptureType (41990) - type: short (3) - value: b'\x00\x00'
2025-06-10 00:31:50,986 | DEBUG    | PIL.TiffImagePlugin | load:942 | tag: ImageUniqueID (42016) - type: string (2) Tag Location: 552 - Data Location: 688 - value: b'G08LLMJ00HM\x00'
2025-06-10 00:31:50,989 | INFO     | PhotoRenamer.LeftPanel | _on_specific_thumbnail_ready:949 | Thumbnail aggiornato per 'C:/Users/<USER>/Desktop/foto2/ph_3.jpg' pronto. Applicazione all'item: 'ph_3.jpg'
2025-06-10 00:31:50,993 | DEBUG    | PhotoRenamer.LeftPanel | _on_specific_thumbnail_ready:953 | Richiesto repaint per l'item 'ph_3.jpg' dopo aggiornamento thumbnail.
