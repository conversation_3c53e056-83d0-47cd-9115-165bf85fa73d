2025-06-10 00:08:25,648 | INFO     | PhotoDrop | <module>:25 | ================================================================================
2025-06-10 00:08:25,648 | INFO     | PhotoDrop | <module>:26 | Nuova sessione avviata il 2025-06-10 00:08:25
2025-06-10 00:08:25,648 | INFO     | PhotoDrop | <module>:27 | ================================================================================
2025-06-10 00:08:25,649 | INFO     | PhotoRenamer | <module>:34 | =================================================================
2025-06-10 00:08:25,649 | INFO     | PhotoRenamer | <module>:35 | Avvio PhotoDrop Applicazione
2025-06-10 00:08:25,649 | INFO     | PhotoRenamer | <module>:36 | =================================================================
2025-06-10 00:08:25,703 | INFO     | PhotoDrop.SettingsModel | _load_settings:112 | File impostazioni non trovato, uso impostazioni predefinite
2025-06-10 00:08:25,704 | INFO     | PhotoDrop.SettingsModel | __init__:93 | SettingsModel inizializzato con file: c:\xampp\htdocs\progetti\photodrop\config\settings.json
2025-06-10 00:08:25,704 | INFO     | core.image_cache | __init__:338 | Cache su disco inizializzata in cache/images (max 500MB, scadenza 30 giorni)
2025-06-10 00:08:25,704 | INFO     | core.image_cache | __init__:342 | Cache immagini inizializzata (max in memoria: 1000 elementi)
2025-06-10 00:08:25,709 | INFO     | PhotoRenamer.LeftPanel | __init__:378 | LeftPanel inizializzato - test logger
2025-06-10 00:08:25,709 | DEBUG    | PhotoRenamer.LeftPanel | _load_last_folder_if_enabled:1019 | Nessuna ultima cartella valida trovata
2025-06-10 00:08:25,710 | INFO     | PhotoRenamer.LeftPanel | __init__:46 | [DND] DraggableListWidget inizializzato
2025-06-10 00:08:25,710 | INFO     | PhotoRenamer.LeftPanel | __init__:55 | [DND] DraggableListWidget configurato con gestione manuale
2025-06-10 00:08:25,710 | INFO     | PhotoRenamer.LeftPanel | _setup_ui:436 | [DND] LeftPanel: creata istanza DraggableListWidget
2025-06-10 00:08:25,711 | DEBUG    | PhotoRenamer.LeftPanel | _setup_list_widget:585 | [LP.__init__] Connesso segnale currentItemChanged a _on_current_item_changed
2025-06-10 00:08:25,739 | INFO     | PhotoDrop.MainWindow | _connect_signals:247 | [MW._connect_signals] Connesso left_panel.selection_changed a _on_selection_changed
2025-06-10 00:08:26,075 | INFO     | PhotoDrop.MainWindow | __init__:126 | Applicazione inizializzata
2025-06-10 00:08:29,228 | DEBUG    | MARKDOWN | registerExtensions:182 | Successfully loaded extension "markdown.extensions.toc.TocExtension".
2025-06-10 00:08:29,237 | DEBUG    | MARKDOWN | registerExtensions:182 | Successfully loaded extension "markdown.extensions.fenced_code.FencedCodeExtension".
2025-06-10 00:08:33,468 | INFO     | core.image_cache | clear:447 | Cache immagini svuotata
2025-06-10 00:08:33,469 | INFO     | PhotoDrop.MainWindow | closeEvent:671 | Applicazione terminata
